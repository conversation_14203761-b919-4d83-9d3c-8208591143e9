import {
  computed,
  defineComponent,
  h,
  inject,
  markRaw,
  nextTick,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  provide,
  reactive,
  ref,
  render,
  watch
} from "./chunk-CPDHMQQS.js";
import "./chunk-JVWSFFO4.js";

// node_modules/@vue-leaflet/vue-leaflet/dist/vue-leaflet.es.js
var ce = (e, o) => {
  for (const t of Object.keys(o))
    e.on(t, o[t]);
};
var ye = (e) => {
  for (const o of Object.keys(e)) {
    const t = e[o];
    t && k(t.cancel) && t.cancel();
  }
};
var Je = (e) => !e || typeof e.charAt != "function" ? e : e.charAt(0).toUpperCase() + e.slice(1);
var k = (e) => typeof e == "function";
var L = (e, o, t) => {
  for (const n in t) {
    const s = "set" + Je(n);
    e[s] ? watch(
      () => t[n],
      (r, l) => {
        e[s](r, l);
      }
    ) : o[s] && watch(
      () => t[n],
      (r) => {
        o[s](r);
      }
    );
  }
};
var f = (e, o, t = {}) => {
  const n = { ...t };
  for (const s in e) {
    const r = o[s], l = e[s];
    r && (r && r.custom === true || l !== void 0 && (n[s] = l));
  }
  return n;
};
var T = (e) => {
  const o = {}, t = {};
  for (const n in e)
    if (n.startsWith("on") && !n.startsWith("onUpdate") && n !== "onReady") {
      const s = n.slice(2).toLocaleLowerCase();
      o[s] = e[n];
    } else
      t[n] = e[n];
  return { listeners: o, attrs: t };
};
var qe = async (e) => {
  const o = await Promise.all([
    import("C:/Bagide/Clients/Perum/simperum2/client/node_modules/leaflet/dist/images/marker-icon-2x.png"),
    import("C:/Bagide/Clients/Perum/simperum2/client/node_modules/leaflet/dist/images/marker-icon.png"),
    import("C:/Bagide/Clients/Perum/simperum2/client/node_modules/leaflet/dist/images/marker-shadow.png")
  ]);
  delete e.Default.prototype._getIconUrl, e.Default.mergeOptions({
    iconRetinaUrl: o[0].default,
    iconUrl: o[1].default,
    shadowUrl: o[2].default
  });
};
var Y = (e) => {
  const o = ref(
    (...n) => console.warn(`Method ${e} has been invoked without being replaced`)
  ), t = (...n) => o.value(...n);
  return t.wrapped = o, provide(e, t), t;
};
var V = (e, o) => e.wrapped.value = o;
var b = typeof self == "object" && self.self === self && self || typeof global == "object" && global.global === global && global || globalThis;
var m = (e) => {
  const o = inject(e);
  if (o === void 0)
    throw new Error(
      `Attempt to inject ${e.description} before it was provided.`
    );
  return o;
};
var Kt = Object.freeze(Object.defineProperty({
  __proto__: null,
  WINDOW_OR_GLOBAL: b,
  assertInject: m,
  bindEventHandlers: ce,
  cancelDebounces: ye,
  capitalizeFirstLetter: Je,
  isFunction: k,
  propsBinder: L,
  propsToLeafletOptions: f,
  provideLeafletWrapper: Y,
  remapEvents: T,
  resetWebpackIcon: qe,
  updateLeafletWrapper: V
}, Symbol.toStringTag, { value: "Module" }));
var h2 = Symbol(
  "useGlobalLeaflet"
);
var M = Symbol("addLayer");
var ee = Symbol("removeLayer");
var H = Symbol(
  "registerControl"
);
var me = Symbol(
  "registerLayerControl"
);
var ve = Symbol(
  "canSetParentHtml"
);
var be = Symbol("setParentHtml");
var fe = Symbol("setIcon");
var ge = Symbol("bindPopup");
var Le = Symbol("bindTooltip");
var he = Symbol("unbindPopup");
var Oe = Symbol("unbindTooltip");
var Qt = Object.freeze(Object.defineProperty({
  __proto__: null,
  AddLayerInjection: M,
  BindPopupInjection: ge,
  BindTooltipInjection: Le,
  CanSetParentHtmlInjection: ve,
  RegisterControlInjection: H,
  RegisterLayerControlInjection: me,
  RemoveLayerInjection: ee,
  SetIconInjection: fe,
  SetParentHtmlInjection: be,
  UnbindPopupInjection: he,
  UnbindTooltipInjection: Oe,
  UseGlobalLeafletInjection: h2
}, Symbol.toStringTag, { value: "Module" }));
var W = {
  options: {
    type: Object,
    default: () => ({}),
    custom: true
  }
};
var J = (e) => ({ options: e.options, methods: {} });
var bt = Object.freeze(Object.defineProperty({
  __proto__: null,
  componentProps: W,
  setupComponent: J
}, Symbol.toStringTag, { value: "Module" }));
var D = {
  ...W,
  pane: {
    type: String
  },
  attribution: {
    type: String
  },
  name: {
    type: String,
    custom: true
  },
  layerType: {
    type: String,
    custom: true
  },
  visible: {
    type: Boolean,
    custom: true,
    default: true
  }
};
var q = (e, o, t) => {
  const n = m(M), s = m(ee), { options: r, methods: l } = J(e), a = f(
    e,
    D,
    r
  ), i = () => n({ leafletObject: o.value }), u = () => s({ leafletObject: o.value }), d = {
    ...l,
    setAttribution(y) {
      u(), o.value.options.attribution = y, e.visible && i();
    },
    setName() {
      u(), e.visible && i();
    },
    setLayerType() {
      u(), e.visible && i();
    },
    setVisible(y) {
      o.value && (y ? i() : u());
    },
    bindPopup(y) {
      if (!o.value || !k(o.value.bindPopup)) {
        console.warn(
          "Attempt to bind popup before bindPopup method available on layer."
        );
        return;
      }
      o.value.bindPopup(y);
    },
    bindTooltip(y) {
      if (!o.value || !k(o.value.bindTooltip)) {
        console.warn(
          "Attempt to bind tooltip before bindTooltip method available on layer."
        );
        return;
      }
      o.value.bindTooltip(y);
    },
    unbindTooltip() {
      o.value && (k(o.value.closeTooltip) && o.value.closeTooltip(), k(o.value.unbindTooltip) && o.value.unbindTooltip());
    },
    unbindPopup() {
      o.value && (k(o.value.closePopup) && o.value.closePopup(), k(o.value.unbindPopup) && o.value.unbindPopup());
    },
    updateVisibleProp(y) {
      t.emit("update:visible", y);
    }
  };
  return provide(ge, d.bindPopup), provide(Le, d.bindTooltip), provide(he, d.unbindPopup), provide(Oe, d.unbindTooltip), onUnmounted(() => {
    d.unbindPopup(), d.unbindTooltip(), u();
  }), { options: a, methods: d };
};
var G = (e, o) => {
  if (e && o.default)
    return h("div", { style: { display: "none" } }, o.default());
};
var ft = Object.freeze(Object.defineProperty({
  __proto__: null,
  layerProps: D,
  render: G,
  setupLayer: q
}, Symbol.toStringTag, { value: "Module" }));
var Se = {
  ...D,
  interactive: {
    type: Boolean,
    default: void 0
  },
  bubblingMouseEvents: {
    type: Boolean,
    default: void 0
  }
};
var Ke = (e, o, t) => {
  const { options: n, methods: s } = q(
    e,
    o,
    t
  );
  return { options: f(
    e,
    Se,
    n
  ), methods: s };
};
var gt = Object.freeze(Object.defineProperty({
  __proto__: null,
  interactiveLayerProps: Se,
  setupInteractiveLayer: Ke
}, Symbol.toStringTag, { value: "Module" }));
var ne = {
  ...Se,
  stroke: {
    type: Boolean,
    default: void 0
  },
  color: {
    type: String
  },
  weight: {
    type: Number
  },
  opacity: {
    type: Number
  },
  lineCap: {
    type: String
  },
  lineJoin: {
    type: String
  },
  dashArray: {
    type: String
  },
  dashOffset: {
    type: String
  },
  fill: {
    type: Boolean,
    default: void 0
  },
  fillColor: {
    type: String
  },
  fillOpacity: {
    type: Number
  },
  fillRule: {
    type: String
  },
  className: {
    type: String
  }
};
var _e = (e, o, t) => {
  const { options: n, methods: s } = Ke(e, o, t), r = f(
    e,
    ne,
    n
  ), l = m(ee), a = {
    ...s,
    setStroke(i) {
      o.value.setStyle({ stroke: i });
    },
    setColor(i) {
      o.value.setStyle({ color: i });
    },
    setWeight(i) {
      o.value.setStyle({ weight: i });
    },
    setOpacity(i) {
      o.value.setStyle({ opacity: i });
    },
    setLineCap(i) {
      o.value.setStyle({ lineCap: i });
    },
    setLineJoin(i) {
      o.value.setStyle({ lineJoin: i });
    },
    setDashArray(i) {
      o.value.setStyle({ dashArray: i });
    },
    setDashOffset(i) {
      o.value.setStyle({ dashOffset: i });
    },
    setFill(i) {
      o.value.setStyle({ fill: i });
    },
    setFillColor(i) {
      o.value.setStyle({ fillColor: i });
    },
    setFillOpacity(i) {
      o.value.setStyle({ fillOpacity: i });
    },
    setFillRule(i) {
      o.value.setStyle({ fillRule: i });
    },
    setClassName(i) {
      o.value.setStyle({ className: i });
    }
  };
  return onBeforeUnmount(() => {
    l({ leafletObject: o.value });
  }), { options: r, methods: a };
};
var Lt = Object.freeze(Object.defineProperty({
  __proto__: null,
  pathProps: ne,
  setupPath: _e
}, Symbol.toStringTag, { value: "Module" }));
var re = {
  ...ne,
  /**
   * Radius of the marker in pixels.
   */
  radius: {
    type: Number
  },
  latLng: {
    type: [Object, Array],
    required: true,
    custom: true
  }
};
var je = (e, o, t) => {
  const { options: n, methods: s } = _e(
    e,
    o,
    t
  ), r = f(
    e,
    re,
    n
  ), l = {
    ...s,
    setRadius(a) {
      o.value.setRadius(a);
    },
    setLatLng(a) {
      o.value.setLatLng(a);
    }
  };
  return { options: r, methods: l };
};
var ht = Object.freeze(Object.defineProperty({
  __proto__: null,
  circleMarkerProps: re,
  setupCircleMarker: je
}, Symbol.toStringTag, { value: "Module" }));
var Pe = {
  ...re,
  /**
   * Radius of the circle in meters.
   */
  radius: {
    type: Number
  }
};
var Qe = (e, o, t) => {
  const { options: n, methods: s } = je(e, o, t), r = f(
    e,
    Pe,
    n
  ), l = {
    ...s
  };
  return { options: r, methods: l };
};
var Ot = Object.freeze(Object.defineProperty({
  __proto__: null,
  circleProps: Pe,
  setupCircle: Qe
}, Symbol.toStringTag, { value: "Module" }));
var Xt = defineComponent({
  name: "LCircle",
  props: Pe,
  setup(e, o) {
    const t = ref(), n = ref(false), s = inject(h2), r = m(M), { options: l, methods: a } = Qe(e, t, o);
    return onMounted(async () => {
      const { circle: i } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(i(e.latLng, l));
      const { listeners: u } = T(o.attrs);
      t.value.on(u), L(a, t.value, e), r({
        ...e,
        ...a,
        leafletObject: t.value
      }), n.value = true, nextTick(() => o.emit("ready", t.value));
    }), { ready: n, leafletObject: t };
  },
  render() {
    return G(this.ready, this.$slots);
  }
});
var Yt = defineComponent({
  name: "LCircleMarker",
  props: re,
  setup(e, o) {
    const t = ref(), n = ref(false), s = inject(h2), r = m(M), { options: l, methods: a } = je(
      e,
      t,
      o
    );
    return onMounted(async () => {
      const { circleMarker: i } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(
        i(e.latLng, l)
      );
      const { listeners: u } = T(o.attrs);
      t.value.on(u), L(a, t.value, e), r({
        ...e,
        ...a,
        leafletObject: t.value
      }), n.value = true, nextTick(() => o.emit("ready", t.value));
    }), { ready: n, leafletObject: t };
  },
  render() {
    return G(this.ready, this.$slots);
  }
});
var F = {
  ...W,
  position: {
    type: String
  }
};
var K = (e, o) => {
  const { options: t, methods: n } = J(e), s = f(
    e,
    F,
    t
  ), r = {
    ...n,
    setPosition(l) {
      o.value && o.value.setPosition(l);
    }
  };
  return onUnmounted(() => {
    o.value && o.value.remove();
  }), { options: s, methods: r };
};
var Xe = (e) => e.default ? h("div", { ref: "root" }, e.default()) : null;
var St = Object.freeze(Object.defineProperty({
  __proto__: null,
  controlProps: F,
  renderLControl: Xe,
  setupControl: K
}, Symbol.toStringTag, { value: "Module" }));
var Vt = defineComponent({
  name: "LControl",
  props: {
    ...F,
    disableClickPropagation: {
      type: Boolean,
      custom: true,
      default: true
    },
    disableScrollPropagation: {
      type: Boolean,
      custom: true,
      default: false
    }
  },
  setup(e, o) {
    const t = ref(), n = ref(), s = inject(h2), r = m(H), { options: l, methods: a } = K(e, t);
    return onMounted(async () => {
      const { Control: i, DomEvent: u } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js"), d = i.extend({
        onAdd() {
          return n.value;
        }
      });
      t.value = markRaw(new d(l)), L(a, t.value, e), r({ leafletObject: t.value }), e.disableClickPropagation && n.value && u.disableClickPropagation(n.value), e.disableScrollPropagation && n.value && u.disableScrollPropagation(n.value), nextTick(() => o.emit("ready", t.value));
    }), { root: n, leafletObject: t };
  },
  render() {
    return Xe(this.$slots);
  }
});
var Ce = {
  ...F,
  prefix: {
    type: String
  }
};
var Ye = (e, o) => {
  const { options: t, methods: n } = K(
    e,
    o
  ), s = f(
    e,
    Ce,
    t
  ), r = {
    ...n,
    setPrefix(l) {
      o.value.setPrefix(l);
    }
  };
  return { options: s, methods: r };
};
var _t = Object.freeze(Object.defineProperty({
  __proto__: null,
  controlAttributionProps: Ce,
  setupControlAttribution: Ye
}, Symbol.toStringTag, { value: "Module" }));
var xt = defineComponent({
  name: "LControlAttribution",
  props: Ce,
  setup(e, o) {
    const t = ref(), n = inject(h2), s = m(H), { options: r, methods: l } = Ye(e, t);
    return onMounted(async () => {
      const { control: a } = n ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(
        a.attribution(r)
      ), L(l, t.value, e), s({ leafletObject: t.value }), nextTick(() => o.emit("ready", t.value));
    }), { leafletObject: t };
  },
  render() {
    return null;
  }
});
var Te = {
  ...F,
  collapsed: {
    type: Boolean,
    default: void 0
  },
  autoZIndex: {
    type: Boolean,
    default: void 0
  },
  hideSingleBase: {
    type: Boolean,
    default: void 0
  },
  sortLayers: {
    type: Boolean,
    default: void 0
  },
  sortFunction: {
    type: Function
  }
};
var Ve = (e, o) => {
  const { options: t } = K(e, o);
  return { options: f(
    e,
    Te,
    t
  ), methods: {
    addLayer(r) {
      r.layerType === "base" ? o.value.addBaseLayer(r.leafletObject, r.name) : r.layerType === "overlay" && o.value.addOverlay(r.leafletObject, r.name);
    },
    removeLayer(r) {
      o.value.removeLayer(r.leafletObject);
    }
  } };
};
var jt = Object.freeze(Object.defineProperty({
  __proto__: null,
  controlLayersProps: Te,
  setupControlLayers: Ve
}, Symbol.toStringTag, { value: "Module" }));
var Rt = defineComponent({
  name: "LControlLayers",
  props: Te,
  setup(e, o) {
    const t = ref(), n = inject(h2), s = m(me), { options: r, methods: l } = Ve(e, t);
    return onMounted(async () => {
      const { control: a } = n ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(
        a.layers(void 0, void 0, r)
      ), L(l, t.value, e), s({
        ...e,
        ...l,
        leafletObject: t.value
      }), nextTick(() => o.emit("ready", t.value));
    }), { leafletObject: t };
  },
  render() {
    return null;
  }
});
var Me = {
  ...F,
  maxWidth: {
    type: Number
  },
  metric: {
    type: Boolean,
    default: void 0
  },
  imperial: {
    type: Boolean,
    default: void 0
  },
  updateWhenIdle: {
    type: Boolean,
    default: void 0
  }
};
var xe = (e, o) => {
  const { options: t, methods: n } = K(
    e,
    o
  );
  return { options: f(
    e,
    Me,
    t
  ), methods: n };
};
var Pt = Object.freeze(Object.defineProperty({
  __proto__: null,
  controlScaleProps: Me,
  setupControlScale: xe
}, Symbol.toStringTag, { value: "Module" }));
var eo = defineComponent({
  name: "LControlScale",
  props: Me,
  setup(e, o) {
    const t = ref(), n = inject(h2), s = m(H), { options: r, methods: l } = xe(e, t);
    return onMounted(async () => {
      const { control: a } = n ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(a.scale(r)), L(l, t.value, e), s({ leafletObject: t.value }), nextTick(() => o.emit("ready", t.value));
    }), { leafletObject: t };
  },
  render() {
    return null;
  }
});
var Be = {
  ...F,
  zoomInText: {
    type: String
  },
  zoomInTitle: {
    type: String
  },
  zoomOutText: {
    type: String
  },
  zoomOutTitle: {
    type: String
  }
};
var Re = (e, o) => {
  const { options: t, methods: n } = K(
    e,
    o
  );
  return { options: f(
    e,
    Be,
    t
  ), methods: n };
};
var Ct = Object.freeze(Object.defineProperty({
  __proto__: null,
  controlZoomProps: Be,
  setupControlZoom: Re
}, Symbol.toStringTag, { value: "Module" }));
var to = defineComponent({
  name: "LControlZoom",
  props: Be,
  setup(e, o) {
    const t = ref(), n = inject(h2), s = m(H), { options: r, methods: l } = Re(e, t);
    return onMounted(async () => {
      const { control: a } = n ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(a.zoom(r)), L(l, t.value, e), s({ leafletObject: t.value }), nextTick(() => o.emit("ready", t.value));
    }), { leafletObject: t };
  },
  render() {
    return null;
  }
});
var te = {
  ...D
};
var se = (e, o, t) => {
  const { options: n, methods: s } = q(
    e,
    o,
    t
  ), r = f(
    e,
    te,
    n
  ), l = {
    ...s,
    addLayer(a) {
      o.value.addLayer(a.leafletObject);
    },
    removeLayer(a) {
      o.value.removeLayer(a.leafletObject);
    }
  };
  return provide(M, l.addLayer), provide(ee, l.removeLayer), { options: r, methods: l };
};
var Tt = Object.freeze(Object.defineProperty({
  __proto__: null,
  layerGroupProps: te,
  setupLayerGroup: se
}, Symbol.toStringTag, { value: "Module" }));
var we = {
  ...te
};
var et = (e, o, t) => {
  const { options: n, methods: s } = se(
    e,
    o,
    t
  ), r = f(
    e,
    we,
    n
  ), l = {
    ...s
  };
  return { options: r, methods: l };
};
var Mt = Object.freeze(Object.defineProperty({
  __proto__: null,
  featureGroupProps: we,
  setupFeatureGroup: et
}, Symbol.toStringTag, { value: "Module" }));
var oo = defineComponent({
  props: we,
  setup(e, o) {
    const t = ref(), n = ref(false), s = inject(h2), r = m(M), { methods: l, options: a } = et(
      e,
      t,
      o
    );
    return onMounted(async () => {
      const { featureGroup: i } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(
        i(void 0, a)
      );
      const { listeners: u } = T(o.attrs);
      t.value.on(u), L(l, t.value, e), r({
        ...e,
        ...l,
        leafletObject: t.value
      }), n.value = true, nextTick(() => o.emit("ready", t.value));
    }), { ready: n, leafletObject: t };
  },
  render() {
    return G(this.ready, this.$slots);
  }
});
var Ie = {
  ...te,
  geojson: {
    type: [Object, Array],
    custom: true
  },
  optionsStyle: {
    type: Function,
    custom: true
  }
};
var tt = (e, o, t) => {
  const { options: n, methods: s } = se(
    e,
    o,
    t
  ), r = f(
    e,
    Ie,
    n
  );
  Object.prototype.hasOwnProperty.call(e, "optionsStyle") && (r.style = e.optionsStyle);
  const l = {
    ...s,
    setGeojson(a) {
      o.value.clearLayers(), o.value.addData(a);
    },
    setOptionsStyle(a) {
      o.value.setStyle(a);
    },
    getGeoJSONData() {
      return o.value.toGeoJSON();
    },
    getBounds() {
      return o.value.getBounds();
    }
  };
  return { options: r, methods: l };
};
var Bt = Object.freeze(Object.defineProperty({
  __proto__: null,
  geoJSONProps: Ie,
  setupGeoJSON: tt
}, Symbol.toStringTag, { value: "Module" }));
var no = defineComponent({
  props: Ie,
  setup(e, o) {
    const t = ref(), n = ref(false), s = inject(h2), r = m(M), { methods: l, options: a } = tt(e, t, o);
    return onMounted(async () => {
      const { geoJSON: i } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(i(e.geojson, a));
      const { listeners: u } = T(o.attrs);
      t.value.on(u), L(l, t.value, e), r({
        ...e,
        ...l,
        leafletObject: t.value
      }), n.value = true, nextTick(() => o.emit("ready", t.value));
    }), { ready: n, leafletObject: t };
  },
  render() {
    return G(this.ready, this.$slots);
  }
});
var ae = {
  ...D,
  opacity: {
    type: Number
  },
  zIndex: {
    type: Number
  },
  tileSize: {
    type: [Number, Array, Object]
  },
  noWrap: {
    type: Boolean,
    default: void 0
  },
  minZoom: {
    type: Number
  },
  maxZoom: {
    type: Number
  },
  className: {
    type: String
  }
};
var Ae = (e, o, t) => {
  const { options: n, methods: s } = q(
    e,
    o,
    t
  ), r = f(
    e,
    ae,
    n
  ), l = {
    ...s,
    setTileComponent() {
      var a;
      (a = o.value) == null || a.redraw();
    }
  };
  return onUnmounted(() => {
    o.value.off();
  }), { options: r, methods: l };
};
var ot = (e, o, t, n) => e.extend({
  initialize(s) {
    this.tileComponents = {}, this.on("tileunload", this._unloadTile), t.setOptions(this, s);
  },
  createTile(s) {
    const r = this._tileCoordsToKey(s);
    this.tileComponents[r] = o.create("div");
    const l = h({ setup: n, props: ["coords"] }, { coords: s });
    return render(l, this.tileComponents[r]), this.tileComponents[r];
  },
  _unloadTile(s) {
    const r = this._tileCoordsToKey(s.coords);
    this.tileComponents[r] && (this.tileComponents[r].innerHTML = "", this.tileComponents[r] = void 0);
  }
});
var wt = Object.freeze(Object.defineProperty({
  __proto__: null,
  CreateVueGridLayer: ot,
  gridLayerProps: ae,
  setupGridLayer: Ae
}, Symbol.toStringTag, { value: "Module" }));
var ro = defineComponent({
  props: {
    ...ae,
    childRender: {
      type: Function,
      required: true
    }
  },
  setup(e, o) {
    const t = ref(), n = ref(null), s = ref(false), r = inject(h2), l = m(M), { options: a, methods: i } = Ae(e, t, o);
    return onMounted(async () => {
      const { GridLayer: u, DomUtil: d, Util: y } = r ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js"), w = ot(
        u,
        d,
        y,
        e.childRender
      );
      t.value = markRaw(new w(a));
      const { listeners: v } = T(o.attrs);
      t.value.on(v), L(i, t.value, e), l({
        ...e,
        ...i,
        leafletObject: t.value
      }), s.value = true, nextTick(() => o.emit("ready", t.value));
    }), { root: n, ready: s, leafletObject: t };
  },
  render() {
    return this.ready ? h("div", { style: { display: "none" }, ref: "root" }) : null;
  }
});
var de = {
  iconUrl: {
    type: String
  },
  iconRetinaUrl: {
    type: String
  },
  iconSize: {
    type: [Object, Array]
  },
  iconAnchor: {
    type: [Object, Array]
  },
  popupAnchor: {
    type: [Object, Array]
  },
  tooltipAnchor: {
    type: [Object, Array]
  },
  shadowUrl: {
    type: String
  },
  shadowRetinaUrl: {
    type: String
  },
  shadowSize: {
    type: [Object, Array]
  },
  shadowAnchor: {
    type: [Object, Array]
  },
  bgPos: {
    type: [Object, Array]
  },
  className: {
    type: String
  }
};
var It = Object.freeze(Object.defineProperty({
  __proto__: null,
  iconProps: de
}, Symbol.toStringTag, { value: "Module" }));
var so = defineComponent({
  name: "LIcon",
  props: {
    ...de,
    ...W
  },
  setup(e, o) {
    const t = ref(), n = inject(h2), s = m(ve), r = m(be), l = m(fe);
    let a, i, u, d, y;
    const w = (N, P, B) => {
      const I = N && N.innerHTML;
      if (!P) {
        B && y && s() && r(I);
        return;
      }
      const { listeners: E } = T(o.attrs);
      y && i(y, E);
      const { options: ue } = J(e), $ = f(
        e,
        de,
        ue
      );
      I && ($.html = I), y = $.html ? u($) : d($), a(y, E), l(y);
    }, v = () => {
      nextTick(() => w(t.value, true, false));
    }, z = () => {
      nextTick(() => w(t.value, false, true));
    }, Z = {
      setIconUrl: v,
      setIconRetinaUrl: v,
      setIconSize: v,
      setIconAnchor: v,
      setPopupAnchor: v,
      setTooltipAnchor: v,
      setShadowUrl: v,
      setShadowRetinaUrl: v,
      setShadowAnchor: v,
      setBgPos: v,
      setClassName: v,
      setHtml: v
    };
    return onMounted(async () => {
      const {
        DomEvent: N,
        divIcon: P,
        icon: B
      } = n ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      a = N.on, i = N.off, u = P, d = B, L(Z, {}, e), new MutationObserver(z).observe(t.value, {
        attributes: true,
        childList: true,
        characterData: true,
        subtree: true
      }), v();
    }), { root: t };
  },
  render() {
    const e = this.$slots.default ? this.$slots.default() : void 0;
    return h("div", { ref: "root" }, e);
  }
});
var Ge = {
  ...D,
  opacity: {
    type: Number
  },
  alt: {
    type: String
  },
  interactive: {
    type: Boolean,
    default: void 0
  },
  crossOrigin: {
    type: Boolean,
    default: void 0
  },
  errorOverlayUrl: {
    type: String
  },
  zIndex: {
    type: Number
  },
  className: {
    type: String
  },
  url: {
    type: String,
    required: true,
    custom: true
  },
  bounds: {
    type: [Array, Object],
    required: true,
    custom: true
  }
};
var nt = (e, o, t) => {
  const { options: n, methods: s } = q(
    e,
    o,
    t
  ), r = f(
    e,
    Ge,
    n
  ), l = {
    ...s,
    /**
     * Sets the opacity of the overlay.
     * @param {number} opacity
     */
    setOpacity(a) {
      return o.value.setOpacity(a);
    },
    /**
     * Changes the URL of the image.
     * @param {string} url
     */
    setUrl(a) {
      return o.value.setUrl(a);
    },
    /**
     * Update the bounds that this ImageOverlay covers
     * @param {LatLngBounds | Array<Array<number>>} bounds
     */
    setBounds(a) {
      return o.value.setBounds(a);
    },
    /**
     * Get the bounds that this ImageOverlay covers
     * @returns {LatLngBounds}
     */
    getBounds() {
      return o.value.getBounds();
    },
    /**
     * Returns the instance of HTMLImageElement used by this overlay.
     * @returns {HTMLElement}
     */
    getElement() {
      return o.value.getElement();
    },
    /**
     * Brings the layer to the top of all overlays.
     */
    bringToFront() {
      return o.value.bringToFront();
    },
    /**
     * Brings the layer to the bottom of all overlays.
     */
    bringToBack() {
      return o.value.bringToBack();
    },
    /**
     * Changes the zIndex of the image overlay.
     * @param {number} zIndex
     */
    setZIndex(a) {
      return o.value.setZIndex(a);
    }
  };
  return { options: r, methods: l };
};
var At = Object.freeze(Object.defineProperty({
  __proto__: null,
  imageOverlayProps: Ge,
  setupImageOverlay: nt
}, Symbol.toStringTag, { value: "Module" }));
var ao = defineComponent({
  name: "LImageOverlay",
  props: Ge,
  setup(e, o) {
    const t = ref(), n = ref(false), s = inject(h2), r = m(M), { options: l, methods: a } = nt(
      e,
      t,
      o
    );
    return onMounted(async () => {
      const { imageOverlay: i } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(
        i(e.url, e.bounds, l)
      );
      const { listeners: u } = T(o.attrs);
      t.value.on(u), L(a, t.value, e), r({
        ...e,
        ...a,
        leafletObject: t.value
      }), n.value = true, nextTick(() => o.emit("ready", t.value));
    }), { ready: n, leafletObject: t };
  },
  render() {
    return G(this.ready, this.$slots);
  }
});
var lo = defineComponent({
  props: te,
  setup(e, o) {
    const t = ref(), n = ref(false), s = inject(h2), r = m(M), { methods: l } = se(e, t, o);
    return onMounted(async () => {
      const { layerGroup: a } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(
        a(void 0, e.options)
      );
      const { listeners: i } = T(o.attrs);
      t.value.on(i), L(l, t.value, e), r({
        ...e,
        ...l,
        leafletObject: t.value
      }), n.value = true, nextTick(() => o.emit("ready", t.value));
    }), { ready: n, leafletObject: t };
  },
  render() {
    return G(this.ready, this.$slots);
  }
});
function rt(e, o, t) {
  var n, s, r;
  o === void 0 && (o = 50), t === void 0 && (t = {});
  var l = (n = t.isImmediate) != null && n, a = (s = t.callback) != null && s, i = t.maxWait, u = Date.now(), d = [];
  function y() {
    if (i !== void 0) {
      var v = Date.now() - u;
      if (v + o >= i)
        return i - v;
    }
    return o;
  }
  var w = function() {
    var v = [].slice.call(arguments), z = this;
    return new Promise(function(Z, N) {
      var P = l && r === void 0;
      if (r !== void 0 && clearTimeout(r), r = setTimeout(function() {
        if (r = void 0, u = Date.now(), !l) {
          var I = e.apply(z, v);
          a && a(I), d.forEach(function(E) {
            return (0, E.resolve)(I);
          }), d = [];
        }
      }, y()), P) {
        var B = e.apply(z, v);
        return a && a(B), Z(B);
      }
      d.push({ resolve: Z, reject: N });
    });
  };
  return w.cancel = function(v) {
    r !== void 0 && clearTimeout(r), d.forEach(function(z) {
      return (0, z.reject)(v);
    }), d = [];
  }, w;
}
var We = {
  ...W,
  /**
   * The center of the map, supports .sync modifier
   */
  center: {
    type: [Object, Array]
  },
  /**
   * The bounds of the map, supports .sync modifier
   */
  bounds: {
    type: [Array, Object]
  },
  /**
   * The max bounds of the map
   */
  maxBounds: {
    type: [Array, Object]
  },
  /**
   * The zoom of the map, supports .sync modifier
   */
  zoom: {
    type: Number
  },
  /**
   * The minZoom of the map
   */
  minZoom: {
    type: Number
  },
  /**
   * The maxZoom of the map
   */
  maxZoom: {
    type: Number
  },
  /**
   * The paddingBottomRight of the map
   */
  paddingBottomRight: {
    type: [Object, Array]
  },
  /**
   * The paddingTopLeft of the map
   */
  paddingTopLeft: {
    type: Object
  },
  /**
   * The padding of the map
   */
  padding: {
    type: Object
  },
  /**
   * The worldCopyJump option for the map
   */
  worldCopyJump: {
    type: Boolean,
    default: void 0
  },
  /**
   * The CRS to use for the map. Can be an object that defines a coordinate reference
   * system for projecting geographical points into screen coordinates and back
   * (see https://leafletjs.com/reference-1.7.1.html#crs-l-crs-base), or a string
   * name identifying one of Leaflet's defined CRSs, such as "EPSG4326".
   */
  crs: {
    type: [String, Object]
  },
  maxBoundsViscosity: {
    type: Number
  },
  inertia: {
    type: Boolean,
    default: void 0
  },
  inertiaDeceleration: {
    type: Number
  },
  inertiaMaxSpeed: {
    type: Number
  },
  easeLinearity: {
    type: Number
  },
  zoomAnimation: {
    type: Boolean,
    default: void 0
  },
  zoomAnimationThreshold: {
    type: Number
  },
  fadeAnimation: {
    type: Boolean,
    default: void 0
  },
  markerZoomAnimation: {
    type: Boolean,
    default: void 0
  },
  noBlockingAnimations: {
    type: Boolean,
    default: void 0
  },
  useGlobalLeaflet: {
    type: Boolean,
    default: true,
    custom: true
  }
};
var io = defineComponent({
  inheritAttrs: false,
  emits: ["ready", "update:zoom", "update:center", "update:bounds"],
  props: We,
  setup(e, o) {
    const t = ref(), n = reactive({
      ready: false,
      layersToAdd: [],
      layersInControl: []
    }), { options: s } = J(e), r = f(
      e,
      We,
      s
    ), { listeners: l, attrs: a } = T(o.attrs), i = Y(M), u = Y(ee), d = Y(H), y = Y(
      me
    );
    provide(h2, e.useGlobalLeaflet);
    const w = computed(() => {
      const P = {};
      return e.noBlockingAnimations && (P.animate = false), P;
    }), v = computed(() => {
      const P = w.value;
      return e.padding && (P.padding = e.padding), e.paddingTopLeft && (P.paddingTopLeft = e.paddingTopLeft), e.paddingBottomRight && (P.paddingBottomRight = e.paddingBottomRight), P;
    }), z = {
      moveend: rt((P) => {
        n.leafletRef && (o.emit("update:zoom", n.leafletRef.getZoom()), o.emit("update:center", n.leafletRef.getCenter()), o.emit("update:bounds", n.leafletRef.getBounds()));
      }),
      overlayadd(P) {
        const B = n.layersInControl.find((I) => I.name === P.name);
        B && B.updateVisibleProp(true);
      },
      overlayremove(P) {
        const B = n.layersInControl.find((I) => I.name === P.name);
        B && B.updateVisibleProp(false);
      }
    };
    onMounted(async () => {
      e.useGlobalLeaflet && (b.L = b.L || await import("./leaflet.js"));
      const { map: P, CRS: B, Icon: I, latLngBounds: E, latLng: ue, stamp: $ } = e.useGlobalLeaflet ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      try {
        r.beforeMapMount && await r.beforeMapMount();
      } catch (p) {
        console.error(
          `The following error occurred running the provided beforeMapMount hook ${p.message}`
        );
      }
      await qe(I);
      const yt = typeof r.crs == "string" ? B[r.crs] : r.crs;
      r.crs = yt || B.EPSG3857;
      const Q = {
        addLayer(p) {
          p.layerType !== void 0 && (n.layerControl === void 0 ? n.layersToAdd.push(p) : n.layersInControl.find(
            (X) => $(X.leafletObject) === $(p.leafletObject)
          ) || (n.layerControl.addLayer(p), n.layersInControl.push(p))), p.visible !== false && n.leafletRef.addLayer(p.leafletObject);
        },
        removeLayer(p) {
          p.layerType !== void 0 && (n.layerControl === void 0 ? n.layersToAdd = n.layersToAdd.filter(
            (C) => C.name !== p.name
          ) : (n.layerControl.removeLayer(p.leafletObject), n.layersInControl = n.layersInControl.filter(
            (C) => $(C.leafletObject) !== $(p.leafletObject)
          ))), n.leafletRef.removeLayer(p.leafletObject);
        },
        registerLayerControl(p) {
          n.layerControl = p, n.layersToAdd.forEach((C) => {
            n.layerControl.addLayer(C);
          }), n.layersToAdd = [], d(p);
        },
        registerControl(p) {
          n.leafletRef.addControl(p.leafletObject);
        },
        setZoom(p) {
          const C = n.leafletRef.getZoom();
          p !== C && n.leafletRef.setZoom(p, w.value);
        },
        setCrs(p) {
          const C = n.leafletRef.getBounds();
          n.leafletRef.options.crs = p, n.leafletRef.fitBounds(C, {
            animate: false,
            padding: [0, 0]
          });
        },
        fitBounds(p) {
          n.leafletRef.fitBounds(p, v.value);
        },
        setBounds(p) {
          if (!p)
            return;
          const C = E(p);
          if (!C.isValid())
            return;
          !(n.lastSetBounds || n.leafletRef.getBounds()).equals(C, 0) && (n.lastSetBounds = C, n.leafletRef.fitBounds(C));
        },
        setCenter(p) {
          if (p == null)
            return;
          const C = ue(p), X = n.lastSetCenter || n.leafletRef.getCenter();
          (X.lat !== C.lat || X.lng !== C.lng) && (n.lastSetCenter = C, n.leafletRef.panTo(C, w.value));
        }
      };
      V(i, Q.addLayer), V(u, Q.removeLayer), V(d, Q.registerControl), V(y, Q.registerLayerControl), n.leafletRef = markRaw(P(t.value, r)), L(Q, n.leafletRef, e), ce(n.leafletRef, z), ce(n.leafletRef, l), n.ready = true, nextTick(() => o.emit("ready", n.leafletRef));
    }), onBeforeUnmount(() => {
      ye(z), n.leafletRef && (n.leafletRef.off(), n.leafletRef.remove());
    });
    const Z = computed(() => n.leafletRef), N = computed(() => n.ready);
    return { root: t, ready: N, leafletObject: Z, attrs: a };
  },
  render({ attrs: e }) {
    return e.style || (e.style = {}), e.style.width || (e.style.width = "100%"), e.style.height || (e.style.height = "100%"), h(
      "div",
      {
        ...e,
        ref: "root"
      },
      this.ready && this.$slots.default ? this.$slots.default() : {}
    );
  }
});
var Gt = ["Symbol(Comment)", "Symbol(Text)"];
var zt = ["LTooltip", "LPopup"];
var ze = {
  ...D,
  draggable: {
    type: Boolean,
    default: void 0
  },
  icon: {
    type: [Object]
  },
  zIndexOffset: {
    type: Number
  },
  latLng: {
    type: [Object, Array],
    custom: true,
    required: true
  }
};
var st = (e, o, t) => {
  const { options: n, methods: s } = q(
    e,
    o,
    t
  ), r = f(
    e,
    ze,
    n
  ), l = {
    ...s,
    setDraggable(a) {
      o.value.dragging && (a ? o.value.dragging.enable() : o.value.dragging.disable());
    },
    latLngSync(a) {
      t.emit("update:latLng", a.latlng), t.emit("update:lat-lng", a.latlng);
    },
    setLatLng(a) {
      if (a != null && o.value) {
        const i = o.value.getLatLng();
        (!i || !i.equals(a)) && o.value.setLatLng(a);
      }
    }
  };
  return { options: r, methods: l };
};
var at = (e, o) => {
  const t = o.slots.default && o.slots.default();
  return t && t.length && t.some(Nt);
};
function Nt(e) {
  return !(Gt.includes(e.type.toString()) || zt.includes(e.type.name));
}
var $t = Object.freeze(Object.defineProperty({
  __proto__: null,
  markerProps: ze,
  setupMarker: st,
  shouldBlankIcon: at
}, Symbol.toStringTag, { value: "Module" }));
var uo = defineComponent({
  name: "LMarker",
  props: ze,
  setup(e, o) {
    const t = ref(), n = ref(false), s = inject(h2), r = m(M);
    provide(
      ve,
      () => {
        var u;
        return !!((u = t.value) != null && u.getElement());
      }
    ), provide(be, (u) => {
      var y, w;
      const d = k((y = t.value) == null ? void 0 : y.getElement) && ((w = t.value) == null ? void 0 : w.getElement());
      d && (d.innerHTML = u);
    }), provide(
      fe,
      (u) => {
        var d;
        return ((d = t.value) == null ? void 0 : d.setIcon) && t.value.setIcon(u);
      }
    );
    const { options: l, methods: a } = st(e, t, o), i = {
      moveHandler: rt(a.latLngSync)
    };
    return onMounted(async () => {
      const { marker: u, divIcon: d } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      at(l, o) && (l.icon = d({ className: "" })), t.value = markRaw(u(e.latLng, l));
      const { listeners: y } = T(o.attrs);
      t.value.on(y), t.value.on("move", i.moveHandler), L(a, t.value, e), r({
        ...e,
        ...a,
        leafletObject: t.value
      }), n.value = true, nextTick(() => o.emit("ready", t.value));
    }), onBeforeUnmount(() => ye(i)), { ready: n, leafletObject: t };
  },
  render() {
    return G(this.ready, this.$slots);
  }
});
var le = {
  ...ne,
  smoothFactor: {
    type: Number
  },
  noClip: {
    type: Boolean,
    default: void 0
  },
  latLngs: {
    type: Array,
    required: true,
    custom: true
  }
};
var Ne = (e, o, t) => {
  const { options: n, methods: s } = _e(
    e,
    o,
    t
  ), r = f(
    e,
    le,
    n
  ), l = {
    ...s,
    setSmoothFactor(a) {
      o.value.setStyle({ smoothFactor: a });
    },
    setNoClip(a) {
      o.value.setStyle({ noClip: a });
    },
    addLatLng(a) {
      o.value.addLatLng(a);
    }
  };
  return { options: r, methods: l };
};
var kt = Object.freeze(Object.defineProperty({
  __proto__: null,
  polylineProps: le,
  setupPolyline: Ne
}, Symbol.toStringTag, { value: "Module" }));
var x = {
  ...le
};
var $e = (e, o, t) => {
  const { options: n, methods: s } = Ne(
    e,
    o,
    t
  ), r = f(
    e,
    x,
    n
  ), l = {
    ...s,
    toGeoJSON(a) {
      return o.value.toGeoJSON(a);
    }
  };
  return { options: r, methods: l };
};
var Ut = Object.freeze(Object.defineProperty({
  __proto__: null,
  polygonProps: x,
  setupPolygon: $e
}, Symbol.toStringTag, { value: "Module" }));
var co = defineComponent({
  name: "LPolygon",
  props: x,
  setup(e, o) {
    const t = ref(), n = ref(false), s = inject(h2), r = m(M), { options: l, methods: a } = $e(e, t, o);
    return onMounted(async () => {
      const { polygon: i } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(i(e.latLngs, l));
      const { listeners: u } = T(o.attrs);
      t.value.on(u), L(a, t.value, e), r({
        ...e,
        ...a,
        leafletObject: t.value
      }), n.value = true, nextTick(() => o.emit("ready", t.value));
    }), { ready: n, leafletObject: t };
  },
  render() {
    return G(this.ready, this.$slots);
  }
});
var po = defineComponent({
  name: "LPolyline",
  props: le,
  setup(e, o) {
    const t = ref(), n = ref(false), s = inject(h2), r = m(M), { options: l, methods: a } = Ne(e, t, o);
    return onMounted(async () => {
      const { polyline: i } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(
        i(e.latLngs, l)
      );
      const { listeners: u } = T(o.attrs);
      t.value.on(u), L(a, t.value, e), r({
        ...e,
        ...a,
        leafletObject: t.value
      }), n.value = true, nextTick(() => o.emit("ready", t.value));
    }), { ready: n, leafletObject: t };
  },
  render() {
    return G(this.ready, this.$slots);
  }
});
var ke = {
  ...W,
  content: {
    type: String,
    default: null
  }
};
var Ue = (e, o) => {
  const { options: t, methods: n } = J(e), s = {
    ...n,
    setContent(r) {
      o.value && r !== null && r !== void 0 && o.value.setContent(r);
    }
  };
  return { options: t, methods: s };
};
var De = (e) => e.default ? h("div", { ref: "root" }, e.default()) : null;
var Dt = Object.freeze(Object.defineProperty({
  __proto__: null,
  popperProps: ke,
  render: De,
  setupPopper: Ue
}, Symbol.toStringTag, { value: "Module" }));
var lt = {
  ...ke,
  latLng: {
    type: [Object, Array],
    default: () => []
  }
};
var it = (e, o) => {
  const { options: t, methods: n } = Ue(e, o);
  return { options: t, methods: n };
};
var Ft = Object.freeze(Object.defineProperty({
  __proto__: null,
  popupProps: lt,
  setupPopup: it
}, Symbol.toStringTag, { value: "Module" }));
var yo = defineComponent({
  name: "LPopup",
  props: lt,
  setup(e, o) {
    const t = ref(), n = ref(null), s = inject(h2), r = m(ge), l = m(he), { options: a, methods: i } = it(e, t);
    return onMounted(async () => {
      const { popup: u } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(u(a)), e.latLng !== void 0 && t.value.setLatLng(e.latLng), L(i, t.value, e);
      const { listeners: d } = T(o.attrs);
      t.value.on(d), t.value.setContent(e.content || n.value || ""), r(t.value), nextTick(() => o.emit("ready", t.value));
    }), onBeforeUnmount(() => {
      l();
    }), { root: n, leafletObject: t };
  },
  render() {
    return De(this.$slots);
  }
});
var Fe = {
  ...x,
  latLngs: {
    ...x.latLngs,
    required: false
  },
  bounds: {
    type: Object,
    custom: true
  }
};
var ut = (e, o, t) => {
  const { options: n, methods: s } = $e(
    e,
    o,
    t
  ), r = f(
    e,
    Fe,
    n
  ), l = {
    ...s,
    setBounds(a) {
      o.value.setBounds(a);
    },
    setLatLngs(a) {
      o.value.setBounds(a);
    }
  };
  return { options: r, methods: l };
};
var Zt = Object.freeze(Object.defineProperty({
  __proto__: null,
  rectangleProps: Fe,
  setupRectangle: ut
}, Symbol.toStringTag, { value: "Module" }));
var mo = defineComponent({
  name: "LRectangle",
  props: Fe,
  setup(e, o) {
    const t = ref(), n = ref(false), s = inject(h2), r = m(M), { options: l, methods: a } = ut(e, t, o);
    return onMounted(async () => {
      const { rectangle: i, latLngBounds: u } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js"), d = e.bounds ? u(e.bounds) : u(e.latLngs || []);
      t.value = markRaw(i(d, l));
      const { listeners: y } = T(o.attrs);
      t.value.on(y), L(a, t.value, e), r({
        ...e,
        ...a,
        leafletObject: t.value
      }), n.value = true, nextTick(() => o.emit("ready", t.value));
    }), { ready: n, leafletObject: t };
  },
  render() {
    return G(this.ready, this.$slots);
  }
});
var ie = {
  ...ae,
  tms: {
    type: Boolean,
    default: void 0
  },
  subdomains: {
    type: [String, Array],
    validator: (e) => typeof e == "string" ? true : Array.isArray(e) ? e.every((o) => typeof o == "string") : false
  },
  detectRetina: {
    type: Boolean,
    default: void 0
  },
  url: {
    type: String,
    required: true,
    custom: true
  }
};
var Ze = (e, o, t) => {
  const { options: n, methods: s } = Ae(e, o, t), r = f(
    e,
    ie,
    n
  ), l = {
    ...s
  };
  return { options: r, methods: l };
};
var Et = Object.freeze(Object.defineProperty({
  __proto__: null,
  setupTileLayer: Ze,
  tileLayerProps: ie
}, Symbol.toStringTag, { value: "Module" }));
var vo = defineComponent({
  props: ie,
  setup(e, o) {
    const t = ref(), n = inject(h2), s = m(M), { options: r, methods: l } = Ze(e, t, o);
    return onMounted(async () => {
      const { tileLayer: a } = n ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(a(e.url, r));
      const { listeners: i } = T(o.attrs);
      t.value.on(i), L(l, t.value, e), s({
        ...e,
        ...l,
        leafletObject: t.value
      }), nextTick(() => o.emit("ready", t.value));
    }), { leafletObject: t };
  },
  render() {
    return null;
  }
});
var ct = {
  ...ke
};
var dt = (e, o) => {
  const { options: t, methods: n } = Ue(e, o), s = m(Oe);
  return onBeforeUnmount(() => {
    s();
  }), { options: t, methods: n };
};
var Ht = Object.freeze(Object.defineProperty({
  __proto__: null,
  setupTooltip: dt,
  tooltipProps: ct
}, Symbol.toStringTag, { value: "Module" }));
var bo = defineComponent({
  name: "LTooltip",
  props: ct,
  setup(e, o) {
    const t = ref(), n = ref(null), s = inject(h2), r = m(Le), { options: l, methods: a } = dt(e, t);
    return onMounted(async () => {
      const { tooltip: i } = s ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(i(l)), L(a, t.value, e);
      const { listeners: u } = T(o.attrs);
      t.value.on(u), t.value.setContent(e.content || n.value || ""), r(t.value), nextTick(() => o.emit("ready", t.value));
    }), { root: n, leafletObject: t };
  },
  render() {
    return De(this.$slots);
  }
});
var Ee = {
  ...ie,
  layers: {
    type: String,
    required: true
  },
  styles: {
    type: String
  },
  format: {
    type: String
  },
  transparent: {
    type: Boolean,
    default: void 0
  },
  version: {
    type: String
  },
  crs: {
    type: Object
  },
  uppercase: {
    type: Boolean,
    default: void 0
  }
};
var pt = (e, o, t) => {
  const { options: n, methods: s } = Ze(e, o, t);
  return {
    options: f(
      e,
      Ee,
      n
    ),
    methods: {
      ...s
    }
  };
};
var Wt = Object.freeze(Object.defineProperty({
  __proto__: null,
  setupWMSTileLayer: pt,
  wmsTileLayerProps: Ee
}, Symbol.toStringTag, { value: "Module" }));
var fo = defineComponent({
  props: Ee,
  setup(e, o) {
    const t = ref(), n = inject(h2), s = m(M), { options: r, methods: l } = pt(
      e,
      t,
      o
    );
    return onMounted(async () => {
      const { tileLayer: a } = n ? b.L : await import("./leaflet-src.esm-F2MEOW4Q.js");
      t.value = markRaw(
        a.wms(e.url, r)
      );
      const { listeners: i } = T(o.attrs);
      t.value.on(i), L(l, t.value, e), s({
        ...e,
        ...l,
        leafletObject: t.value
      }), nextTick(() => o.emit("ready", t.value));
    }), { leafletObject: t };
  },
  render() {
    return null;
  }
});
var go = Object.freeze(Object.defineProperty({
  __proto__: null,
  Circle: Ot,
  CircleMarker: ht,
  Component: bt,
  Control: St,
  ControlAttribution: _t,
  ControlLayers: jt,
  ControlScale: Pt,
  ControlZoom: Ct,
  FeatureGroup: Mt,
  GeoJSON: Bt,
  GridLayer: wt,
  Icon: It,
  ImageOverlay: At,
  InteractiveLayer: gt,
  Layer: ft,
  LayerGroup: Tt,
  Marker: $t,
  Path: Lt,
  Polygon: Ut,
  Polyline: kt,
  Popper: Dt,
  Popup: Ft,
  Rectangle: Zt,
  TileLayer: Et,
  Tooltip: Ht,
  WmsTileLayer: Wt
}, Symbol.toStringTag, { value: "Module" }));
export {
  go as Functions,
  Qt as InjectionKeys,
  Xt as LCircle,
  Yt as LCircleMarker,
  Vt as LControl,
  xt as LControlAttribution,
  Rt as LControlLayers,
  eo as LControlScale,
  to as LControlZoom,
  oo as LFeatureGroup,
  no as LGeoJson,
  ro as LGridLayer,
  so as LIcon,
  ao as LImageOverlay,
  lo as LLayerGroup,
  io as LMap,
  uo as LMarker,
  co as LPolygon,
  po as LPolyline,
  yo as LPopup,
  mo as LRectangle,
  vo as LTileLayer,
  bo as LTooltip,
  fo as LWmsTileLayer,
  Kt as Utilities
};
//# sourceMappingURL=@vue-leaflet_vue-leaflet.js.map
