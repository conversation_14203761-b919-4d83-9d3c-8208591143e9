{"version": 3, "file": "index.mjs", "sources": ["../../../src/breadcrumb/style/BreadcrumbStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/breadcrumb';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-breadcrumb p-component',\n    list: 'p-breadcrumb-list',\n    homeItem: 'p-breadcrumb-home-item',\n    separator: 'p-breadcrumb-separator',\n    separatorIcon: 'p-breadcrumb-separator-icon',\n    item: ({ instance }) => ['p-breadcrumb-item', { 'p-disabled': instance.disabled() }],\n    itemLink: 'p-breadcrumb-item-link',\n    itemIcon: 'p-breadcrumb-item-icon',\n    itemLabel: 'p-breadcrumb-item-label'\n};\n\nexport default BaseStyle.extend({\n    name: 'breadcrumb',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "list", "homeItem", "separator", "separatorIcon", "item", "_ref", "instance", "disabled", "itemLink", "itemIcon", "itemLabel", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,0BAA0B;AAChCC,EAAAA,IAAI,EAAE,mBAAmB;AACzBC,EAAAA,QAAQ,EAAE,wBAAwB;AAClCC,EAAAA,SAAS,EAAE,wBAAwB;AACnCC,EAAAA,aAAa,EAAE,6BAA6B;AAC5CC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAA,OAAO,CAAC,mBAAmB,EAAE;AAAE,MAAA,YAAY,EAAEA,QAAQ,CAACC,QAAQ;AAAG,KAAC,CAAC;AAAA,GAAA;AACpFC,EAAAA,QAAQ,EAAE,wBAAwB;AAClCC,EAAAA,QAAQ,EAAE,wBAAwB;AAClCC,EAAAA,SAAS,EAAE;AACf,CAAC;AAED,sBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,YAAY;AAClBC,EAAAA,KAAK,EAALA,KAAK;AACLhB,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}