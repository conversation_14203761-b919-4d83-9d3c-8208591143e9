{"version": 3, "file": "index.mjs", "sources": ["../../src/avatar/BaseAvatar.vue", "../../src/avatar/Avatar.vue", "../../src/avatar/Avatar.vue?vue&type=template&id=09391380&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport AvatarStyle from 'primevue/avatar/style';\n\nexport default {\n    name: 'BaseAvatar',\n    extends: BaseComponent,\n    props: {\n        label: {\n            type: String,\n            default: null\n        },\n        icon: {\n            type: String,\n            default: null\n        },\n        image: {\n            type: String,\n            default: null\n        },\n        size: {\n            type: String,\n            default: 'normal'\n        },\n        shape: {\n            type: String,\n            default: 'square'\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        }\n    },\n    style: AvatarStyle,\n    provide() {\n        return {\n            $pcAvatar: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :aria-labelledby=\"ariaLabelledby\" :aria-label=\"ariaLabel\" v-bind=\"ptmi('root')\" :data-p=\"dataP\">\n        <slot>\n            <span v-if=\"label\" :class=\"cx('label')\" v-bind=\"ptm('label')\" :data-p=\"dataP\">{{ label }}</span>\n            <component v-else-if=\"$slots.icon\" :is=\"$slots.icon\" :class=\"cx('icon')\" />\n            <span v-else-if=\"icon\" :class=\"[cx('icon'), icon]\" v-bind=\"ptm('icon')\" :data-p=\"dataP\" />\n            <img v-else-if=\"image\" :src=\"image\" :alt=\"ariaLabel\" @error=\"onError\" v-bind=\"ptm('image')\" :data-p=\"dataP\" />\n        </slot>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport BaseAvatar from './BaseAvatar.vue';\n\nexport default {\n    name: 'Avatar',\n    extends: BaseAvatar,\n    inheritAttrs: false,\n    emits: ['error'],\n    methods: {\n        onError(event) {\n            this.$emit('error', event);\n        }\n    },\n    computed: {\n        dataP() {\n            return cn({\n                [this.shape]: this.shape,\n                [this.size]: this.size\n            });\n        }\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :aria-labelledby=\"ariaLabelledby\" :aria-label=\"ariaLabel\" v-bind=\"ptmi('root')\" :data-p=\"dataP\">\n        <slot>\n            <span v-if=\"label\" :class=\"cx('label')\" v-bind=\"ptm('label')\" :data-p=\"dataP\">{{ label }}</span>\n            <component v-else-if=\"$slots.icon\" :is=\"$slots.icon\" :class=\"cx('icon')\" />\n            <span v-else-if=\"icon\" :class=\"[cx('icon'), icon]\" v-bind=\"ptm('icon')\" :data-p=\"dataP\" />\n            <img v-else-if=\"image\" :src=\"image\" :alt=\"ariaLabel\" @error=\"onError\" v-bind=\"ptm('image')\" :data-p=\"dataP\" />\n        </slot>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport BaseAvatar from './BaseAvatar.vue';\n\nexport default {\n    name: 'Avatar',\n    extends: BaseAvatar,\n    inheritAttrs: false,\n    emits: ['error'],\n    methods: {\n        onError(event) {\n            this.$emit('error', event);\n        }\n    },\n    computed: {\n        dataP() {\n            return cn({\n                [this.shape]: this.shape,\n                [this.size]: this.size\n            });\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "label", "type", "String", "icon", "image", "size", "shape", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "style", "AvatarStyle", "provide", "$pcAvatar", "$parentInstance", "BaseAvatar", "inheritAttrs", "emits", "methods", "onError", "event", "$emit", "computed", "dataP", "cn", "_defineProperty", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "ptmi", "$options", "_renderSlot", "ptm", "_hoisted_2", "$slots", "_createBlock", "_resolveDynamicComponent", "_hoisted_3", "src", "alt", "apply", "arguments", "_hoisted_4"], "mappings": ";;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,YAAY;AAClB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE;AACHC,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,IAAI,EAAE;AACFF,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDE,IAAAA,KAAK,EAAE;AACHH,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDG,IAAAA,IAAI,EAAE;AACFJ,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDI,IAAAA,KAAK,EAAE;AACHL,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDK,IAAAA,cAAc,EAAE;AACZN,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDM,IAAAA,SAAS,EAAE;AACPP,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDO,EAAAA,KAAK,EAAEC,WAAW;EAClBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,SAAS,EAAE,IAAI;AACfC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;AC7BD,aAAe;AACXhB,EAAAA,IAAI,EAAE,QAAQ;AACd,EAAA,SAAA,EAASiB,QAAU;AACnBC,EAAAA,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAE,CAAC,OAAO,CAAC;AAChBC,EAAAA,OAAO,EAAE;AACLC,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACC,KAAK,EAAE;AACX,MAAA,IAAI,CAACC,KAAK,CAAC,OAAO,EAAED,KAAK,CAAC;AAC9B;GACH;AACDE,EAAAA,QAAQ,EAAE;IACNC,KAAK,EAAA,SAALA,KAAKA,GAAG;MACJ,OAAOC,EAAE,CAAAC,eAAA,CAAAA,eAAA,CACJ,EAAA,EAAA,IAAI,CAAClB,KAAK,EAAG,IAAI,CAACA,KAAK,CAAA,EACvB,IAAI,CAACD,IAAI,EAAG,IAAI,CAACA,IAAG,CACxB,CAAC;AACN;AACJ;AACJ,CAAC;;;;;;;EChCG,OAAAoB,SAAA,EAAA,EAAAC,kBAAA,CAOK,OAPLC,UAOK,CAAA;AAPC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;IAAW,iBAAe,EAAED,IAAc,CAAArB,cAAA;IAAG,YAAU,EAAEqB,IAAS,CAAApB;KAAUoB,IAAI,CAAAE,IAAA,CAAA,MAAA,CAAA,EAAA;IAAW,QAAM,EAAEC,QAAK,CAAAT;AAAA,GAAA,CAAA,EAAA,CACnHU,UAAA,CAKMJ,4BALN,YAAA;AAAA,IAAA,OAKM,CAJUA,IAAK,CAAA5B,KAAA,IAAjByB,SAAA,EAAA,EAAAC,kBAAA,CAA+F,QAA/FC,UAA+F,CAAA;;AAA3E,MAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,OAAA;AAAmB,KAAA,EAAAD,IAAA,CAAAK,GAAG,CAAY,OAAA,CAAA,EAAA;MAAA,QAAM,EAAEF,QAAA,CAAAT;wBAAUM,IAAI,CAAA5B,KAAA,CAAA,EAAA,EAAA,EAAAkC,UAAA,CAAA,IAC/DN,IAAA,CAAAO,MAAM,CAAChC,IAAI,iBAAjCiC,WAA0E,CAAAC,uBAAA,CAAlCT,IAAM,CAAAO,MAAA,CAAChC,IAAI,CAAA,EAAA;;AAAG,MAAA,OAAA,iBAAOyB,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;8BAC9CD,IAAI,CAAAzB,IAAA,IAArBsB,SAAA,EAAA,EAAAC,kBAAA,CAAyF,QAAzFC,UAAyF,CAAA;;MAAjE,OAAK,EAAA,CAAGC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA,EAAUD,IAAI,CAAAzB,IAAA;OAAWyB,IAAG,CAAAK,GAAA,CAAA,MAAA,CAAA,EAAA;MAAW,QAAM,EAAEF,QAAK,CAAAT;KAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAAgB,UAAA,CAAA,IACtEV,IAAK,CAAAxB,KAAA,IAArBqB,SAAA,EAAA,EAAAC,kBAAA,CAA6G,OAA7GC,UAA6G,CAAA;;MAArFY,GAAG,EAAEX,IAAK,CAAAxB,KAAA;MAAGoC,GAAG,EAAEZ,IAAS,CAAApB,SAAA;MAAGU,OAAK;eAAEa,QAAO,CAAAb,OAAA,IAAAa,QAAA,CAAAb,OAAA,CAAAuB,KAAA,CAAAV,QAAA,EAAAW,SAAA,CAAA;OAAA;OAAUd,IAAG,CAAAK,GAAA,CAAA,OAAA,CAAA,EAAA;MAAY,QAAM,EAAEF,QAAK,CAAAT;AAAA,KAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAAqB,UAAA,CAAA;;;;;;;;"}