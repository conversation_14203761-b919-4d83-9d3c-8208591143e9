{"version": 3, "file": "index.mjs", "sources": ["../../src/accordionpanel/BaseAccordionPanel.vue", "../../src/accordionpanel/AccordionPanel.vue", "../../src/accordionpanel/AccordionPanel.vue?vue&type=template&id=37e9bce5&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport AccordionPanelStyle from 'primevue/accordionpanel/style';\n\nexport default {\n    name: 'BaseAccordionPanel',\n    extends: BaseComponent,\n    props: {\n        value: {\n            type: [String, Number],\n            default: undefined\n        },\n        disabled: {\n            type: Boolean,\n            default: false\n        },\n        as: {\n            type: [String, Object],\n            default: 'DIV'\n        },\n        asChild: {\n            type: Boolean,\n            default: false\n        }\n    },\n    style: AccordionPanelStyle,\n    provide() {\n        return {\n            $pcAccordionPanel: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <component v-if=\"!asChild\" :is=\"as\" :class=\"cx('root')\" v-bind=\"attrs\">\n        <slot></slot>\n    </component>\n    <slot v-else :class=\"cx('root')\" :active=\"active\" :a11yAttrs=\"a11yAttrs\"></slot>\n</template>\n\n<script>\nimport { mergeProps } from 'vue';\nimport BaseAccordionPanel from './BaseAccordionPanel.vue';\n\nexport default {\n    name: 'AccordionPanel',\n    extends: BaseAccordionPanel,\n    inheritAttrs: false,\n    inject: ['$pcAccordion'],\n    computed: {\n        active() {\n            return this.$pcAccordion.isItemActive(this.value);\n        },\n        attrs() {\n            return mergeProps(this.a11yAttrs, this.ptmi('root', this.ptParams));\n        },\n        a11yAttrs() {\n            return {\n                'data-pc-name': 'accordionpanel',\n                'data-p-disabled': this.disabled,\n                'data-p-active': this.active\n            };\n        },\n        ptParams() {\n            return {\n                context: {\n                    active: this.active\n                }\n            };\n        }\n    }\n};\n</script>\n", "<template>\n    <component v-if=\"!asChild\" :is=\"as\" :class=\"cx('root')\" v-bind=\"attrs\">\n        <slot></slot>\n    </component>\n    <slot v-else :class=\"cx('root')\" :active=\"active\" :a11yAttrs=\"a11yAttrs\"></slot>\n</template>\n\n<script>\nimport { mergeProps } from 'vue';\nimport BaseAccordionPanel from './BaseAccordionPanel.vue';\n\nexport default {\n    name: 'AccordionPanel',\n    extends: BaseAccordionPanel,\n    inheritAttrs: false,\n    inject: ['$pcAccordion'],\n    computed: {\n        active() {\n            return this.$pcAccordion.isItemActive(this.value);\n        },\n        attrs() {\n            return mergeProps(this.a11yAttrs, this.ptmi('root', this.ptParams));\n        },\n        a11yAttrs() {\n            return {\n                'data-pc-name': 'accordionpanel',\n                'data-p-disabled': this.disabled,\n                'data-p-active': this.active\n            };\n        },\n        ptParams() {\n            return {\n                context: {\n                    active: this.active\n                }\n            };\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "value", "type", "String", "Number", "undefined", "disabled", "Boolean", "as", "Object", "<PERSON><PERSON><PERSON><PERSON>", "style", "AccordionPanelStyle", "provide", "$pcAccordionPanel", "$parentInstance", "BaseAccordionPanel", "inheritAttrs", "inject", "computed", "active", "$pcAccordion", "isItemActive", "attrs", "mergeProps", "a11yAttrs", "ptmi", "ptParams", "context", "_ctx", "_createBlock", "_resolveDynamicComponent", "_mergeProps", "cx", "$options", "_renderSlot", "$slots"], "mappings": ";;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,oBAAoB;AAC1B,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE;AACHC,MAAAA,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;MACtB,SAASC,EAAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNJ,MAAAA,IAAI,EAAEK,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,EAAE,EAAE;AACAN,MAAAA,IAAI,EAAE,CAACC,MAAM,EAAEM,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDC,IAAAA,OAAO,EAAE;AACLR,MAAAA,IAAI,EAAEK,OAAO;MACb,SAAS,EAAA;AACb;GACH;AACDI,EAAAA,KAAK,EAAEC,mBAAmB;EAC1BC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,iBAAiB,EAAE,IAAI;AACvBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACrBD,aAAe;AACXjB,EAAAA,IAAI,EAAE,gBAAgB;AACtB,EAAA,SAAA,EAASkB,QAAkB;AAC3BC,EAAAA,YAAY,EAAE,KAAK;EACnBC,MAAM,EAAE,CAAC,cAAc,CAAC;AACxBC,EAAAA,QAAQ,EAAE;IACNC,MAAM,EAAA,SAANA,MAAMA,GAAG;MACL,OAAO,IAAI,CAACC,YAAY,CAACC,YAAY,CAAC,IAAI,CAACrB,KAAK,CAAC;KACpD;IACDsB,KAAK,EAAA,SAALA,KAAKA,GAAG;AACJ,MAAA,OAAOC,UAAU,CAAC,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,IAAI,CAAC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;KACtE;IACDF,SAAS,EAAA,SAATA,SAASA,GAAG;MACR,OAAO;AACH,QAAA,cAAc,EAAE,gBAAgB;QAChC,iBAAiB,EAAE,IAAI,CAACnB,QAAQ;QAChC,eAAe,EAAE,IAAI,CAACc;OACzB;KACJ;IACDO,QAAQ,EAAA,SAARA,QAAQA,GAAG;MACP,OAAO;AACHC,QAAAA,OAAO,EAAE;UACLR,MAAM,EAAE,IAAI,CAACA;AACjB;OACH;AACL;AACJ;AACJ,CAAC;;;UCrCqBS,IAAO,CAAAnB,OAAA,iBAAzBoB,WAEW,CAAAC,uBAAA,CAFqBF,IAAE,CAAArB,EAAA,CAAA,EAAlCwB,UAEW,CAAA;;AAF0B,IAAA,OAAA,EAAOH,IAAE,CAAAI,EAAA,CAAA,MAAA;KAAkBC,QAAK,CAAAX,KAAA,CAAA,EAAA;uBACjE,YAAA;MAAA,OAAY,CAAZY,UAAY,CAAAN,IAAA,CAAAO,MAAA,EAAA,SAAA,CAAA;;;uBAEhBD,UAA+E,CAAAN,IAAA,CAAAO,MAAA,EAAA,SAAA,EAAA;;IAAjE,wBAAOP,IAAE,CAAAI,EAAA,CAAA,MAAA,CAAA,CAAA;IAAWb,MAAM,EAAEc,QAAM,CAAAd,MAAA;IAAGK,SAAS,EAAES,QAAS,CAAAT;;;;;;;;"}