{"version": 3, "file": "index.mjs", "sources": ["../../src/blockui/BaseBlockUI.vue", "../../src/blockui/BlockUI.vue", "../../src/blockui/BlockUI.vue?vue&type=template&id=65d07dd0&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport BlockUIStyle from 'primevue/blockui/style';\n\nexport default {\n    name: 'BaseBlockUI',\n    extends: BaseComponent,\n    props: {\n        blocked: {\n            type: Boolean,\n            default: false\n        },\n        fullScreen: {\n            type: Boolean,\n            default: false\n        },\n        baseZIndex: {\n            type: Number,\n            default: 0\n        },\n        autoZIndex: {\n            type: Boolean,\n            default: true\n        }\n    },\n    style: BlockUIStyle,\n    provide() {\n        return {\n            $pcBlockUI: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div ref=\"container\" :class=\"cx('root')\" :aria-busy=\"isBlocked\" v-bind=\"ptmi('root')\">\n        <slot></slot>\n    </div>\n</template>\n\n<script>\nimport { addClass, createElement, hasCSSAnimation } from '@primeuix/utils/dom';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { blockBodyScroll, unblockBodyScroll } from 'primevue/utils';\nimport BaseBlockUI from './BaseBlockUI.vue';\n\nexport default {\n    name: 'BlockUI',\n    extends: BaseBlockUI,\n    inheritAttrs: false,\n    emits: ['block', 'unblock'],\n    mask: null,\n    data() {\n        return {\n            isBlocked: false\n        };\n    },\n    watch: {\n        blocked(newValue) {\n            if (newValue === true) this.block();\n            else this.unblock();\n        }\n    },\n    mounted() {\n        if (this.blocked) {\n            this.block();\n        }\n    },\n    methods: {\n        block() {\n            let styleClass = 'p-blockui-mask p-overlay-mask p-overlay-mask-enter';\n\n            if (this.fullScreen) {\n                styleClass += ' p-blockui-mask-document';\n\n                this.mask = createElement('div', {\n                    style: {\n                        position: 'fixed',\n                        top: '0',\n                        left: '0',\n                        width: '100%',\n                        height: '100%'\n                    },\n                    class: !this.isUnstyled && styleClass,\n                    'p-bind': this.ptm('mask')\n                });\n\n                document.body.appendChild(this.mask);\n                blockBodyScroll();\n                document.activeElement.blur();\n            } else {\n                this.mask = createElement('div', {\n                    style: {\n                        position: 'absolute',\n                        top: '0',\n                        left: '0',\n                        width: '100%',\n                        height: '100%'\n                    },\n                    class: !this.isUnstyled && styleClass,\n                    'p-bind': this.ptm('mask')\n                });\n                this.$refs.container.appendChild(this.mask);\n            }\n\n            if (this.autoZIndex) {\n                ZIndex.set('modal', this.mask, this.baseZIndex + this.$primevue.config.zIndex.modal);\n            }\n\n            this.isBlocked = true;\n            this.$emit('block');\n        },\n        unblock() {\n            if (this.mask) {\n                !this.isUnstyled && addClass(this.mask, 'p-overlay-mask-leave');\n\n                const handleAnimationEnd = () => {\n                    clearTimeout(fallbackTimer);\n                    this.mask.removeEventListener('animationend', handleAnimationEnd);\n                    this.mask.removeEventListener('webkitAnimationEnd', handleAnimationEnd);\n                };\n\n                const fallbackTimer = setTimeout(() => {\n                    this.removeMask();\n                }, 10);\n\n                if (hasCSSAnimation(this.mask) > 0) {\n                    this.mask.addEventListener('animationend', handleAnimationEnd);\n                    this.mask.addEventListener('webkitAnimationEnd', handleAnimationEnd);\n                }\n            } else {\n                this.removeMask();\n            }\n        },\n        removeMask() {\n            ZIndex.clear(this.mask);\n\n            if (this.fullScreen) {\n                document.body.removeChild(this.mask);\n                unblockBodyScroll();\n            } else {\n                this.$refs.container?.removeChild(this.mask);\n            }\n\n            this.isBlocked = false;\n            this.$emit('unblock');\n        }\n    }\n};\n</script>\n", "<template>\n    <div ref=\"container\" :class=\"cx('root')\" :aria-busy=\"isBlocked\" v-bind=\"ptmi('root')\">\n        <slot></slot>\n    </div>\n</template>\n\n<script>\nimport { addClass, createElement, hasCSSAnimation } from '@primeuix/utils/dom';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { blockBodyScroll, unblockBodyScroll } from 'primevue/utils';\nimport BaseBlockUI from './BaseBlockUI.vue';\n\nexport default {\n    name: 'BlockUI',\n    extends: BaseBlockUI,\n    inheritAttrs: false,\n    emits: ['block', 'unblock'],\n    mask: null,\n    data() {\n        return {\n            isBlocked: false\n        };\n    },\n    watch: {\n        blocked(newValue) {\n            if (newValue === true) this.block();\n            else this.unblock();\n        }\n    },\n    mounted() {\n        if (this.blocked) {\n            this.block();\n        }\n    },\n    methods: {\n        block() {\n            let styleClass = 'p-blockui-mask p-overlay-mask p-overlay-mask-enter';\n\n            if (this.fullScreen) {\n                styleClass += ' p-blockui-mask-document';\n\n                this.mask = createElement('div', {\n                    style: {\n                        position: 'fixed',\n                        top: '0',\n                        left: '0',\n                        width: '100%',\n                        height: '100%'\n                    },\n                    class: !this.isUnstyled && styleClass,\n                    'p-bind': this.ptm('mask')\n                });\n\n                document.body.appendChild(this.mask);\n                blockBodyScroll();\n                document.activeElement.blur();\n            } else {\n                this.mask = createElement('div', {\n                    style: {\n                        position: 'absolute',\n                        top: '0',\n                        left: '0',\n                        width: '100%',\n                        height: '100%'\n                    },\n                    class: !this.isUnstyled && styleClass,\n                    'p-bind': this.ptm('mask')\n                });\n                this.$refs.container.appendChild(this.mask);\n            }\n\n            if (this.autoZIndex) {\n                ZIndex.set('modal', this.mask, this.baseZIndex + this.$primevue.config.zIndex.modal);\n            }\n\n            this.isBlocked = true;\n            this.$emit('block');\n        },\n        unblock() {\n            if (this.mask) {\n                !this.isUnstyled && addClass(this.mask, 'p-overlay-mask-leave');\n\n                const handleAnimationEnd = () => {\n                    clearTimeout(fallbackTimer);\n                    this.mask.removeEventListener('animationend', handleAnimationEnd);\n                    this.mask.removeEventListener('webkitAnimationEnd', handleAnimationEnd);\n                };\n\n                const fallbackTimer = setTimeout(() => {\n                    this.removeMask();\n                }, 10);\n\n                if (hasCSSAnimation(this.mask) > 0) {\n                    this.mask.addEventListener('animationend', handleAnimationEnd);\n                    this.mask.addEventListener('webkitAnimationEnd', handleAnimationEnd);\n                }\n            } else {\n                this.removeMask();\n            }\n        },\n        removeMask() {\n            ZIndex.clear(this.mask);\n\n            if (this.fullScreen) {\n                document.body.removeChild(this.mask);\n                unblockBodyScroll();\n            } else {\n                this.$refs.container?.removeChild(this.mask);\n            }\n\n            this.isBlocked = false;\n            this.$emit('unblock');\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "blocked", "type", "Boolean", "fullScreen", "baseZIndex", "Number", "autoZIndex", "style", "BlockUIStyle", "provide", "$pcBlockUI", "$parentInstance", "BaseBlockUI", "inheritAttrs", "emits", "mask", "data", "isBlocked", "watch", "newValue", "block", "unblock", "mounted", "methods", "styleClass", "createElement", "position", "top", "left", "width", "height", "isUnstyled", "ptm", "document", "body", "append<PERSON><PERSON><PERSON>", "blockBodyScroll", "activeElement", "blur", "$refs", "container", "ZIndex", "set", "$primevue", "config", "zIndex", "modal", "$emit", "_this", "addClass", "handleAnimationEnd", "clearTimeout", "fallbackTimer", "removeEventListener", "setTimeout", "removeMask", "hasCSSAnimation", "addEventListener", "clear", "<PERSON><PERSON><PERSON><PERSON>", "unblockBodyScroll", "_this$$refs$container", "_openBlock", "_createElementBlock", "_mergeProps", "ref", "_ctx", "cx", "$data", "ptmi", "_renderSlot", "$slots"], "mappings": ";;;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,aAAa;AACnB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,OAAO,EAAE;AACLC,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,UAAU,EAAE;AACRF,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACDE,IAAAA,UAAU,EAAE;AACRH,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,UAAU,EAAE;AACRL,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;AACb;GACH;AACDK,EAAAA,KAAK,EAAEC,YAAY;EACnBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,UAAU,EAAE,IAAI;AAChBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACpBD,aAAe;AACXd,EAAAA,IAAI,EAAE,SAAS;AACf,EAAA,SAAA,EAASe,QAAW;AACpBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;AAC3BC,EAAAA,IAAI,EAAE,IAAI;EACVC,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACHC,MAAAA,SAAS,EAAE;KACd;GACJ;AACDC,EAAAA,KAAK,EAAE;AACHlB,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACmB,QAAQ,EAAE;AACd,MAAA,IAAIA,QAAO,KAAM,IAAI,EAAE,IAAI,CAACC,KAAK,EAAE,CAAA,KAC9B,IAAI,CAACC,OAAO,EAAE;AACvB;GACH;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,IAAI,CAACtB,OAAO,EAAE;MACd,IAAI,CAACoB,KAAK,EAAE;AAChB;GACH;AACDG,EAAAA,OAAO,EAAE;IACLH,KAAK,EAAA,SAALA,KAAKA,GAAG;MACJ,IAAII,UAAS,GAAI,oDAAoD;MAErE,IAAI,IAAI,CAACrB,UAAU,EAAE;AACjBqB,QAAAA,UAAS,IAAK,0BAA0B;AAExC,QAAA,IAAI,CAACT,IAAG,GAAIU,aAAa,CAAC,KAAK,EAAE;AAC7BlB,UAAAA,KAAK,EAAE;AACHmB,YAAAA,QAAQ,EAAE,OAAO;AACjBC,YAAAA,GAAG,EAAE,GAAG;AACRC,YAAAA,IAAI,EAAE,GAAG;AACTC,YAAAA,KAAK,EAAE,MAAM;AACbC,YAAAA,MAAM,EAAE;WACX;AACD,UAAA,OAAA,EAAO,CAAC,IAAI,CAACC,UAAW,IAAGP,UAAU;AACrC,UAAA,QAAQ,EAAE,IAAI,CAACQ,GAAG,CAAC,MAAM;AAC7B,SAAC,CAAC;QAEFC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACpB,IAAI,CAAC;AACpCqB,QAAAA,eAAe,EAAE;AACjBH,QAAAA,QAAQ,CAACI,aAAa,CAACC,IAAI,EAAE;AACjC,OAAE,MAAK;AACH,QAAA,IAAI,CAACvB,IAAG,GAAIU,aAAa,CAAC,KAAK,EAAE;AAC7BlB,UAAAA,KAAK,EAAE;AACHmB,YAAAA,QAAQ,EAAE,UAAU;AACpBC,YAAAA,GAAG,EAAE,GAAG;AACRC,YAAAA,IAAI,EAAE,GAAG;AACTC,YAAAA,KAAK,EAAE,MAAM;AACbC,YAAAA,MAAM,EAAE;WACX;AACD,UAAA,OAAA,EAAO,CAAC,IAAI,CAACC,UAAW,IAAGP,UAAU;AACrC,UAAA,QAAQ,EAAE,IAAI,CAACQ,GAAG,CAAC,MAAM;AAC7B,SAAC,CAAC;QACF,IAAI,CAACO,KAAK,CAACC,SAAS,CAACL,WAAW,CAAC,IAAI,CAACpB,IAAI,CAAC;AAC/C;MAEA,IAAI,IAAI,CAACT,UAAU,EAAE;QACjBmC,MAAM,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC3B,IAAI,EAAE,IAAI,CAACX,UAAW,GAAE,IAAI,CAACuC,SAAS,CAACC,MAAM,CAACC,MAAM,CAACC,KAAK,CAAC;AACxF;MAEA,IAAI,CAAC7B,SAAQ,GAAI,IAAI;AACrB,MAAA,IAAI,CAAC8B,KAAK,CAAC,OAAO,CAAC;KACtB;IACD1B,OAAO,EAAA,SAAPA,OAAOA,GAAG;AAAA,MAAA,IAAA2B,KAAA,GAAA,IAAA;MACN,IAAI,IAAI,CAACjC,IAAI,EAAE;QACX,CAAC,IAAI,CAACgB,UAAS,IAAKkB,QAAQ,CAAC,IAAI,CAAClC,IAAI,EAAE,sBAAsB,CAAC;AAE/D,QAAA,IAAMmC,mBAAiB,GAAI,SAArBA,kBAAiBA,GAAU;UAC7BC,YAAY,CAACC,aAAa,CAAC;UAC3BJ,KAAI,CAACjC,IAAI,CAACsC,mBAAmB,CAAC,cAAc,EAAEH,mBAAkB,CAAC;UACjEF,KAAI,CAACjC,IAAI,CAACsC,mBAAmB,CAAC,oBAAoB,EAAEH,mBAAkB,CAAC;SAC1E;AAED,QAAA,IAAME,aAAc,GAAEE,UAAU,CAAC,YAAM;UACnCN,KAAI,CAACO,UAAU,EAAE;SACpB,EAAE,EAAE,CAAC;QAEN,IAAIC,eAAe,CAAC,IAAI,CAACzC,IAAI,CAAE,GAAE,CAAC,EAAE;UAChC,IAAI,CAACA,IAAI,CAAC0C,gBAAgB,CAAC,cAAc,EAAEP,mBAAkB,CAAC;UAC9D,IAAI,CAACnC,IAAI,CAAC0C,gBAAgB,CAAC,oBAAoB,EAAEP,mBAAkB,CAAC;AACxE;AACJ,OAAE,MAAK;QACH,IAAI,CAACK,UAAU,EAAE;AACrB;KACH;IACDA,UAAU,EAAA,SAAVA,UAAUA,GAAG;AACTd,MAAAA,MAAM,CAACiB,KAAK,CAAC,IAAI,CAAC3C,IAAI,CAAC;MAEvB,IAAI,IAAI,CAACZ,UAAU,EAAE;QACjB8B,QAAQ,CAACC,IAAI,CAACyB,WAAW,CAAC,IAAI,CAAC5C,IAAI,CAAC;AACpC6C,QAAAA,iBAAiB,EAAE;AACvB,OAAE,MAAK;AAAA,QAAA,IAAAC,qBAAA;AACH,QAAA,CAAAA,qBAAA,GAAI,IAAA,CAACtB,KAAK,CAACC,SAAS,MAAAqB,IAAAA,IAAAA,qBAAA,KAApBA,MAAAA,IAAAA,qBAAA,CAAsBF,WAAW,CAAC,IAAI,CAAC5C,IAAI,CAAC;AAChD;MAEA,IAAI,CAACE,SAAU,GAAE,KAAK;AACtB,MAAA,IAAI,CAAC8B,KAAK,CAAC,SAAS,CAAC;AACzB;AACJ;AACJ,CAAC;;;;ECjHG,OAAAe,SAAA,EAAA,EAAAC,kBAAA,CAEK,OAFLC,UAEK,CAAA;AAFAC,IAAAA,GAAG,EAAC,WAAU;AAAG,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;IAAW,WAAS,EAAEC,KAAS,CAAAnD;KAAUiD,IAAI,CAAAG,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACxEC,UAAY,CAAAJ,IAAA,CAAAK,MAAA,EAAA,SAAA,CAAA;;;;;;;"}