/*
  @license
	Rollup.js v4.41.1
	Sat, 24 May 2025 06:13:57 GMT - commit 7c469dc4eb8e1cb6def9fdc04581fdfce9975da3

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

require('./native.js');
const parseAst_js = require('./shared/parseAst.js');
require('node:path');



exports.parseAst = parseAst_js.parseAst;
exports.parseAstAsync = parseAst_js.parseAstAsync;
//# sourceMappingURL=parseAst.js.map
