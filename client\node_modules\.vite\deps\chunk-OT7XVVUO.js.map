{"version": 3, "sources": ["../../src/focustrap/style/FocusTrapStyle.js", "../../src/focustrap/BaseFocusTrap.js", "../../src/focustrap/FocusTrap.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nexport default BaseStyle.extend({\n    name: 'focustrap-directive'\n});\n", "import BaseDirective from '@primevue/core/basedirective';\nimport FocusTrapStyle from 'primevue/focustrap/style';\n\nconst BaseFocusTrap = BaseDirective.extend({\n    style: FocusTrapStyle\n});\n\nexport default BaseFocusTrap;\n", "import { createElement, focus, getFirstFocusableElement, getLastFocusableElement, isFocusableElement } from '@primeuix/utils/dom';\nimport { isNotEmpty } from '@primeuix/utils/object';\nimport BaseFocusTrap from './BaseFocusTrap';\n\nconst FocusTrap = BaseFocusTrap.extend('focustrap', {\n    mounted(el, binding) {\n        const { disabled } = binding.value || {};\n\n        if (!disabled) {\n            this.createHiddenFocusableElements(el, binding);\n            this.bind(el, binding);\n            this.autoElementFocus(el, binding);\n        }\n\n        el.setAttribute('data-pd-focustrap', true);\n\n        this.$el = el;\n    },\n    updated(el, binding) {\n        const { disabled } = binding.value || {};\n\n        disabled && this.unbind(el);\n    },\n    unmounted(el) {\n        this.unbind(el);\n    },\n    methods: {\n        getComputedSelector(selector) {\n            return `:not(.p-hidden-focusable):not([data-p-hidden-focusable=\"true\"])${selector ?? ''}`;\n        },\n        bind(el, binding) {\n            const { onFocusIn, onFocusOut } = binding.value || {};\n\n            el.$_pfocustrap_mutationobserver = new MutationObserver((mutationList) => {\n                mutationList.forEach((mutation) => {\n                    if (mutation.type === 'childList' && !el.contains(document.activeElement)) {\n                        const findNextFocusableElement = (_el) => {\n                            const focusableElement = isFocusableElement(_el)\n                                ? isFocusableElement(_el, this.getComputedSelector(el.$_pfocustrap_focusableselector))\n                                    ? _el\n                                    : getFirstFocusableElement(el, this.getComputedSelector(el.$_pfocustrap_focusableselector))\n                                : getFirstFocusableElement(_el);\n\n                            return isNotEmpty(focusableElement) ? focusableElement : _el.nextSibling && findNextFocusableElement(_el.nextSibling);\n                        };\n\n                        focus(findNextFocusableElement(mutation.nextSibling));\n                    }\n                });\n            });\n\n            el.$_pfocustrap_mutationobserver.disconnect();\n            el.$_pfocustrap_mutationobserver.observe(el, {\n                childList: true\n            });\n\n            el.$_pfocustrap_focusinlistener = (event) => onFocusIn && onFocusIn(event);\n            el.$_pfocustrap_focusoutlistener = (event) => onFocusOut && onFocusOut(event);\n\n            el.addEventListener('focusin', el.$_pfocustrap_focusinlistener);\n            el.addEventListener('focusout', el.$_pfocustrap_focusoutlistener);\n        },\n        unbind(el) {\n            el.$_pfocustrap_mutationobserver && el.$_pfocustrap_mutationobserver.disconnect();\n            el.$_pfocustrap_focusinlistener && el.removeEventListener('focusin', el.$_pfocustrap_focusinlistener) && (el.$_pfocustrap_focusinlistener = null);\n            el.$_pfocustrap_focusoutlistener && el.removeEventListener('focusout', el.$_pfocustrap_focusoutlistener) && (el.$_pfocustrap_focusoutlistener = null);\n        },\n        autoFocus(options) {\n            this.autoElementFocus(this.$el, { value: { ...options, autoFocus: true } });\n        },\n        autoElementFocus(el, binding) {\n            const { autoFocusSelector = '', firstFocusableSelector = '', autoFocus = false } = binding.value || {};\n            let focusableElement = getFirstFocusableElement(el, `[autofocus]${this.getComputedSelector(autoFocusSelector)}`);\n\n            autoFocus && !focusableElement && (focusableElement = getFirstFocusableElement(el, this.getComputedSelector(firstFocusableSelector)));\n            focus(focusableElement);\n        },\n        onFirstHiddenElementFocus(event) {\n            const { currentTarget, relatedTarget } = event;\n            const focusableElement =\n                relatedTarget === currentTarget.$_pfocustrap_lasthiddenfocusableelement || !this.$el?.contains(relatedTarget)\n                    ? getFirstFocusableElement(currentTarget.parentElement, this.getComputedSelector(currentTarget.$_pfocustrap_focusableselector))\n                    : currentTarget.$_pfocustrap_lasthiddenfocusableelement;\n\n            focus(focusableElement);\n        },\n        onLastHiddenElementFocus(event) {\n            const { currentTarget, relatedTarget } = event;\n            const focusableElement =\n                relatedTarget === currentTarget.$_pfocustrap_firsthiddenfocusableelement || !this.$el?.contains(relatedTarget)\n                    ? getLastFocusableElement(currentTarget.parentElement, this.getComputedSelector(currentTarget.$_pfocustrap_focusableselector))\n                    : currentTarget.$_pfocustrap_firsthiddenfocusableelement;\n\n            focus(focusableElement);\n        },\n        createHiddenFocusableElements(el, binding) {\n            const { tabIndex = 0, firstFocusableSelector = '', lastFocusableSelector = '' } = binding.value || {};\n\n            const createFocusableElement = (onFocus) => {\n                return createElement('span', {\n                    class: 'p-hidden-accessible p-hidden-focusable',\n                    tabIndex,\n                    role: 'presentation',\n                    'aria-hidden': true,\n                    'data-p-hidden-accessible': true,\n                    'data-p-hidden-focusable': true,\n                    onFocus: onFocus?.bind(this)\n                });\n            };\n\n            const firstFocusableElement = createFocusableElement(this.onFirstHiddenElementFocus);\n            const lastFocusableElement = createFocusableElement(this.onLastHiddenElementFocus);\n\n            firstFocusableElement.$_pfocustrap_lasthiddenfocusableelement = lastFocusableElement;\n            firstFocusableElement.$_pfocustrap_focusableselector = firstFocusableSelector;\n            firstFocusableElement.setAttribute('data-pc-section', 'firstfocusableelement');\n\n            lastFocusableElement.$_pfocustrap_firsthiddenfocusableelement = firstFocusableElement;\n            lastFocusableElement.$_pfocustrap_focusableselector = lastFocusableSelector;\n            lastFocusableElement.setAttribute('data-pc-section', 'lastfocusableelement');\n\n            el.prepend(firstFocusableElement);\n            el.append(lastFocusableElement);\n        }\n    }\n});\n\nexport default FocusTrap;\n"], "mappings": ";;;;;;;;;;;;;;AAEA,IAAA,iBAAeA,UAAUC,OAAO;EAC5BC,MAAM;AACV,CAAC;;;ACDD,IAAMC,gBAAgBC,cAAcC,OAAO;EACvCC,OAAOC;AACX,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACDD,IAAMC,YAAYL,cAAcE,OAAO,aAAa;EAChDI,SAAO,SAAPA,QAAQC,IAAIC,SAAS;AACjB,QAAAC,OAAqBD,QAAQE,SAAS,CAAA,GAA9BC,WAAQF,KAARE;AAER,QAAI,CAACA,UAAU;AACX,WAAKC,8BAA8BL,IAAIC,OAAO;AAC9C,WAAKK,KAAKN,IAAIC,OAAO;AACrB,WAAKM,iBAAiBP,IAAIC,OAAO;IACrC;AAEAD,OAAGQ,aAAa,qBAAqB,IAAI;AAEzC,SAAKC,MAAMT;;EAEfU,SAAO,SAAPA,QAAQV,IAAIC,SAAS;AACjB,QAAAU,QAAqBV,QAAQE,SAAS,CAAA,GAA9BC,WAAQO,MAARP;AAERA,gBAAY,KAAKQ,OAAOZ,EAAE;;EAE9Ba,WAAAA,SAAAA,UAAUb,IAAI;AACV,SAAKY,OAAOZ,EAAE;;EAElBc,SAAS;IACLC,qBAAAA,SAAAA,oBAAoBC,UAAU;AAC1B,aAAAC,kEAAAA,OAAyED,aAAQ,QAARA,aAAAA,SAAAA,WAAY,EAAE;;IAE3FV,MAAI,SAAJA,KAAKN,IAAIC,SAAS;AAAA,UAAAiB,QAAA;AACd,UAAAC,QAAkClB,QAAQE,SAAS,CAAA,GAA3CiB,YAASD,MAATC,WAAWC,aAAUF,MAAVE;AAEnBrB,SAAGsB,gCAAgC,IAAIC,iBAAiB,SAACC,cAAiB;AACtEA,qBAAaC,QAAQ,SAACC,UAAa;AAC/B,cAAIA,SAASC,SAAS,eAAe,CAAC3B,GAAG4B,SAASC,SAASC,aAAa,GAAG;AACvE,gBAAMC,4BAA2B,SAA3BA,yBAA4BC,KAAQ;AACtC,kBAAMC,mBAAmBC,mBAAmBF,GAAG,IACzCE,mBAAmBF,KAAKd,MAAKH,oBAAoBf,GAAGmC,8BAA8B,CAAC,IAC/EH,MACAI,yBAAyBpC,IAAIkB,MAAKH,oBAAoBf,GAAGmC,8BAA8B,CAAC,IAC5FC,yBAAyBJ,GAAG;AAElC,qBAAOK,WAAWJ,gBAAgB,IAAIA,mBAAmBD,IAAIM,eAAeP,0BAAyBC,IAAIM,WAAW;;AAGxHC,kBAAMR,0BAAyBL,SAASY,WAAW,CAAC;UACxD;QACJ,CAAC;MACL,CAAC;AAEDtC,SAAGsB,8BAA8BkB,WAAU;AAC3CxC,SAAGsB,8BAA8BmB,QAAQzC,IAAI;QACzC0C,WAAW;MACf,CAAC;AAED1C,SAAG2C,+BAA+B,SAACC,OAAK;AAAA,eAAKxB,aAAaA,UAAUwB,KAAK;MAAC;AAC1E5C,SAAG6C,gCAAgC,SAACD,OAAK;AAAA,eAAKvB,cAAcA,WAAWuB,KAAK;MAAC;AAE7E5C,SAAG8C,iBAAiB,WAAW9C,GAAG2C,4BAA4B;AAC9D3C,SAAG8C,iBAAiB,YAAY9C,GAAG6C,6BAA6B;;IAEpEjC,QAAAA,SAAAA,OAAOZ,IAAI;AACPA,SAAGsB,iCAAiCtB,GAAGsB,8BAA8BkB,WAAU;AAC/ExC,SAAG2C,gCAAgC3C,GAAG+C,oBAAoB,WAAW/C,GAAG2C,4BAA4B,MAAM3C,GAAG2C,+BAA+B;AAC5I3C,SAAG6C,iCAAiC7C,GAAG+C,oBAAoB,YAAY/C,GAAG6C,6BAA6B,MAAM7C,GAAG6C,gCAAgC;;IAEpJG,WAAAA,SAAAA,UAAUC,SAAS;AACf,WAAK1C,iBAAiB,KAAKE,KAAK;QAAEN,OAAK+C,cAAAA,cAAA,CAAA,GAAOD,OAAO,GAAA,CAAA,GAAA;UAAED,WAAW;QAAI,CAAA;MAAG,CAAC;;IAE9EzC,kBAAgB,SAAhBA,iBAAiBP,IAAIC,SAAS;AAC1B,UAAAkD,QAAmFlD,QAAQE,SAAS,CAAA,GAAEiD,wBAAAD,MAA9FE,mBAAAA,oBAAiBD,0BAAG,SAAA,KAAEA,uBAAAE,wBAAAH,MAAEI,wBAAAA,yBAAsBD,0BAAG,SAAA,KAAEA,uBAAAE,kBAAAL,MAAEH,WAAAA,aAASQ,oBAAG,SAAA,QAAKA;AAC9E,UAAIvB,mBAAmBG,yBAAyBpC,IAAEiB,cAAAA,OAAgB,KAAKF,oBAAoBsC,iBAAiB,CAAC,CAAE;AAE/GL,MAAAA,cAAa,CAACf,qBAAqBA,mBAAmBG,yBAAyBpC,IAAI,KAAKe,oBAAoBwC,sBAAsB,CAAC;AACnIhB,YAAMN,gBAAgB;;IAE1BwB,2BAAAA,SAAAA,0BAA0Bb,OAAO;AAAA,UAAAc;AAC7B,UAAQC,gBAAiCf,MAAjCe,eAAeC,gBAAkBhB,MAAlBgB;AACvB,UAAM3B,mBACF2B,kBAAkBD,cAAcE,2CAA2C,GAAAH,YAAC,KAAKjD,SAAG,QAAAiD,cAARA,UAAAA,UAAU9B,SAASgC,aAAa,KACtGxB,yBAAyBuB,cAAcG,eAAe,KAAK/C,oBAAoB4C,cAAcxB,8BAA8B,CAAC,IAC5HwB,cAAcE;AAExBtB,YAAMN,gBAAgB;;IAE1B8B,0BAAAA,SAAAA,yBAAyBnB,OAAO;AAAA,UAAAoB;AAC5B,UAAQL,gBAAiCf,MAAjCe,eAAeC,gBAAkBhB,MAAlBgB;AACvB,UAAM3B,mBACF2B,kBAAkBD,cAAcM,4CAA4C,GAAAD,aAAC,KAAKvD,SAAG,QAAAuD,eAARA,UAAAA,WAAUpC,SAASgC,aAAa,KACvGM,wBAAwBP,cAAcG,eAAe,KAAK/C,oBAAoB4C,cAAcxB,8BAA8B,CAAC,IAC3HwB,cAAcM;AAExB1B,YAAMN,gBAAgB;;IAE1B5B,+BAA6B,SAA7BA,8BAA8BL,IAAIC,SAAS;AAAA,UAAAkE,SAAA;AACvC,UAAAC,QAAkFnE,QAAQE,SAAS,CAAA,GAAEkE,iBAAAD,MAA7FE,UAAAA,WAAQD,mBAAG,SAAA,IAACA,gBAAAE,wBAAAH,MAAEb,wBAAAA,yBAAsBgB,0BAAG,SAAA,KAAEA,uBAAAC,wBAAAJ,MAAEK,uBAAAA,wBAAqBD,0BAAG,SAAA,KAAEA;AAE7E,UAAME,yBAAyB,SAAzBA,wBAA0BC,SAAY;AACxC,eAAOC,cAAc,QAAQ;UACzB,SAAO;UACPN;UACAO,MAAM;UACN,eAAe;UACf,4BAA4B;UAC5B,2BAA2B;UAC3BF,SAASA,YAAAA,QAAAA,YAAO,SAAA,SAAPA,QAASrE,KAAK6D,MAAI;QAC/B,CAAC;;AAGL,UAAMW,wBAAwBJ,uBAAuB,KAAKjB,yBAAyB;AACnF,UAAMsB,uBAAuBL,uBAAuB,KAAKX,wBAAwB;AAEjFe,4BAAsBjB,0CAA0CkB;AAChED,4BAAsB3C,iCAAiCoB;AACvDuB,4BAAsBtE,aAAa,mBAAmB,uBAAuB;AAE7EuE,2BAAqBd,2CAA2Ca;AAChEC,2BAAqB5C,iCAAiCsC;AACtDM,2BAAqBvE,aAAa,mBAAmB,sBAAsB;AAE3ER,SAAGgF,QAAQF,qBAAqB;AAChC9E,SAAGiF,OAAOF,oBAAoB;IAClC;EACJ;AACJ,CAAC;", "names": ["BaseStyle", "extend", "name", "BaseFocusTrap", "BaseDirective", "extend", "style", "FocusTrapStyle", "FocusTrap", "mounted", "el", "binding", "_ref", "value", "disabled", "createHiddenFocusableElements", "bind", "autoElementFocus", "setAttribute", "$el", "updated", "_ref2", "unbind", "unmounted", "methods", "getComputedSelector", "selector", "concat", "_this", "_ref3", "onFocusIn", "onFocusOut", "$_pfocustrap_mutationobserver", "MutationObserver", "mutationList", "for<PERSON>ach", "mutation", "type", "contains", "document", "activeElement", "findNextFocusableElement", "_el", "focusableElement", "isFocusableElement", "$_pfocustrap_focusableselector", "getFirstFocusableElement", "isNotEmpty", "nextS<PERSON>ling", "focus", "disconnect", "observe", "childList", "$_pfocustrap_focusinlistener", "event", "$_pfocustrap_focusoutlistener", "addEventListener", "removeEventListener", "autoFocus", "options", "_objectSpread", "_ref4", "_ref4$autoFocusSelect", "autoFocusSelector", "_ref4$firstFocusableS", "firstFocusableSelector", "_ref4$autoFocus", "onFirstHiddenElementFocus", "_this$$el", "currentTarget", "relatedTarget", "$_pfocustrap_lasthiddenfocusableelement", "parentElement", "onLastHiddenElementFocus", "_this$$el2", "$_pfocustrap_firsthiddenfocusableelement", "getLastFocusableElement", "_this2", "_ref5", "_ref5$tabIndex", "tabIndex", "_ref5$firstFocusableS", "_ref5$lastFocusableSe", "lastFocusableSelector", "createFocusableElement", "onFocus", "createElement", "role", "firstFocusableElement", "lastFocusableElement", "prepend", "append"]}