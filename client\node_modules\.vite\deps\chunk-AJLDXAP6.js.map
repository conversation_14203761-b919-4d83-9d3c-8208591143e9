{"version": 3, "sources": ["../../src/portal/Portal.vue", "../../src/portal/Portal.vue"], "sourcesContent": ["<template>\n    <template v-if=\"inline\">\n        <slot></slot>\n    </template>\n    <template v-else-if=\"mounted\">\n        <Teleport :to=\"appendTo\">\n            <slot></slot>\n        </Teleport>\n    </template>\n</template>\n\n<script>\nimport { isClient } from '@primeuix/utils/dom';\n\nexport default {\n    name: 'Portal',\n    props: {\n        appendTo: {\n            type: [String, Object],\n            default: 'body'\n        },\n        disabled: {\n            type: Boolean,\n            default: false\n        }\n    },\n    data() {\n        return {\n            mounted: false\n        };\n    },\n    mounted() {\n        this.mounted = isClient();\n    },\n    computed: {\n        inline() {\n            return this.disabled || this.appendTo === 'self';\n        }\n    }\n};\n</script>\n", "<template>\n    <template v-if=\"inline\">\n        <slot></slot>\n    </template>\n    <template v-else-if=\"mounted\">\n        <Teleport :to=\"appendTo\">\n            <slot></slot>\n        </Teleport>\n    </template>\n</template>\n\n<script>\nimport { isClient } from '@primeuix/utils/dom';\n\nexport default {\n    name: 'Portal',\n    props: {\n        appendTo: {\n            type: [String, Object],\n            default: 'body'\n        },\n        disabled: {\n            type: Boolean,\n            default: false\n        }\n    },\n    data() {\n        return {\n            mounted: false\n        };\n    },\n    mounted() {\n        this.mounted = isClient();\n    },\n    computed: {\n        inline() {\n            return this.disabled || this.appendTo === 'self';\n        }\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;AAcA,IAAA,SAAe;EACXA,MAAM;EACNC,OAAO;IACHC,UAAU;MACNC,MAAM,CAACC,QAAQC,MAAM;MACrB,WAAS;;IAEbC,UAAU;MACNH,MAAMI;MACN,WAAS;IACb;;EAEJC,MAAI,SAAJA,OAAO;AACH,WAAO;MACHC,SAAS;;;EAGjBA,SAAO,SAAPA,UAAU;AACN,SAAKA,UAAUC,SAAQ;;EAE3BC,UAAU;IACNC,QAAM,SAANA,SAAS;AACL,aAAO,KAAKN,YAAY,KAAKJ,aAAa;IAC9C;EACJ;AACJ;;SCtCoBW,SAAMD,SAClBE,WAAYC,KAAAC,QAAA,WAAA;IAAAC,KAAA;EAAA,CAAA,IAEKC,MAAOT,WAAA,UAAA,GACxBU,YAEUC,UAAA;;IAFCC,IAAIC,OAAQpB;MACnBY,WAAYC,KAAAC,QAAA,SAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,mBAAA,IAAA,IAAA;;;", "names": ["name", "props", "appendTo", "type", "String", "Object", "disabled", "Boolean", "data", "mounted", "isClient", "computed", "inline", "$options", "_renderSlot", "_ctx", "$slots", "key", "$data", "_createBlock", "_Teleport", "to", "$props"]}