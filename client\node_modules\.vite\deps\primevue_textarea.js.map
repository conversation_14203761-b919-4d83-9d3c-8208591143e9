{"version": 3, "sources": ["../../src/textarea/style/TextareaStyle.js", "../../src/textarea/BaseTextarea.vue", "../../src/textarea/Textarea.vue", "../../src/textarea/Textarea.vue"], "sourcesContent": ["import { style } from '@primeuix/styles/textarea';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-textarea p-component',\n        {\n            'p-filled': instance.$filled,\n            'p-textarea-resizable ': props.autoResize,\n            'p-textarea-sm p-inputfield-sm': props.size === 'small',\n            'p-textarea-lg p-inputfield-lg': props.size === 'large',\n            'p-invalid': instance.$invalid,\n            'p-variant-filled': instance.$variant === 'filled',\n            'p-textarea-fluid': instance.$fluid\n        }\n    ]\n};\n\nexport default BaseStyle.extend({\n    name: 'textarea',\n    style,\n    classes\n});\n", "<script>\nimport BaseInput from '@primevue/core/baseinput';\nimport TextareaStyle from 'primevue/textarea/style';\n\nexport default {\n    name: 'BaseTextarea',\n    extends: BaseInput,\n    props: {\n        autoResize: Boolean\n    },\n    style: TextareaStyle,\n    provide() {\n        return {\n            $pcTextarea: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <textarea :class=\"cx('root')\" :value=\"d_value\" :name=\"name\" :disabled=\"disabled\" :aria-invalid=\"invalid || undefined\" :data-p=\"dataP\" @input=\"onInput\" v-bind=\"attrs\"></textarea>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { mergeProps } from 'vue';\nimport BaseTextarea from './BaseTextarea.vue';\n\nexport default {\n    name: 'Textarea',\n    extends: BaseTextarea,\n    inheritAttrs: false,\n    observer: null,\n    mounted() {\n        if (this.autoResize) {\n            this.observer = new ResizeObserver(() => {\n                // Firefox has issues without the requestAnimationFrame - ResizeObserver loop completed with undelivered notifications.\n                requestAnimationFrame(() => {\n                    this.resize();\n                });\n            });\n            this.observer.observe(this.$el);\n        }\n    },\n    updated() {\n        if (this.autoResize) {\n            this.resize();\n        }\n    },\n    beforeUnmount() {\n        if (this.observer) {\n            this.observer.disconnect();\n        }\n    },\n    methods: {\n        resize() {\n            if (!this.$el.offsetParent) return;\n\n            this.$el.style.height = 'auto';\n            this.$el.style.height = this.$el.scrollHeight + 'px';\n\n            if (parseFloat(this.$el.style.height) >= parseFloat(this.$el.style.maxHeight)) {\n                this.$el.style.overflowY = 'scroll';\n                this.$el.style.height = this.$el.style.maxHeight;\n            } else {\n                this.$el.style.overflow = 'hidden';\n            }\n        },\n        onInput(event) {\n            if (this.autoResize) {\n                this.resize();\n            }\n\n            this.writeValue(event.target.value, event);\n        }\n    },\n    computed: {\n        attrs() {\n            return mergeProps(\n                this.ptmi('root', {\n                    context: {\n                        filled: this.$filled,\n                        disabled: this.disabled\n                    }\n                }),\n                this.formField\n            );\n        },\n        dataP() {\n            return cn({\n                invalid: this.$invalid,\n                fluid: this.$fluid,\n                filled: this.$variant === 'filled',\n                [this.size]: this.size\n            });\n        }\n    }\n};\n</script>\n", "<template>\n    <textarea :class=\"cx('root')\" :value=\"d_value\" :name=\"name\" :disabled=\"disabled\" :aria-invalid=\"invalid || undefined\" :data-p=\"dataP\" @input=\"onInput\" v-bind=\"attrs\"></textarea>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { mergeProps } from 'vue';\nimport BaseTextarea from './BaseTextarea.vue';\n\nexport default {\n    name: 'Textarea',\n    extends: BaseTextarea,\n    inheritAttrs: false,\n    observer: null,\n    mounted() {\n        if (this.autoResize) {\n            this.observer = new ResizeObserver(() => {\n                // Firefox has issues without the requestAnimationFrame - ResizeObserver loop completed with undelivered notifications.\n                requestAnimationFrame(() => {\n                    this.resize();\n                });\n            });\n            this.observer.observe(this.$el);\n        }\n    },\n    updated() {\n        if (this.autoResize) {\n            this.resize();\n        }\n    },\n    beforeUnmount() {\n        if (this.observer) {\n            this.observer.disconnect();\n        }\n    },\n    methods: {\n        resize() {\n            if (!this.$el.offsetParent) return;\n\n            this.$el.style.height = 'auto';\n            this.$el.style.height = this.$el.scrollHeight + 'px';\n\n            if (parseFloat(this.$el.style.height) >= parseFloat(this.$el.style.maxHeight)) {\n                this.$el.style.overflowY = 'scroll';\n                this.$el.style.height = this.$el.style.maxHeight;\n            } else {\n                this.$el.style.overflow = 'hidden';\n            }\n        },\n        onInput(event) {\n            if (this.autoResize) {\n                this.resize();\n            }\n\n            this.writeValue(event.target.value, event);\n        }\n    },\n    computed: {\n        attrs() {\n            return mergeProps(\n                this.ptmi('root', {\n                    context: {\n                        filled: this.$filled,\n                        disabled: this.disabled\n                    }\n                }),\n                this.formField\n            );\n        },\n        dataP() {\n            return cn({\n                invalid: this.$invalid,\n                fluid: this.$fluid,\n                filled: this.$variant === 'filled',\n                [this.size]: this.size\n            });\n        }\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAMA,UAAU;EACZC,MAAM,SAANA,KAAIC,MAAA;AAAA,QAAKC,WAAQD,KAARC,UAAUC,QAAKF,KAALE;AAAK,WAAO,CAC3B,0BACA;MACI,YAAYD,SAASE;MACrB,yBAAyBD,MAAME;MAC/B,iCAAiCF,MAAMG,SAAS;MAChD,iCAAiCH,MAAMG,SAAS;MAChD,aAAaJ,SAASK;MACtB,oBAAoBL,SAASM,aAAa;MAC1C,oBAAoBN,SAASO;IACjC,CAAC;EACJ;AACL;AAEA,IAAA,gBAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNC;EACAd;AACJ,CAAC;;;AClBD,IAAA,WAAe;EACXe,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,YAAYC;;EAEhBC,OAAOC;EACPC,SAAO,SAAPA,UAAU;AACN,WAAO;MACHC,aAAa;MACbC,iBAAiB;;EAEzB;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;ACRA,IAAAC,UAAe;EACXV,MAAM;EACN,WAASW;EACTC,cAAc;EACdC,UAAU;EACVC,SAAO,SAAPA,UAAU;AAAA,QAAAC,QAAA;AACN,QAAI,KAAKZ,YAAY;AACjB,WAAKU,WAAW,IAAIG,eAAe,WAAM;AAErCC,8BAAsB,WAAM;AACxBF,gBAAKG,OAAM;QACf,CAAC;MACL,CAAC;AACD,WAAKL,SAASM,QAAQ,KAAKC,GAAG;IAClC;;EAEJC,SAAO,SAAPA,UAAU;AACN,QAAI,KAAKlB,YAAY;AACjB,WAAKe,OAAM;IACf;;EAEJI,eAAa,SAAbA,gBAAgB;AACZ,QAAI,KAAKT,UAAU;AACf,WAAKA,SAASU,WAAU;IAC5B;;EAEJC,SAAS;IACLN,QAAM,SAANA,SAAS;AACL,UAAI,CAAC,KAAKE,IAAIK,aAAc;AAE5B,WAAKL,IAAIf,MAAMqB,SAAS;AACxB,WAAKN,IAAIf,MAAMqB,SAAS,KAAKN,IAAIO,eAAe;AAEhD,UAAIC,WAAW,KAAKR,IAAIf,MAAMqB,MAAM,KAAKE,WAAW,KAAKR,IAAIf,MAAMwB,SAAS,GAAG;AAC3E,aAAKT,IAAIf,MAAMyB,YAAY;AAC3B,aAAKV,IAAIf,MAAMqB,SAAS,KAAKN,IAAIf,MAAMwB;MAC3C,OAAO;AACH,aAAKT,IAAIf,MAAM0B,WAAW;MAC9B;;IAEJC,SAAAA,SAAAA,QAAQC,OAAO;AACX,UAAI,KAAK9B,YAAY;AACjB,aAAKe,OAAM;MACf;AAEA,WAAKgB,WAAWD,MAAME,OAAOC,OAAOH,KAAK;IAC7C;;EAEJI,UAAU;IACNC,OAAK,SAALA,QAAQ;AACJ,aAAOC,WACH,KAAKC,KAAK,QAAQ;QACdC,SAAS;UACLC,QAAQ,KAAKC;UACbC,UAAU,KAAKA;QACnB;MACJ,CAAC,GACD,KAAKC,SACT;;IAEJC,OAAK,SAALA,QAAQ;AACJ,aAAOC,GAAEC,gBAAA;QACLC,SAAS,KAAKC;QACdC,OAAO,KAAKC;QACZV,QAAQ,KAAKW,aAAa;SACzB,KAAKC,MAAO,KAAKA,IAAG,CACxB;IACL;EACJ;AACJ;;;AC7EI,SAAAC,UAAA,GAAAC,mBAAgL,YAAhLC,WAAgL;IAArK,SAAOC,KAAEC,GAAA,MAAA;IAAWvB,OAAOsB,KAAOE;IAAG5D,MAAM0D,KAAI1D;IAAG4C,UAAUc,KAAQd;IAAG,gBAAcc,KAAMT,WAAKY;IAAY,UAAQC,SAAKhB;IAAGd,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;aAAE8B,SAAO9B,WAAA8B,SAAA9B,QAAA+B,MAAAD,UAAAE,SAAA;;KAAUF,SAAKxB,KAAA,GAAA,MAAA,IAAA2B,UAAA;;;", "names": ["classes", "root", "_ref", "instance", "props", "$filled", "autoResize", "size", "$invalid", "$variant", "$fluid", "BaseStyle", "extend", "name", "style", "name", "BaseInput", "props", "autoResize", "Boolean", "style", "TextareaStyle", "provide", "$pcTextarea", "$parentInstance", "script", "BaseTextarea", "inheritAttrs", "observer", "mounted", "_this", "ResizeObserver", "requestAnimationFrame", "resize", "observe", "$el", "updated", "beforeUnmount", "disconnect", "methods", "offsetParent", "height", "scrollHeight", "parseFloat", "maxHeight", "overflowY", "overflow", "onInput", "event", "writeValue", "target", "value", "computed", "attrs", "mergeProps", "ptmi", "context", "filled", "$filled", "disabled", "formField", "dataP", "cn", "_defineProperty", "invalid", "$invalid", "fluid", "$fluid", "$variant", "size", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "d_value", "undefined", "$options", "apply", "arguments", "_hoisted_1"]}