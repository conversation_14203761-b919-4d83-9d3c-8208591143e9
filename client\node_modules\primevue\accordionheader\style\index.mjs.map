{"version": 3, "file": "index.mjs", "sources": ["../../../src/accordionheader/style/AccordionHeaderStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-accordionheader',\n    toggleicon: 'p-accordionheader-toggle-icon'\n};\n\nexport default BaseStyle.extend({\n    name: 'accordionheader',\n    classes\n});\n"], "names": ["classes", "root", "toggleicon", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,mBAAmB;AACzBC,EAAAA,UAAU,EAAE;AAChB,CAAC;AAED,2BAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,iBAAiB;AACvBL,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}