{"version": 3, "file": "index.mjs", "sources": ["../../src/badge/BaseBadge.vue", "../../src/badge/Badge.vue", "../../src/badge/Badge.vue?vue&type=template&id=5e946bdc&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport BadgeStyle from 'primevue/badge/style';\n\nexport default {\n    name: 'BaseBadge',\n    extends: BaseComponent,\n    props: {\n        value: {\n            type: [String, Number],\n            default: null\n        },\n        severity: {\n            type: String,\n            default: null\n        },\n        size: {\n            type: String,\n            default: null\n        }\n    },\n    style: BadgeStyle,\n    provide() {\n        return {\n            $pcBadge: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <span :class=\"cx('root')\" :data-p=\"dataP\" v-bind=\"ptmi('root')\">\n        <slot>{{ value }}</slot>\n    </span>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport BaseBadge from './BaseBadge.vue';\n\nexport default {\n    name: 'Badge',\n    extends: BaseBadge,\n    inheritAttrs: false,\n    computed: {\n        dataP() {\n            return cn({\n                circle: this.value != null && String(this.value).length === 1,\n                empty: this.value == null && !this.$slots.default,\n                [this.severity]: this.severity,\n                [this.size]: this.size\n            });\n        }\n    }\n};\n</script>\n", "<template>\n    <span :class=\"cx('root')\" :data-p=\"dataP\" v-bind=\"ptmi('root')\">\n        <slot>{{ value }}</slot>\n    </span>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport BaseBadge from './BaseBadge.vue';\n\nexport default {\n    name: 'Badge',\n    extends: BaseBadge,\n    inheritAttrs: false,\n    computed: {\n        dataP() {\n            return cn({\n                circle: this.value != null && String(this.value).length === 1,\n                empty: this.value == null && !this.$slots.default,\n                [this.severity]: this.severity,\n                [this.size]: this.size\n            });\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "value", "type", "String", "Number", "severity", "size", "style", "BadgeStyle", "provide", "$pcBadge", "$parentInstance", "BaseBadge", "inheritAttrs", "computed", "dataP", "cn", "_defineProperty", "circle", "length", "empty", "$slots", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "$options", "ptmi", "_renderSlot"], "mappings": ";;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,WAAW;AACjB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE;AACHC,MAAAA,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNH,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDG,IAAAA,IAAI,EAAE;AACFJ,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDI,EAAAA,KAAK,EAAEC,UAAU;EACjBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,QAAQ,EAAE,IAAI;AACdC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;AClBD,aAAe;AACXb,EAAAA,IAAI,EAAE,OAAO;AACb,EAAA,SAAA,EAASc,QAAS;AAClBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,QAAQ,EAAE;IACNC,KAAK,EAAA,SAALA,KAAKA,GAAG;AACJ,MAAA,OAAOC,EAAE,CAAAC,eAAA,CAAAA,eAAA,CAAA;AACLC,QAAAA,MAAM,EAAE,IAAI,CAACjB,KAAM,IAAG,IAAG,IAAKE,MAAM,CAAC,IAAI,CAACF,KAAK,CAAC,CAACkB,MAAO,KAAI,CAAC;QAC7DC,KAAK,EAAE,IAAI,CAACnB,KAAI,IAAK,IAAG,IAAK,CAAC,IAAI,CAACoB,MAAM,CAAA,SAAA;AAAQ,OAAA,EAChD,IAAI,CAAChB,QAAQ,EAAG,IAAI,CAACA,QAAQ,CAAA,EAC7B,IAAI,CAACC,IAAI,EAAG,IAAI,CAACA,IAAG,CACxB,CAAC;AACN;AACJ;AACJ,CAAC;;;;ECvBG,OAAAgB,SAAA,EAAA,EAAAC,kBAAA,CAEM,QAFNC,UAEM,CAAA;AAFC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;IAAW,QAAM,EAAEC,QAAK,CAAAZ;KAAUU,IAAI,CAAAG,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAClDC,UAAA,CAAuBJ,4BAAvB,YAAA;AAAA,IAAA,OAAuB,iCAAdA,IAAI,CAAAxB,KAAA,CAAA,EAAA,CAAA,CAAA;;;;;;;;"}