{"version": 3, "file": "index.mjs", "sources": ["../../src/animateonscroll/BaseAnimateOnScroll.js", "../../src/animateonscroll/AnimateOnScroll.js"], "sourcesContent": ["import BaseDirective from '@primevue/core/basedirective';\nimport AnimateOnScrollStyle from 'primevue/animateonscroll/style';\n\nconst BaseAnimateOnScroll = BaseDirective.extend({\n    style: AnimateOnScrollStyle\n});\n\nexport default BaseAnimateOnScroll;\n", "import { addClass, removeClass } from '@primeuix/utils/dom';\nimport BaseAnimateOnScroll from './BaseAnimateOnScroll';\n\nconst AnimateOnScroll = BaseAnimateOnScroll.extend('animateonscroll', {\n    created() {\n        this.$value = this.$value || {};\n        this.$el.style.opacity = this.$value.enterClass ? '0' : '';\n    },\n    mounted() {\n        this.$el.setAttribute('data-pd-animateonscroll', true);\n\n        this.bindIntersectionObserver();\n    },\n    unmounted() {\n        this.unbindAnimationEvents();\n        this.unbindIntersectionObserver();\n    },\n    observer: undefined,\n    resetObserver: undefined,\n    isObserverActive: false,\n    animationState: undefined,\n    animationEndListener: undefined,\n    methods: {\n        bindAnimationEvents() {\n            if (!this.animationEndListener) {\n                this.animationEndListener = () => {\n                    removeClass(this.$el, [this.$value.enterClass, this.$value.leaveClass]);\n                    !this.$modifiers.once && this.resetObserver.observe(this.$el);\n                    this.unbindAnimationEvents();\n                };\n\n                this.$el.addEventListener('animationend', this.animationEndListener);\n            }\n        },\n        bindIntersectionObserver() {\n            const { root, rootMargin, threshold = 0.5 } = this.$value;\n            const options = { root, rootMargin, threshold };\n\n            // States\n            this.observer = new IntersectionObserver(([entry]) => {\n                if (this.isObserverActive) {\n                    if (entry.boundingClientRect.top > 0) {\n                        entry.isIntersecting ? this.enter() : this.leave();\n                    }\n                } else if (entry.isIntersecting) {\n                    this.enter();\n                }\n\n                this.isObserverActive = true;\n            }, options);\n\n            setTimeout(() => this.observer.observe(this.$el), 0);\n\n            // Reset\n            this.resetObserver = new IntersectionObserver(\n                ([entry]) => {\n                    if (entry.boundingClientRect.top > 0 && !entry.isIntersecting) {\n                        this.$el.style.opacity = this.$value.enterClass ? '0' : '';\n                        removeClass(this.$el, [this.$value.enterClass, this.$value.leaveClass]);\n\n                        this.resetObserver.unobserve(this.$el);\n                    }\n\n                    this.animationState = undefined;\n                },\n                { ...options, threshold: 0 }\n            );\n        },\n        enter() {\n            if (this.animationState !== 'enter' && this.$value.enterClass) {\n                this.$el.style.opacity = '';\n                removeClass(this.$el, this.$value.leaveClass);\n                addClass(this.$el, this.$value.enterClass);\n\n                this.$modifiers.once && this.unbindIntersectionObserver(this.$el);\n\n                this.bindAnimationEvents();\n                this.animationState = 'enter';\n            }\n        },\n        leave() {\n            if (this.animationState !== 'leave' && this.$value.leaveClass) {\n                this.$el.style.opacity = this.$value.enterClass ? '0' : '';\n                removeClass(this.$el, this.$value.enterClass);\n                addClass(this.$el, this.$value.leaveClass);\n\n                this.bindAnimationEvents();\n                this.animationState = 'leave';\n            }\n        },\n        unbindAnimationEvents() {\n            if (this.animationEndListener) {\n                this.$el.removeEventListener('animationend', this.animationEndListener);\n                this.animationEndListener = undefined;\n            }\n        },\n        unbindIntersectionObserver() {\n            this.observer?.unobserve(this.$el);\n            this.resetObserver?.unobserve(this.$el);\n            this.isObserverActive = false;\n        }\n    }\n});\n\nexport default AnimateOnScroll;\n"], "names": ["BaseAnimateOnScroll", "BaseDirective", "extend", "style", "AnimateOnScrollStyle", "AnimateOnScroll", "created", "$value", "$el", "opacity", "enterClass", "mounted", "setAttribute", "bindIntersectionObserver", "unmounted", "unbindAnimationEvents", "unbindIntersectionObserver", "observer", "undefined", "resetObserver", "isObserverActive", "animationState", "animationEndListener", "methods", "bindAnimationEvents", "_this", "removeClass", "leaveClass", "$modifiers", "once", "observe", "addEventListener", "_this2", "_this$$value", "root", "rootMargin", "_this$$value$threshol", "threshold", "options", "IntersectionObserver", "_ref", "_ref2", "_slicedToArray", "entry", "boundingClientRect", "top", "isIntersecting", "enter", "leave", "setTimeout", "_ref3", "_ref4", "unobserve", "_objectSpread", "addClass", "removeEventListener", "_this$observer", "_this$resetObserver"], "mappings": ";;;;AAGA,IAAMA,mBAAmB,GAAGC,aAAa,CAACC,MAAM,CAAC;AAC7CC,EAAAA,KAAK,EAAEC;AACX,CAAC,CAAC;;;;;;;;;;;;;;ACFF,IAAMC,eAAe,GAAGL,mBAAmB,CAACE,MAAM,CAAC,iBAAiB,EAAE;EAClEI,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,CAACC,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,EAAE;AAC/B,IAAA,IAAI,CAACC,GAAG,CAACL,KAAK,CAACM,OAAO,GAAG,IAAI,CAACF,MAAM,CAACG,UAAU,GAAG,GAAG,GAAG,EAAE;GAC7D;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,CAACH,GAAG,CAACI,YAAY,CAAC,yBAAyB,EAAE,IAAI,CAAC;IAEtD,IAAI,CAACC,wBAAwB,EAAE;GAClC;EACDC,SAAS,EAAA,SAATA,SAASA,GAAG;IACR,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,0BAA0B,EAAE;GACpC;AACDC,EAAAA,QAAQ,EAAEC,SAAS;AACnBC,EAAAA,aAAa,EAAED,SAAS;AACxBE,EAAAA,gBAAgB,EAAE,KAAK;AACvBC,EAAAA,cAAc,EAAEH,SAAS;AACzBI,EAAAA,oBAAoB,EAAEJ,SAAS;AAC/BK,EAAAA,OAAO,EAAE;IACLC,mBAAmB,EAAA,SAAnBA,mBAAmBA,GAAG;AAAA,MAAA,IAAAC,KAAA,GAAA,IAAA;AAClB,MAAA,IAAI,CAAC,IAAI,CAACH,oBAAoB,EAAE;QAC5B,IAAI,CAACA,oBAAoB,GAAG,YAAM;AAC9BI,UAAAA,WAAW,CAACD,KAAI,CAACjB,GAAG,EAAE,CAACiB,KAAI,CAAClB,MAAM,CAACG,UAAU,EAAEe,KAAI,CAAClB,MAAM,CAACoB,UAAU,CAAC,CAAC;AACvE,UAAA,CAACF,KAAI,CAACG,UAAU,CAACC,IAAI,IAAIJ,KAAI,CAACN,aAAa,CAACW,OAAO,CAACL,KAAI,CAACjB,GAAG,CAAC;UAC7DiB,KAAI,CAACV,qBAAqB,EAAE;SAC/B;QAED,IAAI,CAACP,GAAG,CAACuB,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACT,oBAAoB,CAAC;AACxE;KACH;IACDT,wBAAwB,EAAA,SAAxBA,wBAAwBA,GAAG;AAAA,MAAA,IAAAmB,MAAA,GAAA,IAAA;AACvB,MAAA,IAAAC,YAAA,GAA8C,IAAI,CAAC1B,MAAM;QAAjD2B,IAAI,GAAAD,YAAA,CAAJC,IAAI;QAAEC,UAAU,GAAAF,YAAA,CAAVE,UAAU;QAAAC,qBAAA,GAAAH,YAAA,CAAEI,SAAS;AAATA,QAAAA,SAAS,GAAAD,qBAAA,KAAG,MAAA,GAAA,GAAG,GAAAA,qBAAA;AACzC,MAAA,IAAME,OAAO,GAAG;AAAEJ,QAAAA,IAAI,EAAJA,IAAI;AAAEC,QAAAA,UAAU,EAAVA,UAAU;AAAEE,QAAAA,SAAS,EAATA;OAAW;;AAE/C;MACA,IAAI,CAACpB,QAAQ,GAAG,IAAIsB,oBAAoB,CAAC,UAAAC,IAAA,EAAa;AAAA,QAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA,EAAA,CAAA,CAAA;AAAXG,UAAAA,KAAK,GAAAF,KAAA,CAAA,CAAA,CAAA;QAC5C,IAAIT,MAAI,CAACZ,gBAAgB,EAAE;AACvB,UAAA,IAAIuB,KAAK,CAACC,kBAAkB,CAACC,GAAG,GAAG,CAAC,EAAE;AAClCF,YAAAA,KAAK,CAACG,cAAc,GAAGd,MAAI,CAACe,KAAK,EAAE,GAAGf,MAAI,CAACgB,KAAK,EAAE;AACtD;AACJ,SAAC,MAAM,IAAIL,KAAK,CAACG,cAAc,EAAE;UAC7Bd,MAAI,CAACe,KAAK,EAAE;AAChB;QAEAf,MAAI,CAACZ,gBAAgB,GAAG,IAAI;OAC/B,EAAEkB,OAAO,CAAC;AAEXW,MAAAA,UAAU,CAAC,YAAA;QAAA,OAAMjB,MAAI,CAACf,QAAQ,CAACa,OAAO,CAACE,MAAI,CAACxB,GAAG,CAAC;AAAA,OAAA,EAAE,CAAC,CAAC;;AAEpD;MACA,IAAI,CAACW,aAAa,GAAG,IAAIoB,oBAAoB,CACzC,UAAAW,KAAA,EAAa;AAAA,QAAA,IAAAC,KAAA,GAAAT,cAAA,CAAAQ,KAAA,EAAA,CAAA,CAAA;AAAXP,UAAAA,KAAK,GAAAQ,KAAA,CAAA,CAAA,CAAA;AACH,QAAA,IAAIR,KAAK,CAACC,kBAAkB,CAACC,GAAG,GAAG,CAAC,IAAI,CAACF,KAAK,CAACG,cAAc,EAAE;AAC3Dd,UAAAA,MAAI,CAACxB,GAAG,CAACL,KAAK,CAACM,OAAO,GAAGuB,MAAI,CAACzB,MAAM,CAACG,UAAU,GAAG,GAAG,GAAG,EAAE;AAC1DgB,UAAAA,WAAW,CAACM,MAAI,CAACxB,GAAG,EAAE,CAACwB,MAAI,CAACzB,MAAM,CAACG,UAAU,EAAEsB,MAAI,CAACzB,MAAM,CAACoB,UAAU,CAAC,CAAC;UAEvEK,MAAI,CAACb,aAAa,CAACiC,SAAS,CAACpB,MAAI,CAACxB,GAAG,CAAC;AAC1C;QAEAwB,MAAI,CAACX,cAAc,GAAGH,SAAS;AACnC,OAAC,EAAAmC,aAAA,CAAAA,aAAA,KACIf,OAAO,CAAA,EAAA,EAAA,EAAA;AAAED,QAAAA,SAAS,EAAE;AAAC,OAAA,CAC9B,CAAC;KACJ;IACDU,KAAK,EAAA,SAALA,KAAKA,GAAG;MACJ,IAAI,IAAI,CAAC1B,cAAc,KAAK,OAAO,IAAI,IAAI,CAACd,MAAM,CAACG,UAAU,EAAE;AAC3D,QAAA,IAAI,CAACF,GAAG,CAACL,KAAK,CAACM,OAAO,GAAG,EAAE;QAC3BiB,WAAW,CAAC,IAAI,CAAClB,GAAG,EAAE,IAAI,CAACD,MAAM,CAACoB,UAAU,CAAC;QAC7C2B,QAAQ,CAAC,IAAI,CAAC9C,GAAG,EAAE,IAAI,CAACD,MAAM,CAACG,UAAU,CAAC;AAE1C,QAAA,IAAI,CAACkB,UAAU,CAACC,IAAI,IAAI,IAAI,CAACb,0BAA0B,CAAC,IAAI,CAACR,GAAG,CAAC;QAEjE,IAAI,CAACgB,mBAAmB,EAAE;QAC1B,IAAI,CAACH,cAAc,GAAG,OAAO;AACjC;KACH;IACD2B,KAAK,EAAA,SAALA,KAAKA,GAAG;MACJ,IAAI,IAAI,CAAC3B,cAAc,KAAK,OAAO,IAAI,IAAI,CAACd,MAAM,CAACoB,UAAU,EAAE;AAC3D,QAAA,IAAI,CAACnB,GAAG,CAACL,KAAK,CAACM,OAAO,GAAG,IAAI,CAACF,MAAM,CAACG,UAAU,GAAG,GAAG,GAAG,EAAE;QAC1DgB,WAAW,CAAC,IAAI,CAAClB,GAAG,EAAE,IAAI,CAACD,MAAM,CAACG,UAAU,CAAC;QAC7C4C,QAAQ,CAAC,IAAI,CAAC9C,GAAG,EAAE,IAAI,CAACD,MAAM,CAACoB,UAAU,CAAC;QAE1C,IAAI,CAACH,mBAAmB,EAAE;QAC1B,IAAI,CAACH,cAAc,GAAG,OAAO;AACjC;KACH;IACDN,qBAAqB,EAAA,SAArBA,qBAAqBA,GAAG;MACpB,IAAI,IAAI,CAACO,oBAAoB,EAAE;QAC3B,IAAI,CAACd,GAAG,CAAC+C,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACjC,oBAAoB,CAAC;QACvE,IAAI,CAACA,oBAAoB,GAAGJ,SAAS;AACzC;KACH;IACDF,0BAA0B,EAAA,SAA1BA,0BAA0BA,GAAG;MAAA,IAAAwC,cAAA,EAAAC,mBAAA;AACzB,MAAA,CAAAD,cAAA,GAAA,IAAI,CAACvC,QAAQ,cAAAuC,cAAA,KAAA,MAAA,IAAbA,cAAA,CAAeJ,SAAS,CAAC,IAAI,CAAC5C,GAAG,CAAC;AAClC,MAAA,CAAAiD,mBAAA,GAAA,IAAI,CAACtC,aAAa,cAAAsC,mBAAA,KAAA,MAAA,IAAlBA,mBAAA,CAAoBL,SAAS,CAAC,IAAI,CAAC5C,GAAG,CAAC;MACvC,IAAI,CAACY,gBAAgB,GAAG,KAAK;AACjC;AACJ;AACJ,CAAC;;;;"}