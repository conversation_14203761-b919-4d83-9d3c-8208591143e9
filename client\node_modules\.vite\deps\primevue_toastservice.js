import {
  ToastEventBus
} from "./chunk-QABQFO3C.js";
import {
  PrimeVueToastSymbol
} from "./chunk-YXJHB5FJ.js";
import "./chunk-O5TNY3RC.js";
import "./chunk-CPDHMQQS.js";
import "./chunk-JVWSFFO4.js";

// node_modules/primevue/toastservice/index.mjs
var ToastService = {
  install: function install(app) {
    var ToastService2 = {
      add: function add(message) {
        ToastEventBus.emit("add", message);
      },
      remove: function remove(message) {
        ToastEventBus.emit("remove", message);
      },
      removeGroup: function removeGroup(group) {
        ToastEventBus.emit("remove-group", group);
      },
      removeAllGroups: function removeAllGroups() {
        ToastEventBus.emit("remove-all-groups");
      }
    };
    app.config.globalProperties.$toast = ToastService2;
    app.provide(PrimeVueToastSymbol, ToastService2);
  }
};
export {
  ToastService as default
};
//# sourceMappingURL=primevue_toastservice.js.map
