{"version": 3, "sources": ["../../src/menu/style/MenuStyle.js", "../../src/menu/BaseMenu.vue", "../../src/menu/Menuitem.vue", "../../src/menu/Menuitem.vue", "../../src/menu/Menu.vue", "../../src/menu/Menu.vue"], "sourcesContent": ["import { style } from '@primeuix/styles/menu';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ props }) => [\n        'p-menu p-component',\n        {\n            'p-menu-overlay': props.popup\n        }\n    ],\n    start: 'p-menu-start',\n    list: 'p-menu-list',\n    submenuLabel: 'p-menu-submenu-label',\n    separator: 'p-menu-separator',\n    end: 'p-menu-end',\n    item: ({ instance }) => [\n        'p-menu-item',\n        {\n            'p-focus': instance.id === instance.focusedOptionId,\n            'p-disabled': instance.disabled()\n        }\n    ],\n    itemContent: 'p-menu-item-content',\n    itemLink: 'p-menu-item-link',\n    itemIcon: 'p-menu-item-icon',\n    itemLabel: 'p-menu-item-label'\n};\n\nexport default BaseStyle.extend({\n    name: 'menu',\n    style,\n    classes\n});\n", "<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport MenuStyle from 'primevue/menu/style';\n\nexport default {\n    name: 'BaseMenu',\n    extends: BaseComponent,\n    props: {\n        popup: {\n            type: Boolean,\n            default: false\n        },\n        model: {\n            type: Array,\n            default: null\n        },\n        appendTo: {\n            type: [String, Object],\n            default: 'body'\n        },\n        autoZIndex: {\n            type: Boolean,\n            default: true\n        },\n        baseZIndex: {\n            type: Number,\n            default: 0\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        }\n    },\n    style: MenuStyle,\n    provide() {\n        return {\n            $pcMenu: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <li\n        v-if=\"visible()\"\n        :id=\"id\"\n        :class=\"[cx('item'), item.class]\"\n        role=\"menuitem\"\n        :style=\"item.style\"\n        :aria-label=\"label()\"\n        :aria-disabled=\"disabled()\"\n        :data-p-focused=\"isItemFocused()\"\n        :data-p-disabled=\"disabled() || false\"\n        :data-p=\"dataP\"\n        v-bind=\"getPTOptions('item')\"\n    >\n        <div :class=\"cx('itemContent')\" @click=\"onItemClick($event)\" @mousemove=\"onItemMouseMove($event)\" :data-p=\"dataP\" v-bind=\"getPTOptions('itemContent')\">\n            <template v-if=\"!templates.item\">\n                <a v-ripple :href=\"item.url\" :class=\"cx('itemLink')\" :target=\"item.target\" tabindex=\"-1\" v-bind=\"getPTOptions('itemLink')\">\n                    <component v-if=\"templates.itemicon\" :is=\"templates.itemicon\" :item=\"item\" :class=\"cx('itemIcon')\" />\n                    <span v-else-if=\"item.icon\" :class=\"[cx('itemIcon'), item.icon]\" :data-p=\"dataP\" v-bind=\"getPTOptions('itemIcon')\" />\n                    <span :class=\"cx('itemLabel')\" :data-p=\"dataP\" v-bind=\"getPTOptions('itemLabel')\">{{ label() }}</span>\n                </a>\n            </template>\n            <component v-else-if=\"templates.item\" :is=\"templates.item\" :item=\"item\" :label=\"label()\" :props=\"getMenuItemProps(item)\"></component>\n        </div>\n    </li>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { resolve } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'Menuitem',\n    hostName: 'Menu',\n    extends: BaseComponent,\n    inheritAttrs: false,\n    emits: ['item-click', 'item-mousemove'],\n    props: {\n        item: null,\n        templates: null,\n        id: null,\n        focusedOptionId: null,\n        index: null\n    },\n    methods: {\n        getItemProp(processedItem, name) {\n            return processedItem && processedItem.item ? resolve(processedItem.item[name]) : undefined;\n        },\n        getPTOptions(key) {\n            return this.ptm(key, {\n                context: {\n                    item: this.item,\n                    index: this.index,\n                    focused: this.isItemFocused(),\n                    disabled: this.disabled()\n                }\n            });\n        },\n        isItemFocused() {\n            return this.focusedOptionId === this.id;\n        },\n        onItemClick(event) {\n            const command = this.getItemProp(this.item, 'command');\n\n            command && command({ originalEvent: event, item: this.item.item });\n            this.$emit('item-click', { originalEvent: event, item: this.item, id: this.id });\n        },\n        onItemMouseMove(event) {\n            this.$emit('item-mousemove', { originalEvent: event, item: this.item, id: this.id });\n        },\n        visible() {\n            return typeof this.item.visible === 'function' ? this.item.visible() : this.item.visible !== false;\n        },\n        disabled() {\n            return typeof this.item.disabled === 'function' ? this.item.disabled() : this.item.disabled;\n        },\n        label() {\n            return typeof this.item.label === 'function' ? this.item.label() : this.item.label;\n        },\n        getMenuItemProps(item) {\n            return {\n                action: mergeProps(\n                    {\n                        class: this.cx('itemLink'),\n                        tabindex: '-1'\n                    },\n                    this.getPTOptions('itemLink')\n                ),\n                icon: mergeProps(\n                    {\n                        class: [this.cx('itemIcon'), item.icon]\n                    },\n                    this.getPTOptions('itemIcon')\n                ),\n                label: mergeProps(\n                    {\n                        class: this.cx('itemLabel')\n                    },\n                    this.getPTOptions('itemLabel')\n                )\n            };\n        }\n    },\n    computed: {\n        dataP() {\n            return cn({\n                focus: this.isItemFocused(),\n                disabled: this.disabled()\n            });\n        }\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <li\n        v-if=\"visible()\"\n        :id=\"id\"\n        :class=\"[cx('item'), item.class]\"\n        role=\"menuitem\"\n        :style=\"item.style\"\n        :aria-label=\"label()\"\n        :aria-disabled=\"disabled()\"\n        :data-p-focused=\"isItemFocused()\"\n        :data-p-disabled=\"disabled() || false\"\n        :data-p=\"dataP\"\n        v-bind=\"getPTOptions('item')\"\n    >\n        <div :class=\"cx('itemContent')\" @click=\"onItemClick($event)\" @mousemove=\"onItemMouseMove($event)\" :data-p=\"dataP\" v-bind=\"getPTOptions('itemContent')\">\n            <template v-if=\"!templates.item\">\n                <a v-ripple :href=\"item.url\" :class=\"cx('itemLink')\" :target=\"item.target\" tabindex=\"-1\" v-bind=\"getPTOptions('itemLink')\">\n                    <component v-if=\"templates.itemicon\" :is=\"templates.itemicon\" :item=\"item\" :class=\"cx('itemIcon')\" />\n                    <span v-else-if=\"item.icon\" :class=\"[cx('itemIcon'), item.icon]\" :data-p=\"dataP\" v-bind=\"getPTOptions('itemIcon')\" />\n                    <span :class=\"cx('itemLabel')\" :data-p=\"dataP\" v-bind=\"getPTOptions('itemLabel')\">{{ label() }}</span>\n                </a>\n            </template>\n            <component v-else-if=\"templates.item\" :is=\"templates.item\" :item=\"item\" :label=\"label()\" :props=\"getMenuItemProps(item)\"></component>\n        </div>\n    </li>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { resolve } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'Menuitem',\n    hostName: 'Menu',\n    extends: BaseComponent,\n    inheritAttrs: false,\n    emits: ['item-click', 'item-mousemove'],\n    props: {\n        item: null,\n        templates: null,\n        id: null,\n        focusedOptionId: null,\n        index: null\n    },\n    methods: {\n        getItemProp(processedItem, name) {\n            return processedItem && processedItem.item ? resolve(processedItem.item[name]) : undefined;\n        },\n        getPTOptions(key) {\n            return this.ptm(key, {\n                context: {\n                    item: this.item,\n                    index: this.index,\n                    focused: this.isItemFocused(),\n                    disabled: this.disabled()\n                }\n            });\n        },\n        isItemFocused() {\n            return this.focusedOptionId === this.id;\n        },\n        onItemClick(event) {\n            const command = this.getItemProp(this.item, 'command');\n\n            command && command({ originalEvent: event, item: this.item.item });\n            this.$emit('item-click', { originalEvent: event, item: this.item, id: this.id });\n        },\n        onItemMouseMove(event) {\n            this.$emit('item-mousemove', { originalEvent: event, item: this.item, id: this.id });\n        },\n        visible() {\n            return typeof this.item.visible === 'function' ? this.item.visible() : this.item.visible !== false;\n        },\n        disabled() {\n            return typeof this.item.disabled === 'function' ? this.item.disabled() : this.item.disabled;\n        },\n        label() {\n            return typeof this.item.label === 'function' ? this.item.label() : this.item.label;\n        },\n        getMenuItemProps(item) {\n            return {\n                action: mergeProps(\n                    {\n                        class: this.cx('itemLink'),\n                        tabindex: '-1'\n                    },\n                    this.getPTOptions('itemLink')\n                ),\n                icon: mergeProps(\n                    {\n                        class: [this.cx('itemIcon'), item.icon]\n                    },\n                    this.getPTOptions('itemIcon')\n                ),\n                label: mergeProps(\n                    {\n                        class: this.cx('itemLabel')\n                    },\n                    this.getPTOptions('itemLabel')\n                )\n            };\n        }\n    },\n    computed: {\n        dataP() {\n            return cn({\n                focus: this.isItemFocused(),\n                disabled: this.disabled()\n            });\n        }\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <Portal :appendTo=\"appendTo\" :disabled=\"!popup\">\n        <transition name=\"p-connected-overlay\" @enter=\"onEnter\" @leave=\"onLeave\" @after-leave=\"onAfterLeave\" v-bind=\"ptm('transition')\">\n            <div v-if=\"popup ? overlayVisible : true\" :ref=\"containerRef\" :id=\"$id\" :class=\"cx('root')\" @click=\"onOverlayClick\" :data-p=\"dataP\" v-bind=\"ptmi('root')\">\n                <div v-if=\"$slots.start\" :class=\"cx('start')\" v-bind=\"ptm('start')\">\n                    <slot name=\"start\"></slot>\n                </div>\n                <ul\n                    :ref=\"listRef\"\n                    :id=\"$id + '_list'\"\n                    :class=\"cx('list')\"\n                    role=\"menu\"\n                    :tabindex=\"tabindex\"\n                    :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n                    :aria-label=\"ariaLabel\"\n                    :aria-labelledby=\"ariaLabelledby\"\n                    @focus=\"onListFocus\"\n                    @blur=\"onListBlur\"\n                    @keydown=\"onListKeyDown\"\n                    v-bind=\"ptm('list')\"\n                >\n                    <template v-for=\"(item, i) of model\" :key=\"label(item) + i.toString()\">\n                        <template v-if=\"item.items && visible(item) && !item.separator\">\n                            <li v-if=\"item.items\" :id=\"$id + '_' + i\" :class=\"[cx('submenuLabel'), item.class]\" role=\"none\" v-bind=\"ptm('submenuLabel')\">\n                                <!--TODO: submenuheader deprecated since v4.0. Use submenulabel-->\n                                <slot :name=\"$slots.submenulabel ? 'submenulabel' : 'submenuheader'\" :item=\"item\">{{ label(item) }}</slot>\n                            </li>\n                            <template v-for=\"(child, j) of item.items\" :key=\"child.label + i + '_' + j\">\n                                <PVMenuitem\n                                    v-if=\"visible(child) && !child.separator\"\n                                    :id=\"$id + '_' + i + '_' + j\"\n                                    :item=\"child\"\n                                    :templates=\"$slots\"\n                                    :focusedOptionId=\"focusedOptionId\"\n                                    :unstyled=\"unstyled\"\n                                    @item-click=\"itemClick\"\n                                    @item-mousemove=\"itemMouseMove\"\n                                    :pt=\"pt\"\n                                />\n                                <li v-else-if=\"visible(child) && child.separator\" :key=\"'separator' + i + j\" :class=\"[cx('separator'), item.class]\" :style=\"child.style\" role=\"separator\" v-bind=\"ptm('separator')\"></li>\n                            </template>\n                        </template>\n                        <li v-else-if=\"visible(item) && item.separator\" :key=\"'separator' + i.toString()\" :class=\"[cx('separator'), item.class]\" :style=\"item.style\" role=\"separator\" v-bind=\"ptm('separator')\"></li>\n                        <PVMenuitem\n                            v-else\n                            :key=\"label(item) + i.toString()\"\n                            :id=\"$id + '_' + i\"\n                            :item=\"item\"\n                            :index=\"i\"\n                            :templates=\"$slots\"\n                            :focusedOptionId=\"focusedOptionId\"\n                            :unstyled=\"unstyled\"\n                            @item-click=\"itemClick\"\n                            @item-mousemove=\"itemMouseMove\"\n                            :pt=\"pt\"\n                        />\n                    </template>\n                </ul>\n                <div v-if=\"$slots.end\" :class=\"cx('end')\" v-bind=\"ptm('end')\">\n                    <slot name=\"end\"></slot>\n                </div>\n            </div>\n        </transition>\n    </Portal>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { absolutePosition, addStyle, find, findSingle, focus, getOuterWidth, isTouchDevice } from '@primeuix/utils/dom';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { ConnectedOverlayScrollHandler } from '@primevue/core/utils';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport BaseMenu from './BaseMenu.vue';\nimport Menuitem from './Menuitem.vue';\n\nexport default {\n    name: 'Menu',\n    extends: BaseMenu,\n    inheritAttrs: false,\n    emits: ['show', 'hide', 'focus', 'blur'],\n    data() {\n        return {\n            overlayVisible: false,\n            focused: false,\n            focusedOptionIndex: -1,\n            selectedOptionIndex: -1\n        };\n    },\n    target: null,\n    outsideClickListener: null,\n    scrollHandler: null,\n    resizeListener: null,\n    container: null,\n    list: null,\n    mounted() {\n        if (!this.popup) {\n            this.bindResizeListener();\n            this.bindOutsideClickListener();\n        }\n    },\n    beforeUnmount() {\n        this.unbindResizeListener();\n        this.unbindOutsideClickListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        this.target = null;\n\n        if (this.container && this.autoZIndex) {\n            ZIndex.clear(this.container);\n        }\n\n        this.container = null;\n    },\n    methods: {\n        itemClick(event) {\n            const item = event.item;\n\n            if (this.disabled(item)) {\n                return;\n            }\n\n            if (item.command) {\n                item.command(event);\n            }\n\n            if (this.overlayVisible) this.hide();\n\n            if (!this.popup && this.focusedOptionIndex !== event.id) {\n                this.focusedOptionIndex = event.id;\n            }\n        },\n        itemMouseMove(event) {\n            if (this.focused) {\n                this.focusedOptionIndex = event.id;\n            }\n        },\n        onListFocus(event) {\n            this.focused = true;\n            !this.popup && this.changeFocusedOptionIndex(0);\n\n            this.$emit('focus', event);\n        },\n        onListBlur(event) {\n            this.focused = false;\n            this.focusedOptionIndex = -1;\n            this.$emit('blur', event);\n        },\n        onListKeyDown(event) {\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event);\n                    break;\n\n                case 'Escape':\n                    if (this.popup) {\n                        focus(this.target);\n                        this.hide();\n                    }\n\n                case 'Tab':\n                    this.overlayVisible && this.hide();\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDownKey(event) {\n            const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex);\n\n            this.changeFocusedOptionIndex(optionIndex);\n            event.preventDefault();\n        },\n        onArrowUpKey(event) {\n            if (event.altKey && this.popup) {\n                focus(this.target);\n                this.hide();\n                event.preventDefault();\n            } else {\n                const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex);\n\n                this.changeFocusedOptionIndex(optionIndex);\n                event.preventDefault();\n            }\n        },\n        onHomeKey(event) {\n            this.changeFocusedOptionIndex(0);\n            event.preventDefault();\n        },\n        onEndKey(event) {\n            this.changeFocusedOptionIndex(find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]').length - 1);\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            const element = findSingle(this.list, `li[id=\"${`${this.focusedOptionIndex}`}\"]`);\n            const anchorElement = element && findSingle(element, 'a[data-pc-section=\"itemlink\"]');\n\n            this.popup && focus(this.target);\n            anchorElement ? anchorElement.click() : element && element.click();\n\n            event.preventDefault();\n        },\n        onSpaceKey(event) {\n            this.onEnterKey(event);\n        },\n        findNextOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            const matchedOptionIndex = [...links].findIndex((link) => link.id === index);\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;\n        },\n        findPrevOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            const matchedOptionIndex = [...links].findIndex((link) => link.id === index);\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;\n        },\n        changeFocusedOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            let order = index >= links.length ? links.length - 1 : index < 0 ? 0 : index;\n\n            order > -1 && (this.focusedOptionIndex = links[order].getAttribute('id'));\n        },\n        toggle(event, target) {\n            if (this.overlayVisible) this.hide();\n            else this.show(event, target);\n        },\n        show(event, target) {\n            this.overlayVisible = true;\n            this.target = target ?? event.currentTarget;\n        },\n        hide() {\n            this.overlayVisible = false;\n            this.target = null;\n        },\n        onEnter(el) {\n            addStyle(el, { position: 'absolute', top: '0' });\n            this.alignOverlay();\n            this.bindOutsideClickListener();\n            this.bindResizeListener();\n            this.bindScrollListener();\n\n            if (this.autoZIndex) {\n                ZIndex.set('menu', el, this.baseZIndex + this.$primevue.config.zIndex.menu);\n            }\n\n            if (this.popup) {\n                focus(this.list);\n            }\n\n            this.$emit('show');\n        },\n        onLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindResizeListener();\n            this.unbindScrollListener();\n            this.$emit('hide');\n        },\n        onAfterLeave(el) {\n            if (this.autoZIndex) {\n                ZIndex.clear(el);\n            }\n        },\n        alignOverlay() {\n            absolutePosition(this.container, this.target);\n            const targetWidth = getOuterWidth(this.target);\n\n            if (targetWidth > getOuterWidth(this.container)) {\n                this.container.style.minWidth = getOuterWidth(this.target) + 'px';\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = (event) => {\n                    const isOutsideContainer = this.container && !this.container.contains(event.target);\n                    const isOutsideTarget = !(this.target && (this.target === event.target || this.target.contains(event.target)));\n\n                    if (this.overlayVisible && isOutsideContainer && isOutsideTarget) {\n                        this.hide();\n                    } else if (!this.popup && isOutsideContainer && isOutsideTarget) {\n                        this.focusedOptionIndex = -1;\n                    }\n                };\n\n                document.addEventListener('click', this.outsideClickListener, true);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener, true);\n                this.outsideClickListener = null;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                    if (this.overlayVisible) {\n                        this.hide();\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = () => {\n                    if (this.overlayVisible && !isTouchDevice()) {\n                        this.hide();\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        visible(item) {\n            return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n        },\n        disabled(item) {\n            return typeof item.disabled === 'function' ? item.disabled() : item.disabled;\n        },\n        label(item) {\n            return typeof item.label === 'function' ? item.label() : item.label;\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.target\n            });\n        },\n        containerRef(el) {\n            this.container = el;\n        },\n        listRef(el) {\n            this.list = el;\n        }\n    },\n    computed: {\n        focusedOptionId() {\n            return this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : null;\n        },\n        dataP() {\n            return cn({\n                popup: this.popup\n            });\n        }\n    },\n    components: {\n        PVMenuitem: Menuitem,\n        Portal: Portal\n    }\n};\n</script>\n", "<template>\n    <Portal :appendTo=\"appendTo\" :disabled=\"!popup\">\n        <transition name=\"p-connected-overlay\" @enter=\"onEnter\" @leave=\"onLeave\" @after-leave=\"onAfterLeave\" v-bind=\"ptm('transition')\">\n            <div v-if=\"popup ? overlayVisible : true\" :ref=\"containerRef\" :id=\"$id\" :class=\"cx('root')\" @click=\"onOverlayClick\" :data-p=\"dataP\" v-bind=\"ptmi('root')\">\n                <div v-if=\"$slots.start\" :class=\"cx('start')\" v-bind=\"ptm('start')\">\n                    <slot name=\"start\"></slot>\n                </div>\n                <ul\n                    :ref=\"listRef\"\n                    :id=\"$id + '_list'\"\n                    :class=\"cx('list')\"\n                    role=\"menu\"\n                    :tabindex=\"tabindex\"\n                    :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n                    :aria-label=\"ariaLabel\"\n                    :aria-labelledby=\"ariaLabelledby\"\n                    @focus=\"onListFocus\"\n                    @blur=\"onListBlur\"\n                    @keydown=\"onListKeyDown\"\n                    v-bind=\"ptm('list')\"\n                >\n                    <template v-for=\"(item, i) of model\" :key=\"label(item) + i.toString()\">\n                        <template v-if=\"item.items && visible(item) && !item.separator\">\n                            <li v-if=\"item.items\" :id=\"$id + '_' + i\" :class=\"[cx('submenuLabel'), item.class]\" role=\"none\" v-bind=\"ptm('submenuLabel')\">\n                                <!--TODO: submenuheader deprecated since v4.0. Use submenulabel-->\n                                <slot :name=\"$slots.submenulabel ? 'submenulabel' : 'submenuheader'\" :item=\"item\">{{ label(item) }}</slot>\n                            </li>\n                            <template v-for=\"(child, j) of item.items\" :key=\"child.label + i + '_' + j\">\n                                <PVMenuitem\n                                    v-if=\"visible(child) && !child.separator\"\n                                    :id=\"$id + '_' + i + '_' + j\"\n                                    :item=\"child\"\n                                    :templates=\"$slots\"\n                                    :focusedOptionId=\"focusedOptionId\"\n                                    :unstyled=\"unstyled\"\n                                    @item-click=\"itemClick\"\n                                    @item-mousemove=\"itemMouseMove\"\n                                    :pt=\"pt\"\n                                />\n                                <li v-else-if=\"visible(child) && child.separator\" :key=\"'separator' + i + j\" :class=\"[cx('separator'), item.class]\" :style=\"child.style\" role=\"separator\" v-bind=\"ptm('separator')\"></li>\n                            </template>\n                        </template>\n                        <li v-else-if=\"visible(item) && item.separator\" :key=\"'separator' + i.toString()\" :class=\"[cx('separator'), item.class]\" :style=\"item.style\" role=\"separator\" v-bind=\"ptm('separator')\"></li>\n                        <PVMenuitem\n                            v-else\n                            :key=\"label(item) + i.toString()\"\n                            :id=\"$id + '_' + i\"\n                            :item=\"item\"\n                            :index=\"i\"\n                            :templates=\"$slots\"\n                            :focusedOptionId=\"focusedOptionId\"\n                            :unstyled=\"unstyled\"\n                            @item-click=\"itemClick\"\n                            @item-mousemove=\"itemMouseMove\"\n                            :pt=\"pt\"\n                        />\n                    </template>\n                </ul>\n                <div v-if=\"$slots.end\" :class=\"cx('end')\" v-bind=\"ptm('end')\">\n                    <slot name=\"end\"></slot>\n                </div>\n            </div>\n        </transition>\n    </Portal>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { absolutePosition, addStyle, find, findSingle, focus, getOuterWidth, isTouchDevice } from '@primeuix/utils/dom';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { ConnectedOverlayScrollHandler } from '@primevue/core/utils';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport BaseMenu from './BaseMenu.vue';\nimport Menuitem from './Menuitem.vue';\n\nexport default {\n    name: 'Menu',\n    extends: BaseMenu,\n    inheritAttrs: false,\n    emits: ['show', 'hide', 'focus', 'blur'],\n    data() {\n        return {\n            overlayVisible: false,\n            focused: false,\n            focusedOptionIndex: -1,\n            selectedOptionIndex: -1\n        };\n    },\n    target: null,\n    outsideClickListener: null,\n    scrollHandler: null,\n    resizeListener: null,\n    container: null,\n    list: null,\n    mounted() {\n        if (!this.popup) {\n            this.bindResizeListener();\n            this.bindOutsideClickListener();\n        }\n    },\n    beforeUnmount() {\n        this.unbindResizeListener();\n        this.unbindOutsideClickListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        this.target = null;\n\n        if (this.container && this.autoZIndex) {\n            ZIndex.clear(this.container);\n        }\n\n        this.container = null;\n    },\n    methods: {\n        itemClick(event) {\n            const item = event.item;\n\n            if (this.disabled(item)) {\n                return;\n            }\n\n            if (item.command) {\n                item.command(event);\n            }\n\n            if (this.overlayVisible) this.hide();\n\n            if (!this.popup && this.focusedOptionIndex !== event.id) {\n                this.focusedOptionIndex = event.id;\n            }\n        },\n        itemMouseMove(event) {\n            if (this.focused) {\n                this.focusedOptionIndex = event.id;\n            }\n        },\n        onListFocus(event) {\n            this.focused = true;\n            !this.popup && this.changeFocusedOptionIndex(0);\n\n            this.$emit('focus', event);\n        },\n        onListBlur(event) {\n            this.focused = false;\n            this.focusedOptionIndex = -1;\n            this.$emit('blur', event);\n        },\n        onListKeyDown(event) {\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event);\n                    break;\n\n                case 'Escape':\n                    if (this.popup) {\n                        focus(this.target);\n                        this.hide();\n                    }\n\n                case 'Tab':\n                    this.overlayVisible && this.hide();\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDownKey(event) {\n            const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex);\n\n            this.changeFocusedOptionIndex(optionIndex);\n            event.preventDefault();\n        },\n        onArrowUpKey(event) {\n            if (event.altKey && this.popup) {\n                focus(this.target);\n                this.hide();\n                event.preventDefault();\n            } else {\n                const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex);\n\n                this.changeFocusedOptionIndex(optionIndex);\n                event.preventDefault();\n            }\n        },\n        onHomeKey(event) {\n            this.changeFocusedOptionIndex(0);\n            event.preventDefault();\n        },\n        onEndKey(event) {\n            this.changeFocusedOptionIndex(find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]').length - 1);\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            const element = findSingle(this.list, `li[id=\"${`${this.focusedOptionIndex}`}\"]`);\n            const anchorElement = element && findSingle(element, 'a[data-pc-section=\"itemlink\"]');\n\n            this.popup && focus(this.target);\n            anchorElement ? anchorElement.click() : element && element.click();\n\n            event.preventDefault();\n        },\n        onSpaceKey(event) {\n            this.onEnterKey(event);\n        },\n        findNextOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            const matchedOptionIndex = [...links].findIndex((link) => link.id === index);\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;\n        },\n        findPrevOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            const matchedOptionIndex = [...links].findIndex((link) => link.id === index);\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;\n        },\n        changeFocusedOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            let order = index >= links.length ? links.length - 1 : index < 0 ? 0 : index;\n\n            order > -1 && (this.focusedOptionIndex = links[order].getAttribute('id'));\n        },\n        toggle(event, target) {\n            if (this.overlayVisible) this.hide();\n            else this.show(event, target);\n        },\n        show(event, target) {\n            this.overlayVisible = true;\n            this.target = target ?? event.currentTarget;\n        },\n        hide() {\n            this.overlayVisible = false;\n            this.target = null;\n        },\n        onEnter(el) {\n            addStyle(el, { position: 'absolute', top: '0' });\n            this.alignOverlay();\n            this.bindOutsideClickListener();\n            this.bindResizeListener();\n            this.bindScrollListener();\n\n            if (this.autoZIndex) {\n                ZIndex.set('menu', el, this.baseZIndex + this.$primevue.config.zIndex.menu);\n            }\n\n            if (this.popup) {\n                focus(this.list);\n            }\n\n            this.$emit('show');\n        },\n        onLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindResizeListener();\n            this.unbindScrollListener();\n            this.$emit('hide');\n        },\n        onAfterLeave(el) {\n            if (this.autoZIndex) {\n                ZIndex.clear(el);\n            }\n        },\n        alignOverlay() {\n            absolutePosition(this.container, this.target);\n            const targetWidth = getOuterWidth(this.target);\n\n            if (targetWidth > getOuterWidth(this.container)) {\n                this.container.style.minWidth = getOuterWidth(this.target) + 'px';\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = (event) => {\n                    const isOutsideContainer = this.container && !this.container.contains(event.target);\n                    const isOutsideTarget = !(this.target && (this.target === event.target || this.target.contains(event.target)));\n\n                    if (this.overlayVisible && isOutsideContainer && isOutsideTarget) {\n                        this.hide();\n                    } else if (!this.popup && isOutsideContainer && isOutsideTarget) {\n                        this.focusedOptionIndex = -1;\n                    }\n                };\n\n                document.addEventListener('click', this.outsideClickListener, true);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener, true);\n                this.outsideClickListener = null;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                    if (this.overlayVisible) {\n                        this.hide();\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = () => {\n                    if (this.overlayVisible && !isTouchDevice()) {\n                        this.hide();\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        visible(item) {\n            return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n        },\n        disabled(item) {\n            return typeof item.disabled === 'function' ? item.disabled() : item.disabled;\n        },\n        label(item) {\n            return typeof item.label === 'function' ? item.label() : item.label;\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.target\n            });\n        },\n        containerRef(el) {\n            this.container = el;\n        },\n        listRef(el) {\n            this.list = el;\n        }\n    },\n    computed: {\n        focusedOptionId() {\n            return this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : null;\n        },\n        dataP() {\n            return cn({\n                popup: this.popup\n            });\n        }\n    },\n    components: {\n        PVMenuitem: Menuitem,\n        Portal: Portal\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAMA,UAAU;EACZC,MAAM,SAANA,KAAIC,MAAA;AAAA,QAAKC,QAAKD,KAALC;AAAK,WAAO,CACjB,sBACA;MACI,kBAAkBA,MAAMC;IAC5B,CAAC;EACJ;EACDC,OAAO;EACPC,MAAM;EACNC,cAAc;EACdC,WAAW;EACXC,KAAK;EACLC,MAAM,SAANA,KAAIC,OAAA;AAAA,QAAKC,WAAQD,MAARC;AAAQ,WAAO,CACpB,eACA;MACI,WAAWA,SAASC,OAAOD,SAASE;MACpC,cAAcF,SAASG,SAAQ;IACnC,CAAC;EACJ;EACDC,aAAa;EACbC,UAAU;EACVC,UAAU;EACVC,WAAW;AACf;AAEA,IAAA,YAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNC;EACAvB;AACJ,CAAC;;;AC5BD,IAAA,WAAe;EACXwB,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,OAAO;MACHC,MAAMC;MACN,WAAS;;IAEbC,OAAO;MACHF,MAAMG;MACN,WAAS;;IAEbC,UAAU;MACNJ,MAAM,CAACK,QAAQC,MAAM;MACrB,WAAS;;IAEbC,YAAY;MACRP,MAAMC;MACN,WAAS;;IAEbO,YAAY;MACRR,MAAMS;MACN,WAAS;;IAEbC,UAAU;MACNV,MAAMS;MACN,WAAS;;IAEbE,WAAW;MACPX,MAAMK;MACN,WAAS;;IAEbO,gBAAgB;MACZZ,MAAMK;MACN,WAAS;IACb;;EAEJQ,OAAOC;EACPC,SAAO,SAAPA,UAAU;AACN,WAAO;MACHC,SAAS;MACTC,iBAAiB;;EAEzB;AACJ;ACdA,IAAA,WAAe;EACXrB,MAAM;EACNsB,UAAU;EACV,WAASrB;EACTsB,cAAc;EACdC,OAAO,CAAC,cAAc,gBAAgB;EACtCtB,OAAO;IACHuB,MAAM;IACNC,WAAW;IACXC,IAAI;IACJC,iBAAiB;IACjBC,OAAO;;EAEXC,SAAS;IACLC,aAAW,SAAXA,YAAYC,eAAehC,MAAM;AAC7B,aAAOgC,iBAAiBA,cAAcP,OAAOQ,QAAQD,cAAcP,KAAKzB,IAAI,CAAC,IAAIkC;;IAErFC,cAAAA,SAAAA,aAAaC,KAAK;AACd,aAAO,KAAKC,IAAID,KAAK;QACjBE,SAAS;UACLb,MAAM,KAAKA;UACXI,OAAO,KAAKA;UACZU,SAAS,KAAKC,cAAa;UAC3BC,UAAU,KAAKA,SAAQ;QAC3B;MACJ,CAAC;;IAELD,eAAa,SAAbA,gBAAgB;AACZ,aAAO,KAAKZ,oBAAoB,KAAKD;;IAEzCe,aAAAA,SAAAA,YAAYC,OAAO;AACf,UAAMC,UAAU,KAAKb,YAAY,KAAKN,MAAM,SAAS;AAErDmB,iBAAWA,QAAQ;QAAEC,eAAeF;QAAOlB,MAAM,KAAKA,KAAKA;MAAK,CAAC;AACjE,WAAKqB,MAAM,cAAc;QAAED,eAAeF;QAAOlB,MAAM,KAAKA;QAAME,IAAI,KAAKA;MAAG,CAAC;;IAEnFoB,iBAAAA,SAAAA,gBAAgBJ,OAAO;AACnB,WAAKG,MAAM,kBAAkB;QAAED,eAAeF;QAAOlB,MAAM,KAAKA;QAAME,IAAI,KAAKA;MAAG,CAAC;;IAEvFqB,SAAO,SAAPA,UAAU;AACN,aAAO,OAAO,KAAKvB,KAAKuB,YAAY,aAAa,KAAKvB,KAAKuB,QAAO,IAAK,KAAKvB,KAAKuB,YAAY;;IAEjGP,UAAQ,SAARA,WAAW;AACP,aAAO,OAAO,KAAKhB,KAAKgB,aAAa,aAAa,KAAKhB,KAAKgB,SAAQ,IAAK,KAAKhB,KAAKgB;;IAEvFQ,OAAK,SAALA,QAAQ;AACJ,aAAO,OAAO,KAAKxB,KAAKwB,UAAU,aAAa,KAAKxB,KAAKwB,MAAK,IAAK,KAAKxB,KAAKwB;;IAEjFC,kBAAAA,SAAAA,iBAAiBzB,OAAM;AACnB,aAAO;QACH0B,QAAQC,WACJ;UACI,SAAO,KAAKC,GAAG,UAAU;UACzBvC,UAAU;QACd,GACA,KAAKqB,aAAa,UAAU,CAChC;QACAmB,MAAMF,WACF;UACI,SAAO,CAAC,KAAKC,GAAG,UAAU,GAAG5B,MAAK6B,IAAI;QAC1C,GACA,KAAKnB,aAAa,UAAU,CAChC;QACAc,OAAOG,WACH;UACI,SAAO,KAAKC,GAAG,WAAW;QAC9B,GACA,KAAKlB,aAAa,WAAW,CACjC;;IAER;;EAEJoB,UAAU;IACNC,OAAK,SAALA,QAAQ;AACJ,aAAOC,GAAG;QACNC,OAAO,KAAKlB,cAAa;QACzBC,UAAU,KAAKA,SAAQ;MAC3B,CAAC;IACL;;EAEJkB,YAAY;IACRC,QAAQC;EACZ;AACJ;;;;;;;;SCnHcC,SAAOd,QAAA,KADjBe,UAAA,GAAAC,mBAuBI,MAvBJC,WAuBI;;IArBCtC,IAAIuC,OAAEvC;IACN,SAAQ,CAAAwC,KAAAd,GAAY,MAAA,GAAAa,OAAAzC,KAAI,OAAA,CAAM;IAC/B2C,MAAK;IACJnD,OAAOiD,OAAIzC,KAACR;IACZ,cAAY6C,SAAKb,MAAA;IACjB,iBAAea,SAAQrB,SAAA;IACvB,kBAAgBqB,SAAatB,cAAA;IAC7B,mBAAiBsB,SAAQrB,SAAA,KAAA;IACzB,UAAQqB,SAAKN;KACNM,SAAY3B,aAAA,MAAA,CAAA,GAAA,CAEpBkC,gBASK,OATLJ,WASK;IATC,SAAOE,KAAEd,GAAA,aAAA;IAAkBiB,SAAKC,OAAA,CAAA,MAAAA,OAAA,CAAA,IAAA,SAAAC,QAAA;AAAA,aAAEV,SAAWpB,YAAC8B,MAAM;IAAA;IAAIC,aAASF,OAAA,CAAA,MAAAA,OAAA,CAAA,IAAA,SAAAC,QAAA;AAAA,aAAEV,SAAef,gBAACyB,MAAM;IAAA;IAAI,UAAQV,SAAKN;KAAUM,SAAY3B,aAAA,aAAA,CAAA,GAAA,CACjH,CAAA+B,OAAAxC,UAAUD,OACvBiD,gBAAAX,UAAA,GAAAC,mBAIG,KAJHC,WAIG;;IAJUU,MAAMT,OAAIzC,KAACmD;IAAM,SAAOT,KAAEd,GAAA,UAAA;IAAewB,QAAQX,OAAIzC,KAACoD;IAAQ/D,UAAS;KAAagD,SAAY3B,aAAA,UAAA,CAAA,GAAA,CACxF+B,OAAAxC,UAAUoD,YAAQ,UAAA,GAAnCC,YAAoGC,wBAA1Dd,OAASxC,UAACoD,QAAQ,GAAA;;IAAGrD,MAAMyC,OAAIzC;IAAG,SAAA,eAAO0C,KAAEd,GAAA,UAAA,CAAA;oCACpEa,OAAAzC,KAAK6B,QAAtBS,UAAA,GAAAC,mBAAoH,QAApHC,WAAoH;;IAAvF,SAAQ,CAAAE,KAAAd,GAAgB,UAAA,GAAAa,OAAAzC,KAAK6B,IAAI;IAAI,UAAQQ,SAAKN;KAAUM,SAAY3B,aAAA,UAAA,CAAA,GAAA,MAAA,IAAA8C,UAAA,KAAA,mBAAA,IAAA,IAAA,GACrGZ,gBAAqG,QAArGJ,WAAqG;IAA9F,SAAOE,KAAEd,GAAA,WAAA;IAAgB,UAAQS,SAAKN;KAAUM,SAAA3B,aAAY,WAAA,CAAA,GAAA,gBAAkB2B,SAAKb,MAAA,CAAA,GAAA,IAAAiC,UAAA,CAAA,GAAA,IAAA,YAAA,IAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,IAG5EhB,OAAAxC,UAAUD,QAAI,UAAA,GAApCsD,YAAoIC,wBAAzFd,OAASxC,UAACD,IAAI,GAAA;;IAAGA,MAAMyC,OAAIzC;IAAGwB,OAAOa,SAAKb,MAAA;IAAK/C,OAAO4D,SAAgBZ,iBAACgB,OAAIzC,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsDlI,IAAA0D,UAAe;EACXnF,MAAM;EACN,WAASoF;EACT7D,cAAc;EACdC,OAAO,CAAC,QAAQ,QAAQ,SAAS,MAAM;EACvC6D,MAAI,SAAJA,OAAO;AACH,WAAO;MACHC,gBAAgB;MAChB/C,SAAS;MACTgD,oBAAoB;MACpBC,qBAAqB;;;EAG7BX,QAAQ;EACRY,sBAAsB;EACtBC,eAAe;EACfC,gBAAgB;EAChBC,WAAW;EACXC,MAAM;EACNC,SAAO,SAAPA,UAAU;AACN,QAAI,CAAC,KAAK3F,OAAO;AACb,WAAK4F,mBAAkB;AACvB,WAAKC,yBAAwB;IACjC;;EAEJC,eAAa,SAAbA,gBAAgB;AACZ,SAAKC,qBAAoB;AACzB,SAAKC,2BAA0B;AAE/B,QAAI,KAAKT,eAAe;AACpB,WAAKA,cAAcU,QAAO;AAC1B,WAAKV,gBAAgB;IACzB;AAEA,SAAKb,SAAS;AAEd,QAAI,KAAKe,aAAa,KAAKjF,YAAY;AACnC0F,aAAOC,MAAM,KAAKV,SAAS;IAC/B;AAEA,SAAKA,YAAY;;EAErB9D,SAAS;IACLyE,WAAAA,SAAAA,UAAU5D,OAAO;AACb,UAAMlB,QAAOkB,MAAMlB;AAEnB,UAAI,KAAKgB,SAAShB,KAAI,GAAG;AACrB;MACJ;AAEA,UAAIA,MAAKmB,SAAS;AACdnB,QAAAA,MAAKmB,QAAQD,KAAK;MACtB;AAEA,UAAI,KAAK2C,eAAgB,MAAKkB,KAAI;AAElC,UAAI,CAAC,KAAKrG,SAAS,KAAKoF,uBAAuB5C,MAAMhB,IAAI;AACrD,aAAK4D,qBAAqB5C,MAAMhB;MACpC;;IAEJ8E,eAAAA,SAAAA,cAAc9D,OAAO;AACjB,UAAI,KAAKJ,SAAS;AACd,aAAKgD,qBAAqB5C,MAAMhB;MACpC;;IAEJ+E,aAAAA,SAAAA,YAAY/D,OAAO;AACf,WAAKJ,UAAU;AACf,OAAC,KAAKpC,SAAS,KAAKwG,yBAAyB,CAAC;AAE9C,WAAK7D,MAAM,SAASH,KAAK;;IAE7BiE,YAAAA,SAAAA,WAAWjE,OAAO;AACd,WAAKJ,UAAU;AACf,WAAKgD,qBAAqB;AAC1B,WAAKzC,MAAM,QAAQH,KAAK;;IAE5BkE,eAAAA,SAAAA,cAAclE,OAAO;AACjB,cAAQA,MAAMmE,MAAI;QACd,KAAK;AACD,eAAKC,eAAepE,KAAK;AACzB;QAEJ,KAAK;AACD,eAAKqE,aAAarE,KAAK;AACvB;QAEJ,KAAK;AACD,eAAKsE,UAAUtE,KAAK;AACpB;QAEJ,KAAK;AACD,eAAKuE,SAASvE,KAAK;AACnB;QAEJ,KAAK;QACL,KAAK;AACD,eAAKwE,WAAWxE,KAAK;AACrB;QAEJ,KAAK;AACD,eAAKyE,WAAWzE,KAAK;AACrB;QAEJ,KAAK;AACD,cAAI,KAAKxC,OAAO;AACZuD,kBAAM,KAAKmB,MAAM;AACjB,iBAAK2B,KAAI;UACb;QAEJ,KAAK;AACD,eAAKlB,kBAAkB,KAAKkB,KAAI;AAChC;MAIR;;IAEJO,gBAAAA,SAAAA,eAAepE,OAAO;AAClB,UAAM0E,cAAc,KAAKC,oBAAoB,KAAK/B,kBAAkB;AAEpE,WAAKoB,yBAAyBU,WAAW;AACzC1E,YAAM4E,eAAc;;IAExBP,cAAAA,SAAAA,aAAarE,OAAO;AAChB,UAAIA,MAAM6E,UAAU,KAAKrH,OAAO;AAC5BuD,cAAM,KAAKmB,MAAM;AACjB,aAAK2B,KAAI;AACT7D,cAAM4E,eAAc;MACxB,OAAO;AACH,YAAMF,cAAc,KAAKI,oBAAoB,KAAKlC,kBAAkB;AAEpE,aAAKoB,yBAAyBU,WAAW;AACzC1E,cAAM4E,eAAc;MACxB;;IAEJN,WAAAA,SAAAA,UAAUtE,OAAO;AACb,WAAKgE,yBAAyB,CAAC;AAC/BhE,YAAM4E,eAAc;;IAExBL,UAAAA,SAAAA,SAASvE,OAAO;AACZ,WAAKgE,yBAAyBe,KAAK,KAAK9B,WAAW,qDAAqD,EAAE+B,SAAS,CAAC;AACpHhF,YAAM4E,eAAc;;IAExBJ,YAAAA,SAAAA,WAAWxE,OAAO;AACd,UAAMiF,UAAUC,WAAW,KAAKhC,MAAI,UAAAiC,OAAA,GAAAA,OAAe,KAAKvC,kBAAkB,GAAA,IAAA,CAAM;AAChF,UAAMwC,gBAAgBH,WAAWC,WAAWD,SAAS,+BAA+B;AAEpF,WAAKzH,SAASuD,MAAM,KAAKmB,MAAM;AAC/BkD,sBAAgBA,cAAcC,MAAK,IAAKJ,WAAWA,QAAQI,MAAK;AAEhErF,YAAM4E,eAAc;;IAExBH,YAAAA,SAAAA,WAAWzE,OAAO;AACd,WAAKwE,WAAWxE,KAAK;;IAEzB2E,qBAAAA,SAAAA,oBAAoBzF,OAAO;AACvB,UAAMoG,QAAQP,KAAK,KAAK9B,WAAW,qDAAqD;AACxF,UAAMsC,qBAAqBC,mBAAIF,KAAK,EAAEG,UAAU,SAACC,MAAI;AAAA,eAAKA,KAAK1G,OAAOE;OAAM;AAE5E,aAAOqG,qBAAqB,KAAKA,qBAAqB,IAAI;;IAE9DT,qBAAAA,SAAAA,oBAAoB5F,OAAO;AACvB,UAAMoG,QAAQP,KAAK,KAAK9B,WAAW,qDAAqD;AACxF,UAAMsC,qBAAqBC,mBAAIF,KAAK,EAAEG,UAAU,SAACC,MAAI;AAAA,eAAKA,KAAK1G,OAAOE;OAAM;AAE5E,aAAOqG,qBAAqB,KAAKA,qBAAqB,IAAI;;IAE9DvB,0BAAAA,SAAAA,yBAAyB9E,OAAO;AAC5B,UAAMoG,QAAQP,KAAK,KAAK9B,WAAW,qDAAqD;AACxF,UAAI0C,QAAQzG,SAASoG,MAAMN,SAASM,MAAMN,SAAS,IAAI9F,QAAQ,IAAI,IAAIA;AAEvEyG,cAAQ,OAAO,KAAK/C,qBAAqB0C,MAAMK,KAAK,EAAEC,aAAa,IAAI;;IAE3EC,QAAM,SAANA,OAAO7F,OAAOkC,QAAQ;AAClB,UAAI,KAAKS,eAAgB,MAAKkB,KAAI;UAC7B,MAAKiC,KAAK9F,OAAOkC,MAAM;;IAEhC4D,MAAI,SAAJA,KAAK9F,OAAOkC,QAAQ;AAChB,WAAKS,iBAAiB;AACtB,WAAKT,SAASA,WAAO,QAAPA,WAAO,SAAPA,SAAUlC,MAAM+F;;IAElClC,MAAI,SAAJA,OAAO;AACH,WAAKlB,iBAAiB;AACtB,WAAKT,SAAS;;IAElB8D,SAAAA,SAAAA,QAAQC,IAAI;AACRC,eAASD,IAAI;QAAEE,UAAU;QAAYC,KAAK;MAAI,CAAC;AAC/C,WAAKC,aAAY;AACjB,WAAKhD,yBAAwB;AAC7B,WAAKD,mBAAkB;AACvB,WAAKkD,mBAAkB;AAEvB,UAAI,KAAKtI,YAAY;AACjB0F,eAAO6C,IAAI,QAAQN,IAAI,KAAKhI,aAAa,KAAKuI,UAAUC,OAAOC,OAAOC,IAAI;MAC9E;AAEA,UAAI,KAAKnJ,OAAO;AACZuD,cAAM,KAAKmC,IAAI;MACnB;AAEA,WAAK/C,MAAM,MAAM;;IAErByG,SAAO,SAAPA,UAAU;AACN,WAAKpD,2BAA0B;AAC/B,WAAKD,qBAAoB;AACzB,WAAKsD,qBAAoB;AACzB,WAAK1G,MAAM,MAAM;;IAErB2G,cAAAA,SAAAA,aAAab,IAAI;AACb,UAAI,KAAKjI,YAAY;AACjB0F,eAAOC,MAAMsC,EAAE;MACnB;;IAEJI,cAAY,SAAZA,eAAe;AACXU,uBAAiB,KAAK9D,WAAW,KAAKf,MAAM;AAC5C,UAAM8E,cAAcC,cAAc,KAAK/E,MAAM;AAE7C,UAAI8E,cAAcC,cAAc,KAAKhE,SAAS,GAAG;AAC7C,aAAKA,UAAU3E,MAAM4I,WAAWD,cAAc,KAAK/E,MAAM,IAAI;MACjE;;IAEJmB,0BAAwB,SAAxBA,2BAA2B;AAAA,UAAA8D,QAAA;AACvB,UAAI,CAAC,KAAKrE,sBAAsB;AAC5B,aAAKA,uBAAuB,SAAC9C,OAAU;AACnC,cAAMoH,qBAAqBD,MAAKlE,aAAa,CAACkE,MAAKlE,UAAUoE,SAASrH,MAAMkC,MAAM;AAClF,cAAMoF,kBAAkB,EAAEH,MAAKjF,WAAWiF,MAAKjF,WAAWlC,MAAMkC,UAAUiF,MAAKjF,OAAOmF,SAASrH,MAAMkC,MAAM;AAE3G,cAAIiF,MAAKxE,kBAAkByE,sBAAsBE,iBAAiB;AAC9DH,kBAAKtD,KAAI;qBACF,CAACsD,MAAK3J,SAAS4J,sBAAsBE,iBAAiB;AAC7DH,kBAAKvE,qBAAqB;UAC9B;;AAGJ2E,iBAASC,iBAAiB,SAAS,KAAK1E,sBAAsB,IAAI;MACtE;;IAEJU,4BAA0B,SAA1BA,6BAA6B;AACzB,UAAI,KAAKV,sBAAsB;AAC3ByE,iBAASE,oBAAoB,SAAS,KAAK3E,sBAAsB,IAAI;AACrE,aAAKA,uBAAuB;MAChC;;IAEJwD,oBAAkB,SAAlBA,qBAAqB;AAAA,UAAAoB,SAAA;AACjB,UAAI,CAAC,KAAK3E,eAAe;AACrB,aAAKA,gBAAgB,IAAI4E,8BAA8B,KAAKzF,QAAQ,WAAM;AACtE,cAAIwF,OAAK/E,gBAAgB;AACrB+E,mBAAK7D,KAAI;UACb;QACJ,CAAC;MACL;AAEA,WAAKd,cAAcuD,mBAAkB;;IAEzCO,sBAAoB,SAApBA,uBAAuB;AACnB,UAAI,KAAK9D,eAAe;AACpB,aAAKA,cAAc8D,qBAAoB;MAC3C;;IAEJzD,oBAAkB,SAAlBA,qBAAqB;AAAA,UAAAwE,SAAA;AACjB,UAAI,CAAC,KAAK5E,gBAAgB;AACtB,aAAKA,iBAAiB,WAAM;AACxB,cAAI4E,OAAKjF,kBAAkB,CAACkF,cAAa,GAAI;AACzCD,mBAAK/D,KAAI;UACb;;AAGJiE,eAAON,iBAAiB,UAAU,KAAKxE,cAAc;MACzD;;IAEJO,sBAAoB,SAApBA,uBAAuB;AACnB,UAAI,KAAKP,gBAAgB;AACrB8E,eAAOL,oBAAoB,UAAU,KAAKzE,cAAc;AACxD,aAAKA,iBAAiB;MAC1B;;IAEJ3C,SAAAA,SAAAA,SAAQvB,OAAM;AACV,aAAO,OAAOA,MAAKuB,YAAY,aAAavB,MAAKuB,QAAO,IAAKvB,MAAKuB,YAAY;;IAElFP,UAAAA,SAAAA,UAAShB,OAAM;AACX,aAAO,OAAOA,MAAKgB,aAAa,aAAahB,MAAKgB,SAAQ,IAAKhB,MAAKgB;;IAExEQ,OAAAA,SAAAA,OAAMxB,OAAM;AACR,aAAO,OAAOA,MAAKwB,UAAU,aAAaxB,MAAKwB,MAAK,IAAKxB,MAAKwB;;IAElEyH,gBAAAA,SAAAA,eAAe/H,OAAO;AAClBgI,sBAAgBC,KAAK,iBAAiB;QAClC/H,eAAeF;QACfkC,QAAQ,KAAKA;MACjB,CAAC;;IAELgG,cAAAA,SAAAA,aAAajC,IAAI;AACb,WAAKhD,YAAYgD;;IAErBkC,SAAAA,SAAAA,QAAQlC,IAAI;AACR,WAAK/C,OAAO+C;IAChB;;EAEJrF,UAAU;IACN3B,iBAAe,SAAfA,kBAAkB;AACd,aAAO,KAAK2D,uBAAuB,KAAK,KAAKA,qBAAqB;;IAEtE/B,OAAK,SAALA,SAAQ;AACJ,aAAOC,GAAG;QACNtD,OAAO,KAAKA;MAChB,CAAC;IACL;;EAEJ4K,YAAY;IACRC,YAAYC;IACZC,QAAQA;EACZ;AACJ;;;;;;;sBCnYInG,YA8DQoG,mBAAA;IA9DC3K,UAAU2D,KAAQ3D;IAAGiC,UAAQ,CAAG0B,KAAKhE;;uBAC1C,WAAA;AAAA,aA4DY,CA5DZiL,YA4DYC,YA5DZpH,WA4DY;QA5DAjE,MAAK;QAAuB2I,SAAO7E,SAAO6E;QAAGY,SAAOzF,SAAOyF;QAAGE,cAAa3F,SAAY2F;SAAUtF,KAAG9B,IAAA,YAAA,CAAA,GAAA;2BAC5G,WAAA;AAAA,iBA0DK,EA1DM8B,KAAAhE,QAAQmL,MAAahG,iBAAA,SAAhCvB,UAAA,GAAAC,mBA0DK,OA1DLC,WA0DK;;YA1DsCsH,KAAKzH,SAAY+G;YAAGlJ,IAAIwC,KAAGqH;YAAG,SAAOrH,KAAEd,GAAA,MAAA;YAAWiB,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAER,SAAc4G,kBAAA5G,SAAA4G,eAAAe,MAAA3H,UAAA4H,SAAA;YAAA;YAAG,UAAQ5H,SAAKN;aAAUW,KAAIwH,KAAA,MAAA,CAAA,GAAA,CACjIxH,KAAAyH,OAAOC,SAAlB9H,UAAA,GAAAC,mBAEK,OAFLC,WAEK;;YAFqB,SAAOE,KAAEd,GAAA,OAAA;aAAmBc,KAAG9B,IAAA,OAAA,CAAA,GAAA,CACrDyJ,WAAyB3H,KAAAyH,QAAA,OAAA,CAAA,GAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,GAE7BvH,gBAkDI,MAlDJJ,WAkDI;YAjDCsH,KAAKzH,SAAOgH;YACZnJ,IAAIwC,KAAEqH,MAAA;YACN,SAAOrH,KAAEd,GAAA,MAAA;YACVe,MAAK;YACJtD,UAAUqD,KAAQrD;YAClB,yBAAuBwK,MAAA/I,UAAUuB,SAAAlC,kBAAkBM;YACnD,cAAYiC,KAASpD;YACrB,mBAAiBoD,KAAcnD;YAC/B+K,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAEjI,SAAW4C,eAAA5C,SAAA4C,YAAA+E,MAAA3H,UAAA4H,SAAA;YAAA;YAClBM,QAAI,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAElI,SAAU8C,cAAA9C,SAAA8C,WAAA6E,MAAA3H,UAAA4H,SAAA;YAAA;YAChBO,WAAO,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAEnI,SAAa+C,iBAAA/C,SAAA+C,cAAA4E,MAAA3H,UAAA4H,SAAA;;aACfvH,KAAG9B,IAAA,MAAA,CAAA,GAAA,EAEX0B,UAAA,IAAA,GAAAC,mBAmCUkI,UAnCoB,MAAAC,WAAAhI,KAAA7D,OAAZ,SAAAmB,OAAM2K,GAAC;;cAAkBhK,KAAA0B,SAAAb,MAAMxB,KAAI,IAAI2K,EAAEC,SAAQ;gBAC/C5K,MAAK6K,SAASxI,SAAAd,QAAQvB,KAAI,KAAA,CAAMA,MAAK8K,aAAS,UAAA,GAA9DvI,mBAmBUkI,UAAA;cAAA9J,KAAA;YAAA,GAAA,CAlBIX,MAAK6K,SAAfvI,UAAA,GAAAC,mBAGI,MAHJC,WAGI;;cAHmBtC,IAAIwC,KAAEqH,MAAA,MAAUY;cAAI,SAAQ,CAAAjI,KAAAd,GAAoB,cAAA,GAAA5B,MAAI,OAAA,CAAM;cAAG2C,MAAK;;eAAeD,KAAG9B,IAAA,cAAA,CAAA,GAAA,CAEvGyJ,WAAyG3H,KAAAyH,QAA5FzH,KAAAA,OAAOqI,eAAa,iBAAA,iBAAA;cAAqC/K,MAAMA;YAAI,GAAhF,WAAA;AAAA,qBAAyG,CAApBgL,gBAAAC,gBAAA5I,SAAAb,MAAMxB,KAAI,CAAA,GAAA,CAAA,CAAA;mEAEnGsC,UAAA,IAAA,GAAAC,mBAaUkI,UAAAA,MAAAA,WAbqBzK,MAAK6K,OAAlB,SAAAK,OAAOC,GAAC;;gBAAuBxK,KAAAuK,MAAM1J,QAAQmJ,IAAE,MAAQQ;kBAE3D9I,SAAAd,QAAQ2J,KAAK,KAAM,CAAAA,MAAMJ,aAAS,UAAA,GAD5CxH,YAUC8H,uBAAA;;gBARIlL,IAAIwC,KAAAqH,MAAY,MAAAY,IAAE,MAAQQ;gBAC1BnL,MAAMkL;gBACNjL,WAAWyC,KAAMyH;gBACjBhK,iBAAiBkC,SAAelC;gBAChCkL,UAAU3I,KAAQ2I;gBAClBpK,aAAYoB,SAASyC;gBACrBwG,iBAAgBjJ,SAAa2C;gBAC7BuG,IAAI7I,KAAE6I;iIAEIlJ,SAAAd,QAAQ2J,KAAK,KAAKA,MAAMJ,aAAvCxI,UAAA,GAAAC,mBAAwL,MAAxLC,WAAwL;gBAArI7B,KAAG,cAAgBgK,IAAIQ;gBAAI,SAAQ,CAAAzI,KAAAd,GAAiB,WAAA,GAAA5B,MAAI,OAAA,CAAM;gBAAIR,OAAO0L,MAAM1L;gBAAOmD,MAAK;;iBAAoBD,KAAG9B,IAAA,WAAA,CAAA,GAAA,MAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,CAAA,GAAA,EAAA;+BAG9JyB,SAAAd,QAAQvB,KAAI,KAAKA,MAAK8K,aAArCxI,UAAA,GAAAC,mBAA4L,MAA5LC,WAA4L;cAA3I7B,KAAG,cAAgBgK,EAAEC,SAAQ;cAAK,SAAQ,CAAAlI,KAAAd,GAAiB,WAAA,GAAA5B,MAAI,OAAA,CAAM;cAAIR,OAAOQ,MAAKR;cAAOmD,MAAK;;eAAoBD,KAAG9B,IAAA,WAAA,CAAA,GAAA,MAAA,EAAA,MAAA,UAAA,GACzK0C,YAYC8H,uBAAA;cAVIzK,KAAK0B,SAAKb,MAACxB,KAAI,IAAI2K,EAAEC,SAAQ;cAC7B1K,IAAIwC,KAAIqH,MAAA,MAAQY;cAChB3K,MAAMA;cACNI,OAAOuK;cACP1K,WAAWyC,KAAMyH;cACjBhK,iBAAiBkC,SAAelC;cAChCkL,UAAU3I,KAAQ2I;cAClBpK,aAAYoB,SAASyC;cACrBwG,iBAAgBjJ,SAAa2C;cAC7BuG,IAAI7I,KAAE6I;;uCAIR7I,KAAAyH,OAAOqB,OAAlBlJ,UAAA,GAAAC,mBAEK,OAFLC,WAEK;;YAFmB,SAAOE,KAAEd,GAAA,KAAA;aAAiBc,KAAG9B,IAAA,KAAA,CAAA,GAAA,CACjDyJ,WAAuB3H,KAAAyH,QAAA,KAAA,CAAA,GAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,CAAA,GAAA,IAAA,UAAA,KAAA,mBAAA,IAAA,IAAA,CAAA;;;;;;;;;", "names": ["classes", "root", "_ref", "props", "popup", "start", "list", "submenuLabel", "separator", "end", "item", "_ref2", "instance", "id", "focusedOptionId", "disabled", "itemContent", "itemLink", "itemIcon", "itemLabel", "BaseStyle", "extend", "name", "style", "name", "BaseComponent", "props", "popup", "type", "Boolean", "model", "Array", "appendTo", "String", "Object", "autoZIndex", "baseZIndex", "Number", "tabindex", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "MenuStyle", "provide", "$pcMenu", "$parentInstance", "hostName", "inheritAttrs", "emits", "item", "templates", "id", "focusedOptionId", "index", "methods", "getItemProp", "processedItem", "resolve", "undefined", "getPTOptions", "key", "ptm", "context", "focused", "isItemFocused", "disabled", "onItemClick", "event", "command", "originalEvent", "$emit", "onItemMouseMove", "visible", "label", "getMenuItemProps", "action", "mergeProps", "cx", "icon", "computed", "dataP", "cn", "focus", "directives", "ripple", "<PERSON><PERSON><PERSON>", "$options", "_openBlock", "_createElementBlock", "_mergeProps", "$props", "_ctx", "role", "_createElementVNode", "onClick", "_cache", "$event", "onMousemove", "_withDirectives", "href", "url", "target", "itemicon", "_createBlock", "_resolveDynamicComponent", "_hoisted_4", "_hoisted_5", "script", "BaseMenu", "data", "overlayVisible", "focusedOptionIndex", "selectedOptionIndex", "outsideClickListener", "<PERSON><PERSON><PERSON><PERSON>", "resizeListener", "container", "list", "mounted", "bindResizeListener", "bindOutsideClickListener", "beforeUnmount", "unbindResizeListener", "unbindOutsideClickListener", "destroy", "ZIndex", "clear", "itemClick", "hide", "itemMouseMove", "onListFocus", "changeFocusedOptionIndex", "onListBlur", "onListKeyDown", "code", "onArrowDownKey", "onArrowUpKey", "onHomeKey", "onEndKey", "onEnterKey", "onSpaceKey", "optionIndex", "findNextOptionIndex", "preventDefault", "altKey", "findPrevOptionIndex", "find", "length", "element", "findSingle", "concat", "anchorElement", "click", "links", "matchedOptionIndex", "_toConsumableArray", "findIndex", "link", "order", "getAttribute", "toggle", "show", "currentTarget", "onEnter", "el", "addStyle", "position", "top", "alignOverlay", "bindScrollListener", "set", "$primevue", "config", "zIndex", "menu", "onLeave", "unbindScrollListener", "onAfterLeave", "absolutePosition", "targetWidth", "getOuterWidth", "min<PERSON><PERSON><PERSON>", "_this", "isOutsideContainer", "contains", "isOutsideTarget", "document", "addEventListener", "removeEventListener", "_this2", "ConnectedOverlayScrollHandler", "_this3", "isTouchDevice", "window", "onOverlayClick", "OverlayEventBus", "emit", "containerRef", "listRef", "components", "PVMenuitem", "<PERSON><PERSON><PERSON>", "Portal", "_component_Portal", "_createVNode", "_Transition", "$data", "ref", "$id", "apply", "arguments", "ptmi", "$slots", "start", "_renderSlot", "onFocus", "onBlur", "onKeydown", "_Fragment", "_renderList", "i", "toString", "items", "separator", "submenulabel", "_createTextVNode", "_toDisplayString", "child", "j", "_component_PVMenuitem", "unstyled", "onItemMousemove", "pt", "end"]}