{"version": 3, "file": "index.mjs", "sources": ["../../../src/avatargroup/style/AvatarGroupStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-avatar-group p-component'\n};\n\nexport default BaseStyle.extend({\n    name: 'avatargroup',\n    classes\n});\n"], "names": ["classes", "root", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE;AACV,CAAC;AAED,uBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,aAAa;AACnBJ,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}