{"version": 3, "file": "index.mjs", "sources": ["../../../src/button/style/ButtonStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/button';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-button p-component',\n        {\n            'p-button-icon-only': instance.hasIcon && !props.label && !props.badge,\n            'p-button-vertical': (props.iconPos === 'top' || props.iconPos === 'bottom') && props.label,\n            'p-button-loading': props.loading,\n            'p-button-link': props.link || props.variant === 'link',\n            [`p-button-${props.severity}`]: props.severity,\n            'p-button-raised': props.raised,\n            'p-button-rounded': props.rounded,\n            'p-button-text': props.text || props.variant === 'text',\n            'p-button-outlined': props.outlined || props.variant === 'outlined',\n            'p-button-sm': props.size === 'small',\n            'p-button-lg': props.size === 'large',\n            'p-button-plain': props.plain,\n            'p-button-fluid': instance.hasFluid\n        }\n    ],\n    loadingIcon: 'p-button-loading-icon',\n    icon: ({ props }) => [\n        'p-button-icon',\n        {\n            [`p-button-icon-${props.iconPos}`]: props.label\n        }\n    ],\n    label: 'p-button-label'\n};\n\nexport default BaseStyle.extend({\n    name: 'button',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "instance", "props", "_defineProperty", "hasIcon", "label", "badge", "iconPos", "loading", "link", "variant", "concat", "severity", "raised", "rounded", "text", "outlined", "size", "plain", "hasFluid", "loadingIcon", "icon", "_ref3", "BaseStyle", "extend", "name", "style"], "mappings": ";;;;;;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;AAAA,IAAA,OAAO,CAC3B,sBAAsB,EAAAC,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAA;AAElB,MAAA,oBAAoB,EAAEF,QAAQ,CAACG,OAAO,IAAI,CAACF,KAAK,CAACG,KAAK,IAAI,CAACH,KAAK,CAACI,KAAK;AACtE,MAAA,mBAAmB,EAAE,CAACJ,KAAK,CAACK,OAAO,KAAK,KAAK,IAAIL,KAAK,CAACK,OAAO,KAAK,QAAQ,KAAKL,KAAK,CAACG,KAAK;MAC3F,kBAAkB,EAAEH,KAAK,CAACM,OAAO;MACjC,eAAe,EAAEN,KAAK,CAACO,IAAI,IAAIP,KAAK,CAACQ,OAAO,KAAK;AAAM,KAAA,EAAA,WAAA,CAAAC,MAAA,CAC1CT,KAAK,CAACU,QAAQ,CAAKV,EAAAA,KAAK,CAACU,QAAQ,GAC9C,iBAAiB,EAAEV,KAAK,CAACW,MAAM,CAC/B,EAAA,kBAAkB,EAAEX,KAAK,CAACY,OAAO,CACjC,EAAA,eAAe,EAAEZ,KAAK,CAACa,IAAI,IAAIb,KAAK,CAACQ,OAAO,KAAK,MAAM,GACvD,mBAAmB,EAAER,KAAK,CAACc,QAAQ,IAAId,KAAK,CAACQ,OAAO,KAAK,UAAU,CACnE,EAAA,aAAa,EAAER,KAAK,CAACe,IAAI,KAAK,OAAO,CACrC,EAAA,aAAa,EAAEf,KAAK,CAACe,IAAI,KAAK,OAAO,CAAA,EACrC,gBAAgB,EAAEf,KAAK,CAACgB,KAAK,GAC7B,gBAAgB,EAAEjB,QAAQ,CAACkB,QAAQ,CAE1C,CAAA;AAAA,GAAA;AACDC,EAAAA,WAAW,EAAE,uBAAuB;AACpCC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKpB,KAAK,GAAAoB,KAAA,CAALpB,KAAK;AAAA,IAAA,OAAO,CACjB,eAAe,EAAAC,eAAA,sBAAAQ,MAAA,CAEOT,KAAK,CAACK,OAAO,CAAA,EAAKL,KAAK,CAACG,KAAK,CAEtD,CAAA;AAAA,GAAA;AACDA,EAAAA,KAAK,EAAE;AACX,CAAC;AAED,kBAAekB,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,QAAQ;AACdC,EAAAA,KAAK,EAALA,KAAK;AACL5B,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}