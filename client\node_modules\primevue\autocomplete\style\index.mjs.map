{"version": 3, "file": "index.mjs", "sources": ["../../../src/autocomplete/style/AutoCompleteStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/autocomplete';\nimport { isNotEmpty } from '@primeuix/utils/object';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst inlineStyles = {\n    root: { position: 'relative' }\n};\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-autocomplete p-component p-inputwrapper',\n        {\n            'p-disabled': props.disabled,\n            'p-invalid': instance.$invalid,\n            'p-focus': instance.focused,\n            'p-inputwrapper-filled': instance.$filled || isNotEmpty(instance.inputValue),\n            'p-inputwrapper-focus': instance.focused,\n            'p-autocomplete-open': instance.overlayVisible,\n            'p-autocomplete-fluid': instance.$fluid\n        }\n    ],\n    pcInputText: 'p-autocomplete-input',\n    inputMultiple: ({ instance }) => [\n        'p-autocomplete-input-multiple',\n        {\n            'p-variant-filled': instance.$variant === 'filled'\n        }\n    ],\n    chipItem: ({ instance, i }) => [\n        'p-autocomplete-chip-item',\n        {\n            'p-focus': instance.focusedMultipleOptionIndex === i\n        }\n    ],\n    pcChip: 'p-autocomplete-chip',\n    chipIcon: 'p-autocomplete-chip-icon',\n    inputChip: 'p-autocomplete-input-chip',\n    loader: 'p-autocomplete-loader',\n    dropdown: 'p-autocomplete-dropdown',\n    overlay: 'p-autocomplete-overlay p-component',\n    listContainer: 'p-autocomplete-list-container',\n    list: 'p-autocomplete-list',\n    optionGroup: 'p-autocomplete-option-group',\n    option: ({ instance, option, i, getItemOptions }) => [\n        'p-autocomplete-option',\n        {\n            'p-autocomplete-option-selected': instance.isSelected(option),\n            'p-focus': instance.focusedOptionIndex === instance.getOptionIndex(i, getItemOptions),\n            'p-disabled': instance.isOptionDisabled(option)\n        }\n    ],\n    emptyMessage: 'p-autocomplete-empty-message'\n};\n\nexport default BaseStyle.extend({\n    name: 'autocomplete',\n    style,\n    classes,\n    inlineStyles\n});\n"], "names": ["inlineStyles", "root", "position", "classes", "_ref", "instance", "props", "disabled", "$invalid", "focused", "$filled", "isNotEmpty", "inputValue", "overlayVisible", "$fluid", "pcInputText", "inputMultiple", "_ref2", "$variant", "chipItem", "_ref3", "i", "focusedMultipleOptionIndex", "pcChip", "chipIcon", "inputChip", "loader", "dropdown", "overlay", "listContainer", "list", "optionGroup", "option", "_ref4", "getItemOptions", "isSelected", "focusedOptionIndex", "getOptionIndex", "isOptionDisabled", "emptyMessage", "BaseStyle", "extend", "name", "style"], "mappings": ";;;;AAIA,IAAMA,YAAY,GAAG;AACjBC,EAAAA,IAAI,EAAE;AAAEC,IAAAA,QAAQ,EAAE;AAAW;AACjC,CAAC;AAED,IAAMC,OAAO,GAAG;AACZF,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAG,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAA,OAAO,CAC3B,2CAA2C,EAC3C;MACI,YAAY,EAAEA,KAAK,CAACC,QAAQ;MAC5B,WAAW,EAAEF,QAAQ,CAACG,QAAQ;MAC9B,SAAS,EAAEH,QAAQ,CAACI,OAAO;MAC3B,uBAAuB,EAAEJ,QAAQ,CAACK,OAAO,IAAIC,UAAU,CAACN,QAAQ,CAACO,UAAU,CAAC;MAC5E,sBAAsB,EAAEP,QAAQ,CAACI,OAAO;MACxC,qBAAqB,EAAEJ,QAAQ,CAACQ,cAAc;MAC9C,sBAAsB,EAAER,QAAQ,CAACS;AACrC,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,WAAW,EAAE,sBAAsB;AACnCC,EAAAA,aAAa,EAAE,SAAfA,aAAaA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKZ,QAAQ,GAAAY,KAAA,CAARZ,QAAQ;IAAA,OAAO,CAC7B,+BAA+B,EAC/B;AACI,MAAA,kBAAkB,EAAEA,QAAQ,CAACa,QAAQ,KAAK;AAC9C,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,QAAQ,EAAE,SAAVA,QAAQA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKf,QAAQ,GAAAe,KAAA,CAARf,QAAQ;MAAEgB,CAAC,GAAAD,KAAA,CAADC,CAAC;IAAA,OAAO,CAC3B,0BAA0B,EAC1B;AACI,MAAA,SAAS,EAAEhB,QAAQ,CAACiB,0BAA0B,KAAKD;AACvD,KAAC,CACJ;AAAA,GAAA;AACDE,EAAAA,MAAM,EAAE,qBAAqB;AAC7BC,EAAAA,QAAQ,EAAE,0BAA0B;AACpCC,EAAAA,SAAS,EAAE,2BAA2B;AACtCC,EAAAA,MAAM,EAAE,uBAAuB;AAC/BC,EAAAA,QAAQ,EAAE,yBAAyB;AACnCC,EAAAA,OAAO,EAAE,oCAAoC;AAC7CC,EAAAA,aAAa,EAAE,+BAA+B;AAC9CC,EAAAA,IAAI,EAAE,qBAAqB;AAC3BC,EAAAA,WAAW,EAAE,6BAA6B;AAC1CC,EAAAA,MAAM,EAAE,SAARA,MAAMA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAK5B,QAAQ,GAAA4B,KAAA,CAAR5B,QAAQ;MAAE2B,OAAM,GAAAC,KAAA,CAAND,MAAM;MAAEX,CAAC,GAAAY,KAAA,CAADZ,CAAC;MAAEa,cAAc,GAAAD,KAAA,CAAdC,cAAc;IAAA,OAAO,CACjD,uBAAuB,EACvB;AACI,MAAA,gCAAgC,EAAE7B,QAAQ,CAAC8B,UAAU,CAACH,OAAM,CAAC;AAC7D,MAAA,SAAS,EAAE3B,QAAQ,CAAC+B,kBAAkB,KAAK/B,QAAQ,CAACgC,cAAc,CAAChB,CAAC,EAAEa,cAAc,CAAC;AACrF,MAAA,YAAY,EAAE7B,QAAQ,CAACiC,gBAAgB,CAACN,OAAM;AAClD,KAAC,CACJ;AAAA,GAAA;AACDO,EAAAA,YAAY,EAAE;AAClB,CAAC;AAED,wBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,cAAc;AACpBC,EAAAA,KAAK,EAALA,KAAK;AACLxC,EAAAA,OAAO,EAAPA,OAAO;AACPH,EAAAA,YAAY,EAAZA;AACJ,CAAC,CAAC;;;;"}