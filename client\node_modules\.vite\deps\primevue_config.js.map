{"version": 3, "sources": ["../../@primevue/src/config/PrimeVue.js"], "sourcesContent": ["import { Theme, ThemeService } from '@primeuix/styled';\nimport { mergeKeys } from '@primeuix/utils';\nimport { FilterMatchMode } from '@primevue/core/api';\nimport BaseStyle from '@primevue/core/base/style';\nimport PrimeVueService from '@primevue/core/service';\nimport { inject, reactive, ref, watch } from 'vue';\n\nexport const defaultOptions = {\n    ripple: false,\n    inputStyle: null,\n    inputVariant: null,\n    locale: {\n        startsWith: 'Starts with',\n        contains: 'Contains',\n        notContains: 'Not contains',\n        endsWith: 'Ends with',\n        equals: 'Equals',\n        notEquals: 'Not equals',\n        noFilter: 'No Filter',\n        lt: 'Less than',\n        lte: 'Less than or equal to',\n        gt: 'Greater than',\n        gte: 'Greater than or equal to',\n        dateIs: 'Date is',\n        dateIsNot: 'Date is not',\n        dateBefore: 'Date is before',\n        dateAfter: 'Date is after',\n        clear: 'Clear',\n        apply: 'Apply',\n        matchAll: 'Match All',\n        matchAny: 'Match Any',\n        addRule: 'Add Rule',\n        removeRule: 'Remove Rule',\n        accept: 'Yes',\n        reject: 'No',\n        choose: 'Choose',\n        upload: 'Upload',\n        cancel: 'Cancel',\n        completed: 'Completed',\n        pending: 'Pending',\n        fileSizeTypes: ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],\n        dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n        dayNamesShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n        dayNamesMin: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],\n        monthNames: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],\n        monthNamesShort: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n        chooseYear: 'Choose Year',\n        chooseMonth: 'Choose Month',\n        chooseDate: 'Choose Date',\n        prevDecade: 'Previous Decade',\n        nextDecade: 'Next Decade',\n        prevYear: 'Previous Year',\n        nextYear: 'Next Year',\n        prevMonth: 'Previous Month',\n        nextMonth: 'Next Month',\n        prevHour: 'Previous Hour',\n        nextHour: 'Next Hour',\n        prevMinute: 'Previous Minute',\n        nextMinute: 'Next Minute',\n        prevSecond: 'Previous Second',\n        nextSecond: 'Next Second',\n        am: 'am',\n        pm: 'pm',\n        today: 'Today',\n        weekHeader: 'Wk',\n        firstDayOfWeek: 0,\n        showMonthAfterYear: false,\n        dateFormat: 'mm/dd/yy',\n        weak: 'Weak',\n        medium: 'Medium',\n        strong: 'Strong',\n        passwordPrompt: 'Enter a password',\n        emptyFilterMessage: 'No results found',\n        searchMessage: '{0} results are available',\n        selectionMessage: '{0} items selected',\n        emptySelectionMessage: 'No selected item',\n        emptySearchMessage: 'No results found',\n        fileChosenMessage: '{0} files',\n        noFileChosenMessage: 'No file chosen',\n        emptyMessage: 'No available options',\n        aria: {\n            trueLabel: 'True',\n            falseLabel: 'False',\n            nullLabel: 'Not Selected',\n            star: '1 star',\n            stars: '{star} stars',\n            selectAll: 'All items selected',\n            unselectAll: 'All items unselected',\n            close: 'Close',\n            previous: 'Previous',\n            next: 'Next',\n            navigation: 'Navigation',\n            scrollTop: 'Scroll Top',\n            moveTop: 'Move Top',\n            moveUp: 'Move Up',\n            moveDown: 'Move Down',\n            moveBottom: 'Move Bottom',\n            moveToTarget: 'Move to Target',\n            moveToSource: 'Move to Source',\n            moveAllToTarget: 'Move All to Target',\n            moveAllToSource: 'Move All to Source',\n            pageLabel: 'Page {page}',\n            firstPageLabel: 'First Page',\n            lastPageLabel: 'Last Page',\n            nextPageLabel: 'Next Page',\n            prevPageLabel: 'Previous Page',\n            rowsPerPageLabel: 'Rows per page',\n            jumpToPageDropdownLabel: 'Jump to Page Dropdown',\n            jumpToPageInputLabel: 'Jump to Page Input',\n            selectRow: 'Row Selected',\n            unselectRow: 'Row Unselected',\n            expandRow: 'Row Expanded',\n            collapseRow: 'Row Collapsed',\n            showFilterMenu: 'Show Filter Menu',\n            hideFilterMenu: 'Hide Filter Menu',\n            filterOperator: 'Filter Operator',\n            filterConstraint: 'Filter Constraint',\n            editRow: 'Row Edit',\n            saveEdit: 'Save Edit',\n            cancelEdit: 'Cancel Edit',\n            listView: 'List View',\n            gridView: 'Grid View',\n            slide: 'Slide',\n            slideNumber: '{slideNumber}',\n            zoomImage: 'Zoom Image',\n            zoomIn: 'Zoom In',\n            zoomOut: 'Zoom Out',\n            rotateRight: 'Rotate Right',\n            rotateLeft: 'Rotate Left',\n            listLabel: 'Option List'\n        }\n    },\n    filterMatchModeOptions: {\n        text: [FilterMatchMode.STARTS_WITH, FilterMatchMode.CONTAINS, FilterMatchMode.NOT_CONTAINS, FilterMatchMode.ENDS_WITH, FilterMatchMode.EQUALS, FilterMatchMode.NOT_EQUALS],\n        numeric: [FilterMatchMode.EQUALS, FilterMatchMode.NOT_EQUALS, FilterMatchMode.LESS_THAN, FilterMatchMode.LESS_THAN_OR_EQUAL_TO, FilterMatchMode.GREATER_THAN, FilterMatchMode.GREATER_THAN_OR_EQUAL_TO],\n        date: [FilterMatchMode.DATE_IS, FilterMatchMode.DATE_IS_NOT, FilterMatchMode.DATE_BEFORE, FilterMatchMode.DATE_AFTER]\n    },\n    zIndex: {\n        modal: 1100,\n        overlay: 1000,\n        menu: 1000,\n        tooltip: 1100\n    },\n    theme: undefined,\n    unstyled: false,\n    pt: undefined,\n    ptOptions: {\n        mergeSections: true,\n        mergeProps: false\n    },\n    csp: {\n        nonce: undefined\n    }\n};\n\nconst PrimeVueSymbol = Symbol();\n\nexport function usePrimeVue() {\n    const PrimeVue = inject(PrimeVueSymbol);\n\n    if (!PrimeVue) {\n        throw new Error('PrimeVue is not installed!');\n    }\n\n    return PrimeVue;\n}\n\nexport function setup(app, options) {\n    const PrimeVue = {\n        config: reactive(options)\n    };\n\n    app.config.globalProperties.$primevue = PrimeVue;\n    app.provide(PrimeVueSymbol, PrimeVue);\n\n    clearConfig();\n    setupConfig(app, PrimeVue);\n\n    return PrimeVue;\n}\n\nlet stopWatchers = [];\n\nexport function clearConfig() {\n    ThemeService.clear();\n\n    stopWatchers.forEach((fn) => fn?.());\n    stopWatchers = [];\n}\n\nexport function setupConfig(app, PrimeVue) {\n    const isThemeChanged = ref(false);\n\n    /*** Methods and Services ***/\n    const loadCommonTheme = () => {\n        if (PrimeVue.config?.theme === 'none') return;\n\n        // common\n        if (!Theme.isStyleNameLoaded('common')) {\n            const { primitive, semantic, global, style } = BaseStyle.getCommonTheme?.() || {};\n            const styleOptions = { nonce: PrimeVue.config?.csp?.nonce };\n\n            BaseStyle.load(primitive?.css, { name: 'primitive-variables', ...styleOptions });\n            BaseStyle.load(semantic?.css, { name: 'semantic-variables', ...styleOptions });\n            BaseStyle.load(global?.css, { name: 'global-variables', ...styleOptions });\n            BaseStyle.loadStyle({ name: 'global-style', ...styleOptions }, style);\n\n            Theme.setLoadedStyleName('common');\n        }\n    };\n\n    ThemeService.on('theme:change', function (newTheme) {\n        if (!isThemeChanged.value) {\n            app.config.globalProperties.$primevue.config.theme = newTheme;\n            isThemeChanged.value = true;\n        }\n    });\n\n    /*** Watchers ***/\n    const stopConfigWatcher = watch(\n        PrimeVue.config,\n        (newValue, oldValue) => {\n            PrimeVueService.emit('config:change', { newValue, oldValue });\n        },\n        { immediate: true, deep: true }\n    );\n\n    const stopRippleWatcher = watch(\n        () => PrimeVue.config.ripple,\n        (newValue, oldValue) => {\n            PrimeVueService.emit('config:ripple:change', { newValue, oldValue });\n        },\n        { immediate: true, deep: true }\n    );\n\n    const stopThemeWatcher = watch(\n        () => PrimeVue.config.theme,\n        (newValue, oldValue) => {\n            if (!isThemeChanged.value) {\n                Theme.setTheme(newValue);\n            }\n\n            if (!PrimeVue.config.unstyled) {\n                loadCommonTheme();\n            }\n\n            isThemeChanged.value = false;\n            PrimeVueService.emit('config:theme:change', { newValue, oldValue });\n        },\n        { immediate: true, deep: false }\n    );\n\n    const stopUnstyledWatcher = watch(\n        () => PrimeVue.config.unstyled,\n        (newValue, oldValue) => {\n            if (!newValue && PrimeVue.config.theme) {\n                loadCommonTheme();\n            }\n\n            PrimeVueService.emit('config:unstyled:change', { newValue, oldValue });\n        },\n        { immediate: true, deep: true }\n    );\n\n    stopWatchers.push(stopConfigWatcher);\n    stopWatchers.push(stopRippleWatcher);\n    stopWatchers.push(stopThemeWatcher);\n    stopWatchers.push(stopUnstyledWatcher);\n}\n\nexport default {\n    install: (app, options) => {\n        const configOptions = mergeKeys(defaultOptions, options);\n\n        setup(app, configOptions);\n    }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAMA,iBAAiB;EAC1BC,QAAQ;EACRC,YAAY;EACZC,cAAc;EACdC,QAAQ;IACJC,YAAY;IACZC,UAAU;IACVC,aAAa;IACbC,UAAU;IACVC,QAAQ;IACRC,WAAW;IACXC,UAAU;IACVC,IAAI;IACJC,KAAK;IACLC,IAAI;IACJC,KAAK;IACLC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC,WAAW;IACXC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,UAAU;IACVC,SAAS;IACTC,YAAY;IACZC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,WAAW;IACXC,SAAS;IACTC,eAAe,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;IACnEC,UAAU,CAAC,UAAU,UAAU,WAAW,aAAa,YAAY,UAAU,UAAU;IACvFC,eAAe,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;IAC/DC,aAAa,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;IACtDC,YAAY,CAAC,WAAW,YAAY,SAAS,SAAS,OAAO,QAAQ,QAAQ,UAAU,aAAa,WAAW,YAAY,UAAU;IACrIC,iBAAiB,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;IACpGC,YAAY;IACZC,aAAa;IACbC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,UAAU;IACVC,UAAU;IACVC,WAAW;IACXC,WAAW;IACXC,UAAU;IACVC,UAAU;IACVC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,IAAI;IACJC,IAAI;IACJC,OAAO;IACPC,YAAY;IACZC,gBAAgB;IAChBC,oBAAoB;IACpBC,YAAY;IACZC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,gBAAgB;IAChBC,oBAAoB;IACpBC,eAAe;IACfC,kBAAkB;IAClBC,uBAAuB;IACvBC,oBAAoB;IACpBC,mBAAmB;IACnBC,qBAAqB;IACrBC,cAAc;IACdC,MAAM;MACFC,WAAW;MACXC,YAAY;MACZC,WAAW;MACXC,MAAM;MACNC,OAAO;MACPC,WAAW;MACXC,aAAa;MACbC,OAAO;MACPC,UAAU;MACVC,MAAM;MACNC,YAAY;MACZC,WAAW;MACXC,SAAS;MACTC,QAAQ;MACRC,UAAU;MACVC,YAAY;MACZC,cAAc;MACdC,cAAc;MACdC,iBAAiB;MACjBC,iBAAiB;MACjBC,WAAW;MACXC,gBAAgB;MAChBC,eAAe;MACfC,eAAe;MACfC,eAAe;MACfC,kBAAkB;MAClBC,yBAAyB;MACzBC,sBAAsB;MACtBC,WAAW;MACXC,aAAa;MACbC,WAAW;MACXC,aAAa;MACbC,gBAAgB;MAChBC,gBAAgB;MAChBC,gBAAgB;MAChBC,kBAAkB;MAClBC,SAAS;MACTC,UAAU;MACVC,YAAY;MACZC,UAAU;MACVC,UAAU;MACVC,OAAO;MACPC,aAAa;MACbC,WAAW;MACXC,QAAQ;MACRC,SAAS;MACTC,aAAa;MACbC,YAAY;MACZC,WAAW;IACf;;EAEJC,wBAAwB;IACpBC,MAAM,CAACC,gBAAgBC,aAAaD,gBAAgBE,UAAUF,gBAAgBG,cAAcH,gBAAgBI,WAAWJ,gBAAgBK,QAAQL,gBAAgBM,UAAU;IACzKC,SAAS,CAACP,gBAAgBK,QAAQL,gBAAgBM,YAAYN,gBAAgBQ,WAAWR,gBAAgBS,uBAAuBT,gBAAgBU,cAAcV,gBAAgBW,wBAAwB;IACtMC,MAAM,CAACZ,gBAAgBa,SAASb,gBAAgBc,aAAad,gBAAgBe,aAAaf,gBAAgBgB,UAAU;;EAExHC,QAAQ;IACJC,OAAO;IACPC,SAAS;IACTC,MAAM;IACNC,SAAS;;EAEbC,OAAOC;EACPC,UAAU;EACVC,IAAIF;EACJG,WAAW;IACPC,eAAe;IACfC,YAAY;;EAEhBC,KAAK;IACDC,OAAOP;EACX;AACJ;AAEA,IAAMQ,iBAAiBC,OAAM;AAEtB,SAASC,cAAc;AAC1B,MAAMC,YAAWC,OAAOJ,cAAc;AAEtC,MAAI,CAACG,WAAU;AACX,UAAM,IAAIE,MAAM,4BAA4B;EAChD;AAEA,SAAOF;AACX;AAEO,SAASG,MAAMC,KAAKC,SAAS;AAChC,MAAML,YAAW;IACbM,QAAQC,SAASF,OAAO;;AAG5BD,MAAIE,OAAOE,iBAAiBC,YAAYT;AACxCI,MAAIM,QAAQb,gBAAgBG,SAAQ;AAEpCW,cAAW;AACXC,cAAYR,KAAKJ,SAAQ;AAEzB,SAAOA;AACX;AAEA,IAAIa,eAAe,CAAA;AAEZ,SAASF,cAAc;AAC1BG,kBAAazJ,MAAK;AAElBwJ,eAAaE,QAAQ,SAACC,IAAE;AAAA,WAAKA,OAAAA,QAAAA,OAAAA,SAAAA,SAAAA,GAAE;GAAK;AACpCH,iBAAe,CAAA;AACnB;AAEO,SAASD,YAAYR,KAAKJ,WAAU;AACvC,MAAMiB,iBAAiBC,IAAI,KAAK;AAGhC,MAAMC,kBAAkB,SAAlBA,mBAAwB;AAAA,QAAAC;AAC1B,UAAIA,mBAAApB,UAASM,YAAM,QAAAc,qBAAA,SAAA,SAAfA,iBAAiBhC,WAAU,OAAQ;AAGvC,QAAI,CAACiC,eAAMC,kBAAkB,QAAQ,GAAG;AAAA,UAAAC,uBAAAC;AACpC,UAAAC,SAA+CF,wBAAAG,UAAUC,oBAAc,QAAAJ,0BAAxBA,SAAAA,SAAAA,sBAAAK,KAAAF,SAA2B,MAAK,CAAA,GAAvEG,YAASJ,KAATI,WAAWC,WAAQL,KAARK,UAAUC,SAAMN,KAANM,QAAQC,QAAKP,KAALO;AACrC,UAAMC,eAAe;QAAErC,QAAK4B,oBAAExB,UAASM,YAAM,QAAAkB,sBAAA,WAAAA,oBAAfA,kBAAiB7B,SAAG,QAAA6B,sBAAA,SAAA,SAApBA,kBAAsB5B;;AAEpD8B,gBAAUQ,KAAKL,cAAS,QAATA,cAAS,SAAA,SAATA,UAAWM,KAAGC,cAAA;QAAIC,MAAM;SAA0BJ,YAAY,CAAE;AAC/EP,gBAAUQ,KAAKJ,aAAQ,QAARA,aAAQ,SAAA,SAARA,SAAUK,KAAGC,cAAA;QAAIC,MAAM;SAAyBJ,YAAY,CAAE;AAC7EP,gBAAUQ,KAAKH,WAAM,QAANA,WAAM,SAAA,SAANA,OAAQI,KAAGC,cAAA;QAAIC,MAAM;SAAuBJ,YAAY,CAAE;AACzEP,gBAAUY,UAASF,cAAA;QAAGC,MAAM;SAAmBJ,YAAY,GAAID,KAAK;AAEpEX,qBAAMkB,mBAAmB,QAAQ;IACrC;;AAGJzB,kBAAa0B,GAAG,gBAAgB,SAAUC,UAAU;AAChD,QAAI,CAACxB,eAAeyB,OAAO;AACvBtC,UAAIE,OAAOE,iBAAiBC,UAAUH,OAAOlB,QAAQqD;AACrDxB,qBAAeyB,QAAQ;IAC3B;EACJ,CAAC;AAGD,MAAMC,oBAAoBC,MACtB5C,UAASM,QACT,SAACuC,UAAUC,UAAa;AACpBC,oBAAgBC,KAAK,iBAAiB;MAAEH;MAAUC;IAAS,CAAC;EAChE,GACA;IAAEG,WAAW;IAAMC,MAAM;EAAK,CAClC;AAEA,MAAMC,oBAAoBP,MACtB,WAAA;AAAA,WAAM5C,UAASM,OAAOpK;EAAM,GAC5B,SAAC2M,UAAUC,UAAa;AACpBC,oBAAgBC,KAAK,wBAAwB;MAAEH;MAAUC;IAAS,CAAC;EACvE,GACA;IAAEG,WAAW;IAAMC,MAAM;EAAK,CAClC;AAEA,MAAME,mBAAmBR,MACrB,WAAA;AAAA,WAAM5C,UAASM,OAAOlB;EAAK,GAC3B,SAACyD,UAAUC,UAAa;AACpB,QAAI,CAAC7B,eAAeyB,OAAO;AACvBrB,qBAAMgC,SAASR,QAAQ;IAC3B;AAEA,QAAI,CAAC7C,UAASM,OAAOhB,UAAU;AAC3B6B,sBAAe;IACnB;AAEAF,mBAAeyB,QAAQ;AACvBK,oBAAgBC,KAAK,uBAAuB;MAAEH;MAAUC;IAAS,CAAC;EACtE,GACA;IAAEG,WAAW;IAAMC,MAAM;EAAM,CACnC;AAEA,MAAMI,sBAAsBV,MACxB,WAAA;AAAA,WAAM5C,UAASM,OAAOhB;EAAQ,GAC9B,SAACuD,UAAUC,UAAa;AACpB,QAAI,CAACD,YAAY7C,UAASM,OAAOlB,OAAO;AACpC+B,sBAAe;IACnB;AAEA4B,oBAAgBC,KAAK,0BAA0B;MAAEH;MAAUC;IAAS,CAAC;EACzE,GACA;IAAEG,WAAW;IAAMC,MAAM;EAAK,CAClC;AAEArC,eAAa0C,KAAKZ,iBAAiB;AACnC9B,eAAa0C,KAAKJ,iBAAiB;AACnCtC,eAAa0C,KAAKH,gBAAgB;AAClCvC,eAAa0C,KAAKD,mBAAmB;AACzC;AAEA,IAAA,WAAe;EACXE,SAAS,SAATA,QAAUpD,KAAKC,SAAY;AACvB,QAAMoD,gBAAgBC,UAAUzN,gBAAgBoK,OAAO;AAEvDF,UAAMC,KAAKqD,aAAa;EAC5B;AACJ;", "names": ["defaultOptions", "ripple", "inputStyle", "inputVariant", "locale", "startsWith", "contains", "notContains", "endsWith", "equals", "notEquals", "noFilter", "lt", "lte", "gt", "gte", "dateIs", "dateIsNot", "dateBefore", "dateAfter", "clear", "apply", "matchAll", "matchAny", "addRule", "removeRule", "accept", "reject", "choose", "upload", "cancel", "completed", "pending", "fileSizeTypes", "dayNames", "dayNamesShort", "dayNamesMin", "monthNames", "monthNamesShort", "chooseYear", "choose<PERSON>ont<PERSON>", "chooseDate", "prevDecade", "nextDecade", "prevYear", "nextYear", "prevMonth", "nextMonth", "prevHour", "nextHour", "prevMinute", "nextMinute", "prevSecond", "nextSecond", "am", "pm", "today", "weekHeader", "firstDayOfWeek", "showMonthAfterYear", "dateFormat", "weak", "medium", "strong", "passwordPrompt", "emptyFilterMessage", "searchMessage", "selectionMessage", "emptySelectionMessage", "emptySearchMessage", "fileChosenMessage", "noFileChosenMessage", "emptyMessage", "aria", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "star", "stars", "selectAll", "unselectAll", "close", "previous", "next", "navigation", "scrollTop", "moveTop", "moveUp", "moveDown", "moveBottom", "move<PERSON><PERSON><PERSON>arget", "moveToSource", "moveAllToTarget", "moveAllToSource", "pageLabel", "firstPageLabel", "lastPageLabel", "nextPageLabel", "prevPageLabel", "rowsPerPageLabel", "jumpToPageDropdownLabel", "jumpToPageInputLabel", "selectRow", "unselectRow", "expandRow", "collapseRow", "showFilterMenu", "hideFilterMenu", "filterOperator", "filterConstraint", "editRow", "saveEdit", "cancelEdit", "listView", "gridView", "slide", "slideNumber", "zoomImage", "zoomIn", "zoomOut", "rotateRight", "rotateLeft", "listLabel", "filterMatchModeOptions", "text", "FilterMatchMode", "STARTS_WITH", "CONTAINS", "NOT_CONTAINS", "ENDS_WITH", "EQUALS", "NOT_EQUALS", "numeric", "LESS_THAN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN", "GREATER_THAN_OR_EQUAL_TO", "date", "DATE_IS", "DATE_IS_NOT", "DATE_BEFORE", "DATE_AFTER", "zIndex", "modal", "overlay", "menu", "tooltip", "theme", "undefined", "unstyled", "pt", "ptOptions", "mergeSections", "mergeProps", "csp", "nonce", "PrimeVueSymbol", "Symbol", "usePrimeVue", "PrimeVue", "inject", "Error", "setup", "app", "options", "config", "reactive", "globalProperties", "$primevue", "provide", "clearConfig", "setupConfig", "stopWatchers", "ThemeService", "for<PERSON>ach", "fn", "isThemeChanged", "ref", "loadCommonTheme", "_PrimeVue$config", "Theme", "isStyleNameLoaded", "_BaseStyle$getCommonT", "_PrimeVue$config2", "_ref", "BaseStyle", "getCommonTheme", "call", "primitive", "semantic", "global", "style", "styleOptions", "load", "css", "_objectSpread", "name", "loadStyle", "setLoadedStyleName", "on", "newTheme", "value", "stopConfigWatcher", "watch", "newValue", "oldValue", "PrimeVueService", "emit", "immediate", "deep", "stopRippleWatcher", "stopThemeWatcher", "setTheme", "stopUnstyledWatcher", "push", "install", "configOptions", "mergeKeys"]}