{"version": 3, "file": "index.mjs", "sources": ["../../src/accordioncontent/BaseAccordionContent.vue", "../../src/accordioncontent/AccordionContent.vue", "../../src/accordioncontent/AccordionContent.vue?vue&type=template&id=55360607&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport AccordionContentStyle from 'primevue/accordioncontent/style';\n\nexport default {\n    name: 'BaseAccordionContent',\n    extends: BaseComponent,\n    props: {\n        as: {\n            type: [String, Object],\n            default: 'DIV'\n        },\n        asChild: {\n            type: Boolean,\n            default: false\n        }\n    },\n    style: AccordionContentStyle,\n    provide() {\n        return {\n            $pcAccordionContent: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <template v-if=\"!asChild\">\n        <transition name=\"p-toggleable-content\" v-bind=\"ptm('transition', ptParams)\">\n            <component v-if=\"$pcAccordion.lazy ? $pcAccordionPanel.active : true\" v-show=\"$pcAccordion.lazy ? true : $pcAccordionPanel.active\" :is=\"as\" :class=\"cx('root')\" v-bind=\"attrs\">\n                <div :class=\"cx('content')\" v-bind=\"ptm('content', ptParams)\">\n                    <slot></slot>\n                </div>\n            </component>\n        </transition>\n    </template>\n    <slot v-else :class=\"cx('root')\" :active=\"$pcAccordionPanel.active\" :a11yAttrs=\"a11yAttrs\"></slot>\n</template>\n\n<script>\nimport { mergeProps } from 'vue';\nimport BaseAccordionContent from './BaseAccordionContent.vue';\n\nexport default {\n    name: 'AccordionContent',\n    extends: BaseAccordionContent,\n    inheritAttrs: false,\n    inject: ['$pcAccordion', '$pcAccordionPanel'],\n    computed: {\n        id() {\n            return `${this.$pcAccordion.id}_accordioncontent_${this.$pcAccordionPanel.value}`;\n        },\n        ariaLabelledby() {\n            return `${this.$pcAccordion.id}_accordionheader_${this.$pcAccordionPanel.value}`;\n        },\n        attrs() {\n            return mergeProps(this.a11yAttrs, this.ptmi('root', this.ptParams));\n        },\n        a11yAttrs() {\n            return {\n                id: this.id,\n                role: 'region',\n                'aria-labelledby': this.ariaLabelledby,\n                'data-pc-name': 'accordioncontent',\n                'data-p-active': this.$pcAccordionPanel.active\n            };\n        },\n        ptParams() {\n            return {\n                context: {\n                    active: this.$pcAccordionPanel.active\n                }\n            };\n        }\n    }\n};\n</script>\n", "<template>\n    <template v-if=\"!asChild\">\n        <transition name=\"p-toggleable-content\" v-bind=\"ptm('transition', ptParams)\">\n            <component v-if=\"$pcAccordion.lazy ? $pcAccordionPanel.active : true\" v-show=\"$pcAccordion.lazy ? true : $pcAccordionPanel.active\" :is=\"as\" :class=\"cx('root')\" v-bind=\"attrs\">\n                <div :class=\"cx('content')\" v-bind=\"ptm('content', ptParams)\">\n                    <slot></slot>\n                </div>\n            </component>\n        </transition>\n    </template>\n    <slot v-else :class=\"cx('root')\" :active=\"$pcAccordionPanel.active\" :a11yAttrs=\"a11yAttrs\"></slot>\n</template>\n\n<script>\nimport { mergeProps } from 'vue';\nimport BaseAccordionContent from './BaseAccordionContent.vue';\n\nexport default {\n    name: 'AccordionContent',\n    extends: BaseAccordionContent,\n    inheritAttrs: false,\n    inject: ['$pcAccordion', '$pcAccordionPanel'],\n    computed: {\n        id() {\n            return `${this.$pcAccordion.id}_accordioncontent_${this.$pcAccordionPanel.value}`;\n        },\n        ariaLabelledby() {\n            return `${this.$pcAccordion.id}_accordionheader_${this.$pcAccordionPanel.value}`;\n        },\n        attrs() {\n            return mergeProps(this.a11yAttrs, this.ptmi('root', this.ptParams));\n        },\n        a11yAttrs() {\n            return {\n                id: this.id,\n                role: 'region',\n                'aria-labelledby': this.ariaLabelledby,\n                'data-pc-name': 'accordioncontent',\n                'data-p-active': this.$pcAccordionPanel.active\n            };\n        },\n        ptParams() {\n            return {\n                context: {\n                    active: this.$pcAccordionPanel.active\n                }\n            };\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "as", "type", "String", "Object", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "style", "AccordionContentStyle", "provide", "$pcAccordionContent", "$parentInstance", "BaseAccordion<PERSON><PERSON>nt", "inheritAttrs", "inject", "computed", "id", "concat", "$pcAccordion", "$pcAccordionPanel", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attrs", "mergeProps", "a11yAttrs", "ptmi", "ptParams", "role", "active", "context", "_ctx", "_openBlock", "_createBlock", "_Transition", "_mergeProps", "ptm", "$options", "lazy", "_resolveDynamicComponent", "cx", "_createElementVNode", "_renderSlot", "$slots", "_vShow"], "mappings": ";;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,sBAAsB;AAC5B,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,EAAE,EAAE;AACAC,MAAAA,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDC,IAAAA,OAAO,EAAE;AACLH,MAAAA,IAAI,EAAEI,OAAO;MACb,SAAS,EAAA;AACb;GACH;AACDC,EAAAA,KAAK,EAAEC,qBAAqB;EAC5BC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,mBAAmB,EAAE,IAAI;AACzBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACPD,aAAe;AACXb,EAAAA,IAAI,EAAE,kBAAkB;AACxB,EAAA,SAAA,EAASc,QAAoB;AAC7BC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,MAAM,EAAE,CAAC,cAAc,EAAE,mBAAmB,CAAC;AAC7CC,EAAAA,QAAQ,EAAE;IACNC,EAAE,EAAA,SAAFA,EAAEA,GAAG;AACD,MAAA,OAAA,EAAA,CAAAC,MAAA,CAAU,IAAI,CAACC,YAAY,CAACF,EAAE,EAAAC,oBAAAA,CAAAA,CAAAA,MAAA,CAAqB,IAAI,CAACE,iBAAiB,CAACC,KAAK,CAAA;KAClF;IACDC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,OAAA,EAAA,CAAAJ,MAAA,CAAU,IAAI,CAACC,YAAY,CAACF,EAAE,EAAAC,mBAAAA,CAAAA,CAAAA,MAAA,CAAoB,IAAI,CAACE,iBAAiB,CAACC,KAAK,CAAA;KACjF;IACDE,KAAK,EAAA,SAALA,KAAKA,GAAG;AACJ,MAAA,OAAOC,UAAU,CAAC,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,IAAI,CAAC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;KACtE;IACDF,SAAS,EAAA,SAATA,SAASA,GAAG;MACR,OAAO;QACHR,EAAE,EAAE,IAAI,CAACA,EAAE;AACXW,QAAAA,IAAI,EAAE,QAAQ;QACd,iBAAiB,EAAE,IAAI,CAACN,cAAc;AACtC,QAAA,cAAc,EAAE,kBAAkB;AAClC,QAAA,eAAe,EAAE,IAAI,CAACF,iBAAiB,CAACS;OAC3C;KACJ;IACDF,QAAQ,EAAA,SAARA,QAAQA,GAAG;MACP,OAAO;AACHG,QAAAA,OAAO,EAAE;AACLD,UAAAA,MAAM,EAAE,IAAI,CAACT,iBAAiB,CAACS;AACnC;OACH;AACL;AACJ;AACJ,CAAC;;;UChDoBE,IAAO,CAAAzB,OAAA,IACpB0B,SAAA,EAAA,EAAAC,WAAA,CAMYC,YANZC,UAMY,CAAA;;AANApC,IAAAA,IAAI,EAAC;AAA+B,GAAA,EAAAgC,IAAA,CAAAK,GAAG,eAAeC,QAAQ,CAAAV,QAAA,CAAA,CAAA,EAAA;uBACtE,YAAA;AAAA,MAAA,OAIW,CAJM,CAAAU,QAAA,CAAAlB,YAAY,CAACmB,IAAK,GAAED,QAAA,CAAAjB,iBAAiB,CAACS,MAAO,GAAA,IAAA,iCAA9DI,WAIW,CAAAM,uBAAA,CAJ6HR,IAAE,CAAA7B,EAAA,CAAA,EAA1IiC,UAIW,CAAA;;AAJkI,QAAA,OAAA,EAAOJ,IAAE,CAAAS,EAAA,CAAA,MAAA;SAAkBH,QAAK,CAAAd,KAAA,CAAA,EAAA;2BACzK,YAAA;AAAA,UAAA,OAEK,CAFLkB,kBAAA,CAEK,OAFLN,UAEK,CAAA;AAFC,YAAA,OAAA,EAAOJ,IAAE,CAAAS,EAAA,CAAA,SAAA;WAAqB,EAAAT,IAAA,CAAAK,GAAG,YAAYC,QAAQ,CAAAV,QAAA,CAAA,CAAA,EAAA,CACvDe,UAAY,CAAAX,IAAA,CAAAY,MAAA,EAAA,SAAA,CAAA;;;2BAF0D,CAAAC,KAAA,EAAAP,QAAA,CAAAlB,YAAY,CAACmB,IAAK,GAAS,IAAA,GAAAD,QAAA,CAAAjB,iBAAiB,CAACS,MAAM,CAAA;;;YAOzIa,UAAiG,CAAAX,IAAA,CAAAY,MAAA,EAAA,SAAA,EAAA;;IAAnF,wBAAOZ,IAAE,CAAAS,EAAA,CAAA,MAAA,CAAA,CAAA;AAAWX,IAAAA,MAAM,EAAEQ,QAAiB,CAAAjB,iBAAA,CAACS,MAAM;IAAGJ,SAAS,EAAEY,QAAS,CAAAZ;;;;;;;;"}