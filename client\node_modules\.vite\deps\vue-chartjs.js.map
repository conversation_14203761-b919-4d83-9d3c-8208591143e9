{"version": 3, "sources": ["../../../../node_modules/vue-chartjs/src/props.ts", "../../../../node_modules/vue-chartjs/src/utils.ts", "../../../../node_modules/vue-chartjs/src/chart.ts", "../../../../node_modules/vue-chartjs/src/typedCharts.ts"], "sourcesContent": ["import type { PropType } from 'vue'\nimport type {\n  ChartType,\n  ChartData,\n  ChartOptions,\n  Plugin,\n  UpdateMode\n} from 'chart.js'\n\nexport const CommonProps = {\n  data: {\n    type: Object as PropType<ChartData>,\n    required: true\n  },\n  options: {\n    type: Object as PropType<ChartOptions>,\n    default: () => ({})\n  },\n  plugins: {\n    type: Array as PropType<Plugin[]>,\n    default: () => []\n  },\n  datasetIdKey: {\n    type: String,\n    default: 'label'\n  },\n  updateMode: {\n    type: String as PropType<UpdateMode>,\n    default: undefined\n  }\n} as const\n\nexport const A11yProps = {\n  ariaLabel: {\n    type: String\n  },\n  ariaDescribedby: {\n    type: String\n  }\n} as const\n\nexport const Props = {\n  type: {\n    type: String as PropType<ChartType>,\n    required: true\n  },\n  destroyDelay: {\n    type: Number,\n    default: 0 // No delay by default\n  },\n  ...CommonProps,\n  ...A11yProps\n} as const\n", "import { isProxy, toRaw, version } from 'vue'\nimport type {\n  Chart,\n  ChartType,\n  ChartData,\n  ChartDataset,\n  ChartOptions,\n  DefaultDataPoint\n} from 'chart.js'\n\nexport const compatProps =\n  version[0] === '2'\n    ? <I extends {}, T extends {}>(internals: I, props: T) =>\n        Object.assign(internals, { attrs: props }) as unknown as I & T\n    : <I extends {}, T extends {}>(internals: I, props: T) =>\n        Object.assign(internals, props)\n\nexport function toRawIfProxy<T>(obj: T) {\n  return isProxy(obj) ? toRaw(obj) : obj\n}\n\nexport function cloneProxy<T extends object>(obj: T, src = obj) {\n  return isProxy(src) ? new Proxy(obj, {}) : obj\n}\n\nexport function setOptions<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown\n>(chart: Chart<TType, TData, TLabel>, nextOptions: ChartOptions<TType>) {\n  const options = chart.options\n\n  if (options && nextOptions) {\n    Object.assign(options, nextOptions)\n  }\n}\n\nexport function setLabels<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown\n>(\n  currentData: ChartData<TType, TData, TLabel>,\n  nextLabels: TLabel[] | undefined\n) {\n  currentData.labels = nextLabels\n}\n\nexport function setDatasets<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown\n>(\n  currentData: ChartData<TType, TData, TLabel>,\n  nextDatasets: ChartDataset<TType, TData>[],\n  datasetIdKey: string\n) {\n  const addedDatasets: ChartDataset<TType, TData>[] = []\n\n  currentData.datasets = nextDatasets.map(\n    (nextDataset: Record<string, unknown>) => {\n      // given the new set, find it's current match\n      const currentDataset = currentData.datasets.find(\n        (dataset: Record<string, unknown>) =>\n          dataset[datasetIdKey] === nextDataset[datasetIdKey]\n      )\n\n      // There is no original to update, so simply add new one\n      if (\n        !currentDataset ||\n        !nextDataset.data ||\n        addedDatasets.includes(currentDataset)\n      ) {\n        return { ...nextDataset } as ChartDataset<TType, TData>\n      }\n\n      addedDatasets.push(currentDataset)\n\n      Object.assign(currentDataset, nextDataset)\n\n      return currentDataset\n    }\n  )\n}\n\nexport function cloneData<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown\n>(data: ChartData<TType, TData, TLabel>, datasetIdKey: string) {\n  const nextData: ChartData<TType, TData, TLabel> = {\n    labels: [],\n    datasets: []\n  }\n\n  setLabels(nextData, data.labels)\n  setDatasets(nextData, data.datasets, datasetIdKey)\n\n  return nextData\n}\n\n/**\n * Get dataset from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getDatasetAtEvent(chart: Chart, event: MouseEvent) {\n  return chart.getElementsAtEventForMode(\n    event,\n    'dataset',\n    { intersect: true },\n    false\n  )\n}\n\n/**\n * Get single dataset element from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getElementAtEvent(chart: Chart, event: MouseEvent) {\n  return chart.getElementsAtEventForMode(\n    event,\n    'nearest',\n    { intersect: true },\n    false\n  )\n}\n\n/**\n * Get all dataset elements from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getElementsAtEvent(chart: Chart, event: MouseEvent) {\n  return chart.getElementsAtEventForMode(\n    event,\n    'index',\n    { intersect: true },\n    false\n  )\n}\n", "import { Chart as ChartJS } from 'chart.js'\nimport {\n  defineComponent,\n  h,\n  nextTick,\n  onUnmounted,\n  onMounted,\n  ref,\n  shallowRef,\n  toRaw,\n  watch\n} from 'vue'\n\nimport type { ChartComponent } from './types.js'\nimport { Props } from './props.js'\nimport {\n  cloneData,\n  setLabels,\n  setDatasets,\n  setOptions,\n  toRawIfProxy,\n  cloneProxy\n} from './utils.js'\n\nexport const Chart = defineComponent({\n  props: Props,\n  setup(props, { expose, slots }) {\n    const canvasRef = ref<HTMLCanvasElement | null>(null)\n    const chartRef = shallowRef<ChartJS | null>(null)\n\n    expose({ chart: chartRef })\n\n    const renderChart = () => {\n      if (!canvasRef.value) return\n\n      const { type, data, options, plugins, datasetIdKey } = props\n      const clonedData = cloneData(data, datasetIdKey)\n      const proxiedData = cloneProxy(clonedData, data)\n\n      chartRef.value = new ChartJS(canvasRef.value, {\n        type,\n        data: proxiedData,\n        options: { ...options },\n        plugins\n      })\n    }\n\n    const destroyChart = () => {\n      const chart = toRaw(chartRef.value)\n\n      if (chart) {\n        if (props.destroyDelay > 0) {\n          setTimeout(() => {\n            chart.destroy()\n            chartRef.value = null\n          }, props.destroyDelay)\n        } else {\n          chart.destroy()\n          chartRef.value = null\n        }\n      }\n    }\n\n    const update = (chart: ChartJS) => {\n      chart.update(props.updateMode)\n    }\n\n    onMounted(renderChart)\n\n    onUnmounted(destroyChart)\n\n    watch(\n      [() => props.options, () => props.data],\n      (\n        [nextOptionsProxy, nextDataProxy],\n        [prevOptionsProxy, prevDataProxy]\n      ) => {\n        const chart = toRaw(chartRef.value)\n\n        if (!chart) {\n          return\n        }\n\n        let shouldUpdate = false\n\n        if (nextOptionsProxy) {\n          const nextOptions = toRawIfProxy(nextOptionsProxy)\n          const prevOptions = toRawIfProxy(prevOptionsProxy)\n\n          if (nextOptions && nextOptions !== prevOptions) {\n            setOptions(chart, nextOptions)\n            shouldUpdate = true\n          }\n        }\n\n        if (nextDataProxy) {\n          const nextLabels = toRawIfProxy(nextDataProxy.labels)\n          const prevLabels = toRawIfProxy(prevDataProxy.labels)\n          const nextDatasets = toRawIfProxy(nextDataProxy.datasets)\n          const prevDatasets = toRawIfProxy(prevDataProxy.datasets)\n\n          if (nextLabels !== prevLabels) {\n            setLabels(chart.config.data, nextLabels)\n            shouldUpdate = true\n          }\n\n          if (nextDatasets && nextDatasets !== prevDatasets) {\n            setDatasets(chart.config.data, nextDatasets, props.datasetIdKey)\n            shouldUpdate = true\n          }\n        }\n\n        if (shouldUpdate) {\n          nextTick(() => {\n            update(chart)\n          })\n        }\n      },\n      { deep: true }\n    )\n\n    return () => {\n      return h(\n        'canvas',\n        {\n          role: 'img',\n          ariaLabel: props.ariaLabel,\n          ariaDescribedby: props.ariaDescribedby,\n          ref: canvasRef\n        },\n        [h('p', {}, [slots.default ? slots.default() : ''])]\n      )\n    }\n  }\n}) as ChartComponent\n", "import { defineComponent, shallowRef, h } from 'vue'\nimport type { ChartType, ChartComponentLike, DefaultDataPoint } from 'chart.js'\nimport {\n  Chart as ChartJ<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>er,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>roller,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>roller,\n  <PERSON><PERSON><PERSON>roller,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Polar<PERSON>rea<PERSON>ontroller,\n  RadarController,\n  ScatterController\n} from 'chart.js'\nimport type { DistributiveArray } from 'chart.js/dist/types/utils'\nimport type { TypedChartComponent, ChartComponentRef } from './types.js'\nimport { CommonProps } from './props.js'\nimport { Chart } from './chart.js'\nimport { compatProps } from './utils.js'\n\nexport function createTypedChart<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown\n>(\n  type: TType,\n  registerables: ChartComponentLike\n): TypedChartComponent<TType, TData, TLabel> {\n  ChartJS.register(registerables)\n\n  return defineComponent({\n    props: CommonProps,\n    setup(props, { expose }) {\n      const ref = shallowRef<ChartJS | null>(null)\n      const reforwardRef = (chartRef: ChartComponentRef) => {\n        ref.value = chartRef?.chart\n      }\n\n      expose({ chart: ref })\n\n      return () => {\n        return h(\n          Chart,\n          compatProps(\n            {\n              ref: reforwardRef as any\n            },\n            {\n              type,\n              ...props\n            }\n          )\n        )\n      }\n    }\n  }) as any\n}\n\nexport interface ExtendedDataPoint {\n  [key: string]: string | number | null | ExtendedDataPoint\n}\n\nexport const Bar = /* #__PURE__ */ createTypedChart<\n  'bar',\n  DefaultDataPoint<'bar'> | DistributiveArray<ExtendedDataPoint>\n>('bar', BarController)\n\nexport const Doughnut = /* #__PURE__ */ createTypedChart(\n  'doughnut',\n  DoughnutController\n)\n\nexport const Line = /* #__PURE__ */ createTypedChart('line', LineController)\n\nexport const Pie = /* #__PURE__ */ createTypedChart('pie', PieController)\n\nexport const PolarArea = /* #__PURE__ */ createTypedChart(\n  'polarArea',\n  PolarAreaController\n)\n\nexport const Radar = /* #__PURE__ */ createTypedChart('radar', RadarController)\n\nexport const Bubble = /* #__PURE__ */ createTypedChart(\n  'bubble',\n  BubbleController\n)\n\nexport const Scatter = /* #__PURE__ */ createTypedChart(\n  'scatter',\n  ScatterController\n)\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AASO,IAAMA,cAAc;EACzBC,MAAM;IACJC,MAAMC;IACNC,UAAU;EACZ;EACAC,SAAS;IACPH,MAAMC;IACNG,SAAS,OAAO,CAAA;EAClB;EACAC,SAAS;IACPL,MAAMM;IACNF,SAAS,MAAM,CAAA;EACjB;EACAG,cAAc;IACZP,MAAMQ;IACNJ,SAAS;EACX;EACAK,YAAY;IACVT,MAAMQ;IACNJ,SAASM;EACX;AACF;AAEO,IAAMC,YAAY;EACvBC,WAAW;IACTZ,MAAMQ;EACR;EACAK,iBAAiB;IACfb,MAAMQ;EACR;AACF;AAEO,IAAMM,QAAQ;EACnBd,MAAM;IACJA,MAAMQ;IACNN,UAAU;EACZ;EACAa,cAAc;IACZf,MAAMgB;IACNZ,SAAS;;EACX;EACA,GAAGN;EACH,GAAGa;AACL;AC1CO,IAAMM,cACXC,QAAQ,CAAE,MAAK,MACX,CAA6BC,WAAcC,UACzCnB,OAAOoB,OAAOF,WAAW;EAAEG,OAAOF;CACpC,IAAA,CAA6BD,WAAcC,UACzCnB,OAAOoB,OAAOF,WAAWC,KAAAA;AAE1B,SAASG,aAAgBC,KAAQ;AACtC,SAAOC,QAAQD,GAAAA,IAAOE,MAAMF,GAAAA,IAAOA;AACrC;AAEO,SAASG,WAA6BH,KAAmB;AAAXI,MAAAA,MAAAA,UAAAA,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAMJ;AACzD,SAAOC,QAAQG,GAAO,IAAA,IAAIC,MAAML,KAAK,CAAA,CAAA,IAAMA;AAC7C;AAEO,SAASM,WAIdC,OAAoCC,aAAkC;AACtE,QAAM7B,UAAU4B,MAAM5B;AAEtB,MAAIA,WAAW6B,aAAa;AAC1B/B,WAAOoB,OAAOlB,SAAS6B,WAAAA;;AAE3B;AAEO,SAASC,UAKdC,aACAC,YACA;AACAD,cAAYE,SAASD;AACvB;AAEO,SAASE,YAKdH,aACAI,cACA/B,cACA;AACA,QAAMgC,gBAA8C,CAAA;AAEpDL,cAAYM,WAAWF,aAAaG,IAClC,CAACC,gBAAyC;AAExC,UAAMC,iBAAiBT,YAAYM,SAASI,KAC1C,CAACC,YACCA,QAAQtC,YAAAA,MAAkBmC,YAAYnC,YAAa,CAAA;AAIvD,QACE,CAACoC,kBACD,CAACD,YAAY3C,QACbwC,cAAcO,SAASH,cACvB,GAAA;AACA,aAAO;QAAE,GAAGD;MAAY;;AAG1BH,kBAAcQ,KAAKJ,cAAAA;AAEnB1C,WAAOoB,OAAOsB,gBAAgBD,WAAAA;AAE9B,WAAOC;EACT,CAAA;AAEJ;AAEO,SAASK,UAIdjD,MAAuCQ,cAAsB;AAC7D,QAAM0C,WAA4C;IAChDb,QAAQ,CAAA;IACRI,UAAU,CAAA;EACZ;AAEAP,YAAUgB,UAAUlD,KAAKqC,MAAM;AAC/BC,cAAYY,UAAUlD,KAAKyC,UAAUjC,YAAAA;AAErC,SAAO0C;AACT;AAQO,SAASC,kBAAkBnB,OAAcoB,OAAmB;AACjE,SAAOpB,MAAMqB,0BACXD,OACA,WACA;IAAEE,WAAW;EAAK,GAClB,KAAK;AAET;AAQO,SAASC,kBAAkBvB,OAAcoB,OAAmB;AACjE,SAAOpB,MAAMqB,0BACXD,OACA,WACA;IAAEE,WAAW;EAAK,GAClB,KAAK;AAET;AAQO,SAASE,mBAAmBxB,OAAcoB,OAAmB;AAClE,SAAOpB,MAAMqB,0BACXD,OACA,SACA;IAAEE,WAAW;EAAK,GAClB,KAAK;AAET;ACxHO,IAAMG,SAAQC,gBAAgB;EACnCrC,OAAON;EACP4C,MAAMtC,OAAO,OAAmB;AAAnB,QAAA,EAAEuC,QAAQC,MAAK,IAAf;AACX,UAAMC,YAAYC,IAA8B,IAAI;AACpD,UAAMC,WAAWC,WAA2B,IAAI;AAEhDL,WAAO;MAAE5B,OAAOgC;IAAS,CAAA;AAEzB,UAAME,cAAc,MAAM;AACxB,UAAI,CAACJ,UAAUK,MAAO;AAEtB,YAAM,EAAElE,MAAMD,MAAMI,SAASE,SAASE,aAAY,IAAKa;AACvD,YAAM+C,aAAanB,UAAUjD,MAAMQ,YAAAA;AACnC,YAAM6D,cAAczC,WAAWwC,YAAYpE,IAAAA;AAE3CgE,eAASG,QAAQ,IAAIG,MAAQR,UAAUK,OAAO;QAC5ClE;QACAD,MAAMqE;QACNjE,SAAS;UAAE,GAAGA;QAAQ;QACtBE;MACF,CAAA;IACF;AAEA,UAAMiE,eAAe,MAAM;AACzB,YAAMvC,QAAQL,MAAMqC,SAASG,KAAK;AAElC,UAAInC,OAAO;AACT,YAAIX,MAAML,eAAe,GAAG;AAC1BwD,qBAAW,MAAM;AACfxC,kBAAMyC,QAAO;AACbT,qBAASG,QAAQ;UACnB,GAAG9C,MAAML,YAAY;eAChB;AACLgB,gBAAMyC,QAAO;AACbT,mBAASG,QAAQ;;;IAGvB;AAEA,UAAMO,SAAS,CAAC1C,UAAmB;AACjCA,YAAM0C,OAAOrD,MAAMX,UAAU;IAC/B;AAEAiE,cAAUT,WAAAA;AAEVU,gBAAYL,YAAAA;AAEZM,UACE;MAAC,MAAMxD,MAAMjB;MAAS,MAAMiB,MAAMrB;IAAK,GACvC,CAGK8E,QAAA,WAAA;AAFH,UAAA,CAACC,kBAAkBC,aAAAA,IACnBF,QAAA,CAACG,kBAAkBC,aAAc,IAAA;AAEjC,YAAMlD,QAAQL,MAAMqC,SAASG,KAAK;AAElC,UAAI,CAACnC,OAAO;AACV;;AAGF,UAAImD,eAAe;AAEnB,UAAIJ,kBAAkB;AACpB,cAAM9C,cAAcT,aAAauD,gBAAAA;AACjC,cAAMK,cAAc5D,aAAayD,gBAAAA;AAEjC,YAAIhD,eAAeA,gBAAgBmD,aAAa;AAC9CrD,qBAAWC,OAAOC,WAAAA;AAClBkD,yBAAe;;;AAInB,UAAIH,eAAe;AACjB,cAAM5C,aAAaZ,aAAawD,cAAc3C,MAAM;AACpD,cAAMgD,aAAa7D,aAAa0D,cAAc7C,MAAM;AACpD,cAAME,eAAef,aAAawD,cAAcvC,QAAQ;AACxD,cAAM6C,eAAe9D,aAAa0D,cAAczC,QAAQ;AAExD,YAAIL,eAAeiD,YAAY;AAC7BnD,oBAAUF,MAAMuD,OAAOvF,MAAMoC,UAAAA;AAC7B+C,yBAAe;;AAGjB,YAAI5C,gBAAgBA,iBAAiB+C,cAAc;AACjDhD,sBAAYN,MAAMuD,OAAOvF,MAAMuC,cAAclB,MAAMb,YAAY;AAC/D2E,yBAAe;;;AAInB,UAAIA,cAAc;AAChBK,iBAAS,MAAM;AACbd,iBAAO1C,KAAAA;QACT,CAAA;;OAGJ;MAAEyD,MAAM;IAAK,CAAA;AAGf,WAAO,MAAM;AACX,aAAOC,EACL,UACA;QACEC,MAAM;QACN9E,WAAWQ,MAAMR;QACjBC,iBAAiBO,MAAMP;QACvBiD,KAAKD;SAEP;QAAC4B,EAAE,KAAK,CAAA,GAAI;UAAC7B,MAAMxD,UAAUwD,MAAMxD,QAAO,IAAK;QAAG,CAAA;MAAE,CAAA;IAExD;EACF;AACF,CAAoB;ACnHb,SAASuF,iBAKd3F,MACA4F,eAC2C;AAC3CvB,QAAQwB,SAASD,aAAAA;AAEjB,SAAOnC,gBAAgB;IACrBrC,OAAOtB;IACP4D,MAAMtC,OAAO,OAAY;UAAZ,EAAEuC,OAAAA,IAAF;AACX,YAAMG,OAAME,WAA2B,IAAI;AAC3C,YAAM8B,eAAe,CAAC/B,aAAgC;AACpDD,QAAAA,KAAII,QAAQH,qCAAUhC;MACxB;AAEA4B,aAAO;QAAE5B,OAAO+B;MAAI,CAAA;AAEpB,aAAO,MAAM;AACX,eAAO2B,EACLjC,QACAvC,YACE;UACE6C,KAAKgC;WAEP;UACE9F;UACA,GAAGoB;QACL,CAAA,CAAA;MAGN;IACF;EACF,CAAA;AACF;IAMa2E,MAAsBJ,iBAGjC,OAAOK,aAAc;IAEVC,WAA2BN,iBACtC,YACAO,kBACD;IAEYC,OAAuBR,iBAAiB,QAAQS,cAAe;IAE/DC,MAAsBV,iBAAiB,OAAOW,aAAc;IAE5DC,YAA4BZ,iBACvC,aACAa,mBACD;IAEYC,QAAwBd,iBAAiB,SAASe,eAAgB;IAElEC,SAAyBhB,iBACpC,UACAiB,gBACD;IAEYC,UAA0BlB,iBACrC,WACAmB,iBACD;", "names": ["CommonProps", "data", "type", "Object", "required", "options", "default", "plugins", "Array", "datasetIdKey", "String", "updateMode", "undefined", "A11yProps", "aria<PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "Props", "destroyDelay", "Number", "compatProps", "version", "internals", "props", "assign", "attrs", "toRawIfProxy", "obj", "isProxy", "toRaw", "cloneProxy", "src", "Proxy", "setOptions", "chart", "nextOptions", "<PERSON><PERSON><PERSON><PERSON>", "currentData", "<PERSON><PERSON><PERSON><PERSON>", "labels", "setDatasets", "nextDatasets", "addedDatasets", "datasets", "map", "nextDataset", "currentDataset", "find", "dataset", "includes", "push", "cloneData", "nextData", "getDatasetAtEvent", "event", "getElementsAtEventForMode", "intersect", "getElementAtEvent", "getElementsAtEvent", "Chart", "defineComponent", "setup", "expose", "slots", "canvasRef", "ref", "chartRef", "shallowRef", "<PERSON><PERSON><PERSON>", "value", "clonedData", "proxiedData", "ChartJS", "destroy<PERSON>hart", "setTimeout", "destroy", "update", "onMounted", "onUnmounted", "watch", "param", "nextOptionsProxy", "nextDataProxy", "prevOptionsProxy", "prevDataProxy", "shouldUpdate", "prevOptions", "prev<PERSON><PERSON><PERSON>", "prevDatasets", "config", "nextTick", "deep", "h", "role", "createTypedChart", "registerables", "register", "reforwardRef", "Bar", "BarController", "Doughnut", "DoughnutController", "Line", "LineController", "Pie", "PieController", "PolarArea", "PolarAreaController", "Radar", "RadarController", "Bubble", "BubbleController", "<PERSON><PERSON><PERSON>", "ScatterController"]}