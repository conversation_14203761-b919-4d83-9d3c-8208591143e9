{"version": 3, "file": "index.mjs", "sources": ["../../src/badgedirective/BaseBadgeDirective.js", "../../src/badgedirective/BadgeDirective.js"], "sourcesContent": ["import BaseDirective from '@primevue/core/basedirective';\nimport BadgeDirectiveStyle from 'primevue/badgedirective/style';\n\nconst BaseBadgeDirective = BaseDirective.extend({\n    style: BadgeDirectiveStyle\n});\n\nexport default BaseBadgeDirective;\n", "import { addClass, createElement, hasClass, removeClass } from '@primeuix/utils/dom';\nimport { uuid } from '@primeuix/utils/uuid';\nimport BaseBadgeDirective from './BaseBadgeDirective';\n\nconst BadgeDirective = BaseBadgeDirective.extend('badge', {\n    mounted(el, binding) {\n        console.warn('Deprecated since v4. Use OverlayBadge component instead.');\n        const id = uuid('pv_id') + '_badge';\n\n        const badge = createElement('span', {\n            id,\n            class: !this.isUnstyled() && this.cx('root'),\n            [this.$attrSelector]: '',\n            'p-bind': this.ptm('root', {\n                context: {\n                    ...binding.modifiers,\n                    nogutter: String(binding.value).length === 1,\n                    dot: binding.value == null\n                }\n            })\n        });\n\n        el.$_pbadgeId = badge.getAttribute('id');\n\n        for (let modifier in binding.modifiers) {\n            !this.isUnstyled() && addClass(badge, 'p-badge-' + modifier);\n        }\n\n        if (binding.value != null) {\n            if (typeof binding.value === 'object') el.$_badgeValue = binding.value.value;\n            else el.$_badgeValue = binding.value;\n            badge.appendChild(document.createTextNode(el.$_badgeValue));\n\n            if (String(el.$_badgeValue).length === 1 && !this.isUnstyled()) {\n                !this.isUnstyled() && addClass(badge, 'p-badge-circle');\n            }\n        } else {\n            !this.isUnstyled() && addClass(badge, 'p-badge-dot');\n        }\n\n        el.setAttribute('data-pd-badge', true);\n        !this.isUnstyled() && addClass(el, 'p-overlay-badge');\n        el.setAttribute('data-p-overlay-badge', 'true');\n        el.appendChild(badge);\n\n        this.$el = badge;\n    },\n    updated(el, binding) {\n        !this.isUnstyled() && addClass(el, 'p-overlay-badge');\n        el.setAttribute('data-p-overlay-badge', 'true');\n\n        if (binding.oldValue !== binding.value) {\n            let badge = document.getElementById(el.$_pbadgeId);\n\n            if (typeof binding.value === 'object') el.$_badgeValue = binding.value.value;\n            else el.$_badgeValue = binding.value;\n\n            if (!this.isUnstyled()) {\n                if (el.$_badgeValue) {\n                    if (hasClass(badge, 'p-badge-dot')) removeClass(badge, 'p-badge-dot');\n\n                    if (el.$_badgeValue.length === 1) addClass(badge, 'p-badge-circle');\n                    else removeClass(badge, 'p-badge-circle');\n                } else if (!el.$_badgeValue && !hasClass(badge, 'p-badge-dot')) {\n                    addClass(badge, 'p-badge-dot');\n                }\n            }\n\n            badge.innerHTML = '';\n            badge.appendChild(document.createTextNode(el.$_badgeValue));\n        }\n    }\n});\n\nexport default BadgeDirective;\n"], "names": ["BaseBadgeDirective", "BaseDirective", "extend", "style", "BadgeDirectiveStyle", "BadgeDirective", "mounted", "el", "binding", "console", "warn", "id", "uuid", "badge", "createElement", "_defineProperty", "isUnstyled", "cx", "$attrSelector", "ptm", "context", "_objectSpread", "modifiers", "nogutter", "String", "value", "length", "dot", "$_pbadgeId", "getAttribute", "modifier", "addClass", "_typeof", "$_badgeValue", "append<PERSON><PERSON><PERSON>", "document", "createTextNode", "setAttribute", "$el", "updated", "oldValue", "getElementById", "hasClass", "removeClass", "innerHTML"], "mappings": ";;;;;AAGA,IAAMA,kBAAkB,GAAGC,aAAa,CAACC,MAAM,CAAC;AAC5CC,EAAAA,KAAK,EAAEC;AACX,CAAC,CAAC;;;;;;;;ACDF,IAAMC,cAAc,GAAGL,kBAAkB,CAACE,MAAM,CAAC,OAAO,EAAE;AACtDI,EAAAA,OAAO,WAAPA,OAAOA,CAACC,EAAE,EAAEC,OAAO,EAAE;AACjBC,IAAAA,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;AACxE,IAAA,IAAMC,EAAE,GAAGC,IAAI,CAAC,OAAO,CAAC,GAAG,QAAQ;IAEnC,IAAMC,KAAK,GAAGC,aAAa,CAAC,MAAM,EAAAC,eAAA,CAAAA,eAAA,CAAA;AAC9BJ,MAAAA,EAAE,EAAFA,EAAE;MACF,OAAO,EAAA,CAAC,IAAI,CAACK,UAAU,EAAE,IAAI,IAAI,CAACC,EAAE,CAAC,MAAM;AAAC,KAAA,EAC3C,IAAI,CAACC,aAAa,EAAG,EAAE,CAAA,EACxB,QAAQ,EAAE,IAAI,CAACC,GAAG,CAAC,MAAM,EAAE;AACvBC,MAAAA,OAAO,EAAAC,aAAA,CAAAA,aAAA,CACAb,EAAAA,EAAAA,OAAO,CAACc,SAAS,CAAA,EAAA,EAAA,EAAA;QACpBC,QAAQ,EAAEC,MAAM,CAAChB,OAAO,CAACiB,KAAK,CAAC,CAACC,MAAM,KAAK,CAAC;AAC5CC,QAAAA,GAAG,EAAEnB,OAAO,CAACiB,KAAK,IAAI;AAAI,OAAA;KAEjC,CAAC,CACL,CAAC;IAEFlB,EAAE,CAACqB,UAAU,GAAGf,KAAK,CAACgB,YAAY,CAAC,IAAI,CAAC;AAExC,IAAA,KAAK,IAAIC,QAAQ,IAAItB,OAAO,CAACc,SAAS,EAAE;AACpC,MAAA,CAAC,IAAI,CAACN,UAAU,EAAE,IAAIe,QAAQ,CAAClB,KAAK,EAAE,UAAU,GAAGiB,QAAQ,CAAC;AAChE;AAEA,IAAA,IAAItB,OAAO,CAACiB,KAAK,IAAI,IAAI,EAAE;MACvB,IAAIO,OAAA,CAAOxB,OAAO,CAACiB,KAAK,MAAK,QAAQ,EAAElB,EAAE,CAAC0B,YAAY,GAAGzB,OAAO,CAACiB,KAAK,CAACA,KAAK,CAAC,KACxElB,EAAE,CAAC0B,YAAY,GAAGzB,OAAO,CAACiB,KAAK;MACpCZ,KAAK,CAACqB,WAAW,CAACC,QAAQ,CAACC,cAAc,CAAC7B,EAAE,CAAC0B,YAAY,CAAC,CAAC;AAE3D,MAAA,IAAIT,MAAM,CAACjB,EAAE,CAAC0B,YAAY,CAAC,CAACP,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAACV,UAAU,EAAE,EAAE;QAC5D,CAAC,IAAI,CAACA,UAAU,EAAE,IAAIe,QAAQ,CAAClB,KAAK,EAAE,gBAAgB,CAAC;AAC3D;AACJ,KAAC,MAAM;MACH,CAAC,IAAI,CAACG,UAAU,EAAE,IAAIe,QAAQ,CAAClB,KAAK,EAAE,aAAa,CAAC;AACxD;AAEAN,IAAAA,EAAE,CAAC8B,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC;IACtC,CAAC,IAAI,CAACrB,UAAU,EAAE,IAAIe,QAAQ,CAACxB,EAAE,EAAE,iBAAiB,CAAC;AACrDA,IAAAA,EAAE,CAAC8B,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;AAC/C9B,IAAAA,EAAE,CAAC2B,WAAW,CAACrB,KAAK,CAAC;IAErB,IAAI,CAACyB,GAAG,GAAGzB,KAAK;GACnB;AACD0B,EAAAA,OAAO,WAAPA,OAAOA,CAAChC,EAAE,EAAEC,OAAO,EAAE;IACjB,CAAC,IAAI,CAACQ,UAAU,EAAE,IAAIe,QAAQ,CAACxB,EAAE,EAAE,iBAAiB,CAAC;AACrDA,IAAAA,EAAE,CAAC8B,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;AAE/C,IAAA,IAAI7B,OAAO,CAACgC,QAAQ,KAAKhC,OAAO,CAACiB,KAAK,EAAE;MACpC,IAAIZ,KAAK,GAAGsB,QAAQ,CAACM,cAAc,CAAClC,EAAE,CAACqB,UAAU,CAAC;MAElD,IAAII,OAAA,CAAOxB,OAAO,CAACiB,KAAK,MAAK,QAAQ,EAAElB,EAAE,CAAC0B,YAAY,GAAGzB,OAAO,CAACiB,KAAK,CAACA,KAAK,CAAC,KACxElB,EAAE,CAAC0B,YAAY,GAAGzB,OAAO,CAACiB,KAAK;AAEpC,MAAA,IAAI,CAAC,IAAI,CAACT,UAAU,EAAE,EAAE;QACpB,IAAIT,EAAE,CAAC0B,YAAY,EAAE;AACjB,UAAA,IAAIS,QAAQ,CAAC7B,KAAK,EAAE,aAAa,CAAC,EAAE8B,WAAW,CAAC9B,KAAK,EAAE,aAAa,CAAC;UAErE,IAAIN,EAAE,CAAC0B,YAAY,CAACP,MAAM,KAAK,CAAC,EAAEK,QAAQ,CAAClB,KAAK,EAAE,gBAAgB,CAAC,CAAC,KAC/D8B,WAAW,CAAC9B,KAAK,EAAE,gBAAgB,CAAC;AAC7C,SAAC,MAAM,IAAI,CAACN,EAAE,CAAC0B,YAAY,IAAI,CAACS,QAAQ,CAAC7B,KAAK,EAAE,aAAa,CAAC,EAAE;AAC5DkB,UAAAA,QAAQ,CAAClB,KAAK,EAAE,aAAa,CAAC;AAClC;AACJ;MAEAA,KAAK,CAAC+B,SAAS,GAAG,EAAE;MACpB/B,KAAK,CAACqB,WAAW,CAACC,QAAQ,CAACC,cAAc,CAAC7B,EAAE,CAAC0B,YAAY,CAAC,CAAC;AAC/D;AACJ;AACJ,CAAC;;;;"}