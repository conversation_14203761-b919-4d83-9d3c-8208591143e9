{"version": 3, "file": "index.mjs", "sources": ["../../../src/avatar/style/AvatarStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/avatar';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ props }) => [\n        'p-avatar p-component',\n        {\n            'p-avatar-image': props.image != null,\n            'p-avatar-circle': props.shape === 'circle',\n            'p-avatar-lg': props.size === 'large',\n            'p-avatar-xl': props.size === 'xlarge'\n        }\n    ],\n    label: 'p-avatar-label',\n    icon: 'p-avatar-icon'\n};\n\nexport default BaseStyle.extend({\n    name: 'avatar',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "props", "image", "shape", "size", "label", "icon", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAA,OAAO,CACjB,sBAAsB,EACtB;AACI,MAAA,gBAAgB,EAAEA,KAAK,CAACC,KAAK,IAAI,IAAI;AACrC,MAAA,iBAAiB,EAAED,KAAK,CAACE,KAAK,KAAK,QAAQ;AAC3C,MAAA,aAAa,EAAEF,KAAK,CAACG,IAAI,KAAK,OAAO;AACrC,MAAA,aAAa,EAAEH,KAAK,CAACG,IAAI,KAAK;AAClC,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,KAAK,EAAE,gBAAgB;AACvBC,EAAAA,IAAI,EAAE;AACV,CAAC;AAED,kBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,QAAQ;AACdC,EAAAA,KAAK,EAALA,KAAK;AACLZ,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}