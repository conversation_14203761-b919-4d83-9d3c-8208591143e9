{"version": 3, "sources": ["../../src/avatar/style/AvatarStyle.js", "../../src/avatar/BaseAvatar.vue", "../../src/avatar/Avatar.vue", "../../src/avatar/Avatar.vue"], "sourcesContent": ["import { style } from '@primeuix/styles/avatar';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ props }) => [\n        'p-avatar p-component',\n        {\n            'p-avatar-image': props.image != null,\n            'p-avatar-circle': props.shape === 'circle',\n            'p-avatar-lg': props.size === 'large',\n            'p-avatar-xl': props.size === 'xlarge'\n        }\n    ],\n    label: 'p-avatar-label',\n    icon: 'p-avatar-icon'\n};\n\nexport default BaseStyle.extend({\n    name: 'avatar',\n    style,\n    classes\n});\n", "<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport AvatarStyle from 'primevue/avatar/style';\n\nexport default {\n    name: 'BaseAvatar',\n    extends: BaseComponent,\n    props: {\n        label: {\n            type: String,\n            default: null\n        },\n        icon: {\n            type: String,\n            default: null\n        },\n        image: {\n            type: String,\n            default: null\n        },\n        size: {\n            type: String,\n            default: 'normal'\n        },\n        shape: {\n            type: String,\n            default: 'square'\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        }\n    },\n    style: AvatarStyle,\n    provide() {\n        return {\n            $pcAvatar: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :aria-labelledby=\"ariaLabelledby\" :aria-label=\"ariaLabel\" v-bind=\"ptmi('root')\" :data-p=\"dataP\">\n        <slot>\n            <span v-if=\"label\" :class=\"cx('label')\" v-bind=\"ptm('label')\" :data-p=\"dataP\">{{ label }}</span>\n            <component v-else-if=\"$slots.icon\" :is=\"$slots.icon\" :class=\"cx('icon')\" />\n            <span v-else-if=\"icon\" :class=\"[cx('icon'), icon]\" v-bind=\"ptm('icon')\" :data-p=\"dataP\" />\n            <img v-else-if=\"image\" :src=\"image\" :alt=\"ariaLabel\" @error=\"onError\" v-bind=\"ptm('image')\" :data-p=\"dataP\" />\n        </slot>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport BaseAvatar from './BaseAvatar.vue';\n\nexport default {\n    name: 'Avatar',\n    extends: BaseAvatar,\n    inheritAttrs: false,\n    emits: ['error'],\n    methods: {\n        onError(event) {\n            this.$emit('error', event);\n        }\n    },\n    computed: {\n        dataP() {\n            return cn({\n                [this.shape]: this.shape,\n                [this.size]: this.size\n            });\n        }\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :aria-labelledby=\"ariaLabelledby\" :aria-label=\"ariaLabel\" v-bind=\"ptmi('root')\" :data-p=\"dataP\">\n        <slot>\n            <span v-if=\"label\" :class=\"cx('label')\" v-bind=\"ptm('label')\" :data-p=\"dataP\">{{ label }}</span>\n            <component v-else-if=\"$slots.icon\" :is=\"$slots.icon\" :class=\"cx('icon')\" />\n            <span v-else-if=\"icon\" :class=\"[cx('icon'), icon]\" v-bind=\"ptm('icon')\" :data-p=\"dataP\" />\n            <img v-else-if=\"image\" :src=\"image\" :alt=\"ariaLabel\" @error=\"onError\" v-bind=\"ptm('image')\" :data-p=\"dataP\" />\n        </slot>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport BaseAvatar from './BaseAvatar.vue';\n\nexport default {\n    name: 'Avatar',\n    extends: BaseAvatar,\n    inheritAttrs: false,\n    emits: ['error'],\n    methods: {\n        onError(event) {\n            this.$emit('error', event);\n        }\n    },\n    computed: {\n        dataP() {\n            return cn({\n                [this.shape]: this.shape,\n                [this.size]: this.size\n            });\n        }\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAMA,UAAU;EACZC,MAAM,SAANA,KAAIC,MAAA;AAAA,QAAKC,QAAKD,KAALC;AAAK,WAAO,CACjB,wBACA;MACI,kBAAkBA,MAAMC,SAAS;MACjC,mBAAmBD,MAAME,UAAU;MACnC,eAAeF,MAAMG,SAAS;MAC9B,eAAeH,MAAMG,SAAS;IAClC,CAAC;EACJ;EACDC,OAAO;EACPC,MAAM;AACV;AAEA,IAAA,cAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNC;EACAZ;AACJ,CAAC;;;ACjBD,IAAA,WAAe;EACXa,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,OAAO;MACHC,MAAMC;MACN,WAAS;;IAEbC,MAAM;MACFF,MAAMC;MACN,WAAS;;IAEbE,OAAO;MACHH,MAAMC;MACN,WAAS;;IAEbG,MAAM;MACFJ,MAAMC;MACN,WAAS;;IAEbI,OAAO;MACHL,MAAMC;MACN,WAAS;;IAEbK,gBAAgB;MACZN,MAAMC;MACN,WAAS;;IAEbM,WAAW;MACPP,MAAMC;MACN,WAAS;IACb;;EAEJO,OAAOC;EACPC,SAAO,SAAPA,UAAU;AACN,WAAO;MACHC,WAAW;MACXC,iBAAiB;;EAEzB;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;AC7BA,IAAAC,UAAe;EACXjB,MAAM;EACN,WAASkB;EACTC,cAAc;EACdC,OAAO,CAAC,OAAO;EACfC,SAAS;IACLC,SAAAA,SAAAA,QAAQC,OAAO;AACX,WAAKC,MAAM,SAASD,KAAK;IAC7B;;EAEJE,UAAU;IACNC,OAAK,SAALA,QAAQ;AACJ,aAAOC,GAAEC,gBAAAA,gBACJ,CAAA,GAAA,KAAKnB,OAAQ,KAAKA,KAAK,GACvB,KAAKD,MAAO,KAAKA,IAAG,CACxB;IACL;EACJ;AACJ;;;;;;AChCI,SAAAqB,UAAA,GAAAC,mBAOK,OAPLC,WAOK;IAPC,SAAOC,KAAEC,GAAA,MAAA;IAAW,mBAAiBD,KAActB;IAAG,cAAYsB,KAASrB;KAAUqB,KAAIE,KAAA,MAAA,GAAA;IAAW,UAAQC,SAAKT;EAAA,CAAA,GAAA,CACnHU,WAKMJ,KAAAA,QAAAA,WAAAA,CAAAA,GALN,WAAA;AAAA,WAKM,CAJUA,KAAK7B,SAAjB0B,UAAA,GAAAC,mBAA+F,QAA/FC,WAA+F;;MAA3E,SAAOC,KAAEC,GAAA,OAAA;IAAmB,GAAAD,KAAAK,IAAe,OAAA,GAAA;MAAA,UAAQF,SAAAT;wBAAUM,KAAI7B,KAAA,GAAA,IAAAmC,UAAA,KAC/DN,KAAAO,OAAOjC,QAAI,UAAA,GAAjCkC,YAA0EC,wBAAlCT,KAAMO,OAACjC,IAAI,GAAA;;MAAG,SAAA,eAAO0B,KAAEC,GAAA,MAAA,CAAA;8BAC9CD,KAAI1B,QAArBuB,UAAA,GAAAC,mBAAyF,QAAzFC,WAAyF;;MAAjE,SAAK,CAAGC,KAAEC,GAAA,MAAA,GAAUD,KAAI1B,IAAA;OAAW0B,KAAGK,IAAA,MAAA,GAAA;MAAW,UAAQF,SAAKT;KAAA,GAAA,MAAA,IAAAgB,UAAA,KACtEV,KAAKzB,SAArBsB,UAAA,GAAAC,mBAA6G,OAA7GC,WAA6G;;MAArFY,KAAKX,KAAKzB;MAAGqC,KAAKZ,KAASrB;MAAGW,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;eAAEa,SAAOb,WAAAa,SAAAb,QAAAuB,MAAAV,UAAAW,SAAA;;OAAUd,KAAGK,IAAA,OAAA,GAAA;MAAY,UAAQF,SAAKT;IAAA,CAAA,GAAA,MAAA,IAAAqB,UAAA,KAAA,mBAAA,IAAA,IAAA,CAAA;;;;", "names": ["classes", "root", "_ref", "props", "image", "shape", "size", "label", "icon", "BaseStyle", "extend", "name", "style", "name", "BaseComponent", "props", "label", "type", "String", "icon", "image", "size", "shape", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "style", "AvatarStyle", "provide", "$pcAvatar", "$parentInstance", "script", "BaseAvatar", "inheritAttrs", "emits", "methods", "onError", "event", "$emit", "computed", "dataP", "cn", "_defineProperty", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "ptmi", "$options", "_renderSlot", "ptm", "_hoisted_2", "$slots", "_createBlock", "_resolveDynamicComponent", "_hoisted_3", "src", "alt", "apply", "arguments", "_hoisted_4"]}