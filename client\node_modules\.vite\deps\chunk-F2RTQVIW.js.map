{"version": 3, "sources": ["../../@primevue/src/utils/ConnectedOverlayScrollHandler.js", "../../@primevue/src/utils/HelperSet.js", "../../@primevue/src/utils/UniqueComponentId.js", "../../@primevue/src/utils/Utils.js"], "sourcesContent": ["import { getScrollableParents } from '@primeuix/utils/dom';\n\nexport default class ConnectedOverlayScrollHandler {\n    constructor(element, listener = () => {}) {\n        this.element = element;\n        this.listener = listener;\n    }\n\n    bindScrollListener() {\n        this.scrollableParents = getScrollableParents(this.element);\n\n        for (let i = 0; i < this.scrollableParents.length; i++) {\n            this.scrollableParents[i].addEventListener('scroll', this.listener);\n        }\n    }\n\n    unbindScrollListener() {\n        if (this.scrollableParents) {\n            for (let i = 0; i < this.scrollableParents.length; i++) {\n                this.scrollableParents[i].removeEventListener('scroll', this.listener);\n            }\n        }\n    }\n\n    destroy() {\n        this.unbindScrollListener();\n        this.element = null;\n        this.listener = null;\n        this.scrollableParents = null;\n    }\n}\n", "import { isNotEmpty } from '@primeuix/utils/object';\n\nexport default class {\n    helpers;\n    type;\n    constructor({ init, type }) {\n        this.helpers = new Set(init);\n        this.type = type;\n    }\n    add(instance) {\n        this.helpers.add(instance);\n    }\n    update() {\n        // @todo\n    }\n    delete(instance) {\n        this.helpers.delete(instance);\n    }\n    clear() {\n        this.helpers.clear();\n    }\n    get(parentInstance, slots) {\n        const children = this._get(parentInstance, slots);\n        const computed = children ? this._recursive([...this.helpers], children) : null;\n\n        return isNotEmpty(computed) ? computed : null;\n    }\n    _isMatched(instance, key) {\n        const parent = instance?.parent;\n\n        return parent?.vnode?.key === key || (parent && this._isMatched(parent, key)) || false;\n    }\n    _get(parentInstance, slots) {\n        return (slots || parentInstance?.$slots)?.default?.() || null;\n    }\n    _recursive(helpers = [], children = []) {\n        let components = [];\n\n        children.forEach((child) => {\n            if (child.children instanceof Array) {\n                components = components.concat(this._recursive(components, child.children));\n            } else if (child.type.name === this.type) {\n                components.push(child);\n            } else if (isNotEmpty(child.key)) {\n                components = components.concat(helpers.filter((c) => this._isMatched(c, child.key)).map((c) => c.vnode));\n            }\n        });\n\n        return components;\n    }\n}\n", "import { uuid } from '@primeuix/utils/uuid';\n\n/**\n * @deprecated since v4.3.0. Use `uuid` from @primeuix/utils instead.\n * @param {string} prefix\n * @return {string}\n */\nexport default function (prefix = 'pv_id_') {\n    return uuid(prefix);\n}\n", "export * from '@primeuix/utils';\nexport { default as ConnectedOverlayScrollHandler } from './ConnectedOverlayScrollHandler';\nexport { default as HelperSet } from './HelperSet';\nexport { default as UniqueComponentId } from './UniqueComponentId';\n\nexport function getVNodeProp(vnode, prop) {\n    if (vnode) {\n        let props = vnode.props;\n\n        if (props) {\n            let kebabProp = prop.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n            let propName = Object.prototype.hasOwnProperty.call(props, kebabProp) ? kebabProp : prop;\n\n            return vnode.type.extends.props[prop].type === Boolean && props[propName] === '' ? true : props[propName];\n        }\n    }\n\n    return null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA2D,IAEtCA,gCAA6B,WAAA;AAC9C,WAAAA,+BAAYC,SAA8B;AAAA,QAArBC,WAAQC,UAAAC,SAAAD,KAAAA,UAAAE,CAAAA,MAAAA,SAAAF,UAAG,CAAA,IAAA,WAAM;IAAA;AAAEG,sBAAA,MAAAN,8BAAA;AACpC,SAAKC,UAAUA;AACf,SAAKC,WAAWA;EACpB;AAAC,SAAAK,eAAAP,gCAAA,CAAA;IAAAQ,KAAA;IAAAC,OAED,SAAAC,qBAAqB;AACjB,WAAKC,oBAAoBC,qBAAqB,KAAKX,OAAO;AAE1D,eAASY,IAAI,GAAGA,IAAI,KAAKF,kBAAkBP,QAAQS,KAAK;AACpD,aAAKF,kBAAkBE,CAAC,EAAEC,iBAAiB,UAAU,KAAKZ,QAAQ;MACtE;IACJ;EAAC,GAAA;IAAAM,KAAA;IAAAC,OAED,SAAAM,uBAAuB;AACnB,UAAI,KAAKJ,mBAAmB;AACxB,iBAASE,IAAI,GAAGA,IAAI,KAAKF,kBAAkBP,QAAQS,KAAK;AACpD,eAAKF,kBAAkBE,CAAC,EAAEG,oBAAoB,UAAU,KAAKd,QAAQ;QACzE;MACJ;IACJ;EAAC,GAAA;IAAAM,KAAA;IAAAC,OAED,SAAAQ,UAAU;AACN,WAAKF,qBAAoB;AACzB,WAAKd,UAAU;AACf,WAAKC,WAAW;AAChB,WAAKS,oBAAoB;IAC7B;EAAC,CAAA,CAAA;AAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7B+C,IAAAO,WAAA,WAAA;AAKhD,WAAAA,UAAAC,MAA4B;AAAA,QAAdC,OAAID,KAAJC,MAAMC,OAAIF,KAAJE;AAAIf,oBAAA,MAAAY,SAAA;AAAAI,oBAAA,MAAA,WAAA,MAAA;AAAAA,oBAAA,MAAA,QAAA,MAAA;AACpB,SAAKC,UAAU,IAAIC,IAAIJ,IAAI;AAC3B,SAAKC,OAAOA;EAChB;AAAC,SAAAd,aAAAW,WAAA,CAAA;IAAAV,KAAA;IAAAC,OACD,SAAAgB,IAAIC,UAAU;AACV,WAAKH,QAAQE,IAAIC,QAAQ;IAC7B;EAAC,GAAA;IAAAlB,KAAA;IAAAC,OACD,SAAAkB,SAAS;IACL;EACH,GAAA;IAAAnB,KAAA;IAAAC,OACD,SAAAmB,QAAOF,UAAU;AACb,WAAKH,QAAc,QAAA,EAACG,QAAQ;IAChC;EAAC,GAAA;IAAAlB,KAAA;IAAAC,OACD,SAAAoB,QAAQ;AACJ,WAAKN,QAAQM,MAAK;IACtB;EAAC,GAAA;IAAArB,KAAA;IAAAC,OACD,SAAAqB,IAAIC,gBAAgBC,OAAO;AACvB,UAAMC,WAAW,KAAKC,KAAKH,gBAAgBC,KAAK;AAChD,UAAMG,WAAWF,WAAW,KAAKG,WAAUC,mBAAK,KAAKd,OAAO,GAAGU,QAAQ,IAAI;AAE3E,aAAOK,WAAWH,QAAQ,IAAIA,WAAW;IAC7C;EAAC,GAAA;IAAA3B,KAAA;IAAAC,OACD,SAAA8B,WAAWb,UAAUlB,KAAK;AAAA,UAAAgC;AACtB,UAAMC,SAASf,aAAQ,QAARA,aAAAA,SAAAA,SAAAA,SAAUe;AAEzB,cAAOA,WAAAA,QAAAA,WAAMD,WAAAA,gBAANC,OAAQC,WAAKF,QAAAA,kBAAA,SAAA,SAAbA,cAAehC,SAAQA,OAAQiC,UAAU,KAAKF,WAAWE,QAAQjC,GAAG,KAAM;IACrF;EAAC,GAAA;IAAAA,KAAA;IAAAC,OACD,SAAAyB,KAAKH,gBAAgBC,OAAO;AAAA,UAAAW,OAAAC;AACxB,eAAOD,QAACX,UAASD,mBAAAA,QAAAA,mBAAAA,SAAAA,SAAAA,eAAgBc,aAAMF,QAAAA,UAAAC,WAAAA,gBAAhCD,MAA0C,SAAA,OAAA,QAAAC,kBAAA,SAAA,SAA1CA,cAAAE,KAAAH,KAA6C,MAAK;IAC7D;EAAC,GAAA;IAAAnC,KAAA;IAAAC,OACD,SAAA2B,aAAwC;AAAA,UAAAW,QAAA;AAAA,UAA7BxB,UAAOpB,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAA;AAAE,UAAE8B,WAAQ9B,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAA;AAChC,UAAI6C,aAAa,CAAA;AAEjBf,eAASgB,QAAQ,SAACC,OAAU;AACxB,YAAIA,MAAMjB,oBAAoBkB,OAAO;AACjCH,uBAAaA,WAAWI,OAAOL,MAAKX,WAAWY,YAAYE,MAAMjB,QAAQ,CAAC;mBACnEiB,MAAM7B,KAAKgC,SAASN,MAAK1B,MAAM;AACtC2B,qBAAWM,KAAKJ,KAAK;mBACdZ,WAAWY,MAAM1C,GAAG,GAAG;AAC9BwC,uBAAaA,WAAWI,OAAO7B,QAAQgC,OAAO,SAACC,GAAC;AAAA,mBAAKT,MAAKR,WAAWiB,GAAGN,MAAM1C,GAAG;UAAC,CAAA,EAAEiD,IAAI,SAACD,GAAC;AAAA,mBAAKA,EAAEd;UAAK,CAAA,CAAC;QAC3G;MACJ,CAAC;AAED,aAAOM;IACX;EAAC,CAAA,CAAA;AAAA,EAAA;AE5CE,SAASU,aAAaC,OAAOC,MAAM;AACtC,MAAID,OAAO;AACP,QAAIE,QAAQF,MAAME;AAElB,QAAIA,OAAO;AACP,UAAIC,YAAYF,KAAKG,QAAQ,mBAAmB,OAAO,EAAEC,YAAW;AACpE,UAAIC,WAAWC,OAAOC,UAAUC,eAAeC,KAAKR,OAAOC,SAAS,IAAIA,YAAYF;AAEpF,aAAOD,MAAMW,KAAY,SAAA,EAACT,MAAMD,IAAI,EAAEU,SAASC,WAAWV,MAAMI,QAAQ,MAAM,KAAK,OAAOJ,MAAMI,QAAQ;IAC5G;EACJ;AAEA,SAAO;AACX;", "names": ["ConnectedOverlayScrollHandler", "element", "listener", "arguments", "length", "undefined", "_classCallCheck", "_createClass", "key", "value", "bindScrollListener", "scrollableParents", "getScrollableParents", "i", "addEventListener", "unbindScrollListener", "removeEventListener", "destroy", "_default", "_ref", "init", "type", "_defineProperty", "helpers", "Set", "add", "instance", "update", "delete", "clear", "get", "parentInstance", "slots", "children", "_get", "computed", "_recursive", "_toConsumableArray", "isNotEmpty", "_isMatched", "_parent$vnode", "parent", "vnode", "_ref2", "_ref2$default", "$slots", "call", "_this", "components", "for<PERSON>ach", "child", "Array", "concat", "name", "push", "filter", "c", "map", "getVNodeProp", "vnode", "prop", "props", "kebabProp", "replace", "toLowerCase", "propName", "Object", "prototype", "hasOwnProperty", "call", "type", "Boolean"]}