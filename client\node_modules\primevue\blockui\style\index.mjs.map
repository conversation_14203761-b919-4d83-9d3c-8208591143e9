{"version": 3, "file": "index.mjs", "sources": ["../../../src/blockui/style/BlockUIStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/blockui';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-blockui'\n};\n\nexport default BaseStyle.extend({\n    name: 'blockui',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE;AACV,CAAC;AAED,mBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,SAAS;AACfC,EAAAA,KAAK,EAALA,KAAK;AACLL,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}