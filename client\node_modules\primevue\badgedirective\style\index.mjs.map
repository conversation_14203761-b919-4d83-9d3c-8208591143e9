{"version": 3, "file": "index.mjs", "sources": ["../../../src/badgedirective/style/BadgeDirectiveStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-badge p-component'\n};\n\nexport default BaseStyle.extend({\n    name: 'badge-directive',\n    classes\n});\n"], "names": ["classes", "root", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE;AACV,CAAC;AAED,0BAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,iBAAiB;AACvBJ,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}