{"version": 3, "sources": ["../../@primevue/src/baseicon/style/BaseIconStyle.js", "../../@primevue/src/baseicon/BaseIcon.vue"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst css = `\n.p-icon {\n    display: inline-block;\n    vertical-align: baseline;\n}\n\n.p-icon-spin {\n    -webkit-animation: p-icon-spin 2s infinite linear;\n    animation: p-icon-spin 2s infinite linear;\n}\n\n@-webkit-keyframes p-icon-spin {\n    0% {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n    100% {\n        -webkit-transform: rotate(359deg);\n        transform: rotate(359deg);\n    }\n}\n\n@keyframes p-icon-spin {\n    0% {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n    100% {\n        -webkit-transform: rotate(359deg);\n        transform: rotate(359deg);\n    }\n}\n`;\n\nexport default BaseStyle.extend({\n    name: 'baseicon',\n    css\n});\n", "<script>\nimport { isEmpty } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport BaseIconStyle from '@primevue/icons/baseicon/style';\n\nexport default {\n    name: 'BaseIcon',\n    extends: BaseComponent,\n    props: {\n        label: {\n            type: String,\n            default: undefined\n        },\n        spin: {\n            type: Boolean,\n            default: false\n        }\n    },\n    style: BaseIconStyle,\n    provide() {\n        return {\n            $pcIcon: this,\n            $parentInstance: this\n        };\n    },\n    methods: {\n        pti() {\n            const isLabelEmpty = isEmpty(this.label);\n\n            return {\n                ...(!this.isUnstyled && {\n                    class: [\n                        'p-icon',\n                        {\n                            'p-icon-spin': this.spin\n                        }\n                    ]\n                }),\n                role: !isLabelEmpty ? 'img' : undefined,\n                'aria-label': !isLabelEmpty ? this.label : undefined,\n                'aria-hidden': isLabelEmpty\n            };\n        }\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;AAEA,IAAMA,MAgCL;AAED,IAAA,gBAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNH;AACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClCD,IAAAI,UAAe;EACXC,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,OAAO;MACHC,MAAMC;MACN,WAASC;;IAEbC,MAAM;MACFH,MAAMI;MACN,WAAS;IACb;;EAEJC,OAAOC;EACPC,SAAO,SAAPA,UAAU;AACN,WAAO;MACHC,SAAS;MACTC,iBAAiB;;;EAGzBC,SAAS;IACLC,KAAG,SAAHA,MAAM;AACF,UAAMC,eAAeC,QAAQ,KAAKd,KAAK;AAEvC,aAAAe,cAAAA,cAAA,CAAA,GACQ,CAAC,KAAKC,cAAc;QACpB,SAAO,CACH,UACA;UACI,eAAe,KAAKZ;SACxB;OAEP,GAAA,CAAA,GAAA;QACDa,MAAM,CAACJ,eAAe,QAAQV;QAC9B,cAAc,CAACU,eAAe,KAAKb,QAAQG;QAC3C,eAAeU;MAAW,CAAA;IAElC;EACJ;AACJ;", "names": ["css", "BaseStyle", "extend", "name", "script", "name", "BaseComponent", "props", "label", "type", "String", "undefined", "spin", "Boolean", "style", "BaseIconStyle", "provide", "$pcIcon", "$parentInstance", "methods", "pti", "isLabelEmpty", "isEmpty", "_objectSpread", "isUnstyled", "role"]}