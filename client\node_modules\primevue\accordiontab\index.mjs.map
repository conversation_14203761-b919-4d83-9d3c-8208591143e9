{"version": 3, "file": "index.mjs", "sources": ["../../src/accordiontab/BaseAccordionTab.vue", "../../src/accordiontab/AccordionTab.vue", "../../src/accordiontab/AccordionTab.vue?vue&type=template&id=5ee2e540&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport AccordionTabStyle from 'primevue/accordiontab/style';\n\nexport default {\n    name: 'BaseAccordionTab',\n    extends: BaseComponent,\n    props: {\n        header: null,\n        headerStyle: null,\n        headerClass: null,\n        headerProps: null,\n        headerActionProps: null,\n        contentStyle: null,\n        contentClass: null,\n        contentProps: null,\n        disabled: Boolean\n    },\n    style: AccordionTabStyle,\n    provide() {\n        return {\n            $pcAccordionTab: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <slot></slot>\n</template>\n\n<script>\nimport BaseAccordionTab from './BaseAccordionTab.vue';\n\nexport default {\n    name: 'AccordionTab',\n    extends: BaseAccordionTab,\n    inheritAttrs: false,\n    mounted() {\n        console.warn('Deprecated since v4. Use the new structure of Accordion instead.');\n    }\n};\n</script>\n", "<template>\n    <slot></slot>\n</template>\n\n<script>\nimport BaseAccordionTab from './BaseAccordionTab.vue';\n\nexport default {\n    name: 'AccordionTab',\n    extends: BaseAccordionTab,\n    inheritAttrs: false,\n    mounted() {\n        console.warn('Deprecated since v4. Use the new structure of Accordion instead.');\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "header", "headerStyle", "headerClass", "headerProps", "headerActionProps", "contentStyle", "contentClass", "contentProps", "disabled", "Boolean", "style", "AccordionTabStyle", "provide", "$pcAccordionTab", "$parentInstance", "BaseAccordionTab", "inheritAttrs", "mounted", "console", "warn", "_renderSlot", "_ctx", "$slots"], "mappings": ";;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,kBAAkB;AACxB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,MAAM,EAAE,IAAI;AACZC,IAAAA,WAAW,EAAE,IAAI;AACjBC,IAAAA,WAAW,EAAE,IAAI;AACjBC,IAAAA,WAAW,EAAE,IAAI;AACjBC,IAAAA,iBAAiB,EAAE,IAAI;AACvBC,IAAAA,YAAY,EAAE,IAAI;AAClBC,IAAAA,YAAY,EAAE,IAAI;AAClBC,IAAAA,YAAY,EAAE,IAAI;AAClBC,IAAAA,QAAQ,EAAEC;GACb;AACDC,EAAAA,KAAK,EAAEC,iBAAiB;EACxBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,eAAe,EAAE,IAAI;AACrBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;AClBD,aAAe;AACXjB,EAAAA,IAAI,EAAE,cAAc;AACpB,EAAA,SAAA,EAASkB,QAAgB;AACzBC,EAAAA,YAAY,EAAE,KAAK;EACnBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACNC,IAAAA,OAAO,CAACC,IAAI,CAAC,kEAAkE,CAAC;AACpF;AACJ,CAAC;;;SCbGC,UAAY,CAAAC,IAAA,CAAAC,MAAA,EAAA,SAAA,CAAA;;;;;;;"}