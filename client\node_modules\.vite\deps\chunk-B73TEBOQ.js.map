{"version": 3, "sources": ["../../@primevue/src/base/Base.js"], "sourcesContent": ["export default {\n    _loadedStyleNames: new Set(),\n    getLoadedStyleNames() {\n        return this._loadedStyleNames;\n    },\n    isStyleNameLoaded(name) {\n        return this._loadedStyleNames.has(name);\n    },\n    setLoadedStyleName(name) {\n        this._loadedStyleNames.add(name);\n    },\n    deleteLoadedStyleName(name) {\n        this._loadedStyleNames.delete(name);\n    },\n    clearLoadedStyleNames() {\n        this._loadedStyleNames.clear();\n    }\n};\n"], "mappings": ";AAAA,IAAA,OAAe;EACXA,mBAAmB,oBAAIC,IAAG;EAC1BC,qBAAmB,SAAnBA,sBAAsB;AAClB,WAAO,KAAKF;;EAEhBG,mBAAAA,SAAAA,kBAAkBC,MAAM;AACpB,WAAO,KAAKJ,kBAAkBK,IAAID,IAAI;;EAE1CE,oBAAAA,SAAAA,mBAAmBF,MAAM;AACrB,SAAKJ,kBAAkBO,IAAIH,IAAI;;EAEnCI,uBAAAA,SAAAA,sBAAsBJ,MAAM;AACxB,SAAKJ,kBAAwB,QAAA,EAACI,IAAI;;EAEtCK,uBAAqB,SAArBA,wBAAwB;AACpB,SAAKT,kBAAkBU,MAAK;EAChC;AACJ;", "names": ["_loadedStyleNames", "Set", "getLoadedStyleNames", "isStyleNameLoaded", "name", "has", "setLoadedStyleName", "add", "deleteLoadedStyleName", "clearLoadedStyleNames", "clear"]}