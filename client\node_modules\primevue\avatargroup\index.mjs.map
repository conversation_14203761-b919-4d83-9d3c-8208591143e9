{"version": 3, "file": "index.mjs", "sources": ["../../src/avatargroup/BaseAvatarGroup.vue", "../../src/avatargroup/AvatarGroup.vue", "../../src/avatargroup/AvatarGroup.vue?vue&type=template&id=3f7291ce&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport AvatarGroupStyle from 'primevue/avatargroup/style';\n\nexport default {\n    name: 'BaseAvatarGroup',\n    extends: BaseComponent,\n    style: AvatarGroupStyle,\n    provide() {\n        return {\n            $pcAvatarGroup: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot></slot>\n    </div>\n</template>\n\n<script>\nimport BaseAvatarGroup from './BaseAvatarGroup.vue';\n\nexport default {\n    name: 'AvatarGroup',\n    extends: BaseAvatarGroup,\n    inheritAttrs: false\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot></slot>\n    </div>\n</template>\n\n<script>\nimport BaseAvatarGroup from './BaseAvatarGroup.vue';\n\nexport default {\n    name: 'AvatarGroup',\n    extends: BaseAvatarGroup,\n    inheritAttrs: false\n};\n</script>\n"], "names": ["name", "BaseComponent", "style", "AvatarGroupStyle", "provide", "$pcAvatarGroup", "$parentInstance", "BaseAvatarGroup", "inheritAttrs", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "ptmi", "_renderSlot", "$slots"], "mappings": ";;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,iBAAiB;AACvB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAEC,gBAAgB;EACvBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,cAAc,EAAE,IAAI;AACpBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACLD,aAAe;AACXN,EAAAA,IAAI,EAAE,aAAa;AACnB,EAAA,SAAA,EAASO,QAAe;AACxBC,EAAAA,YAAY,EAAE;AAClB,CAAC;;;ECZG,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAEK,OAFLC,UAEK,CAAA;AAFC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA;KAAkBD,IAAI,CAAAE,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACjCC,UAAY,CAAAH,IAAA,CAAAI,MAAA,EAAA,SAAA,CAAA;;;;;;;"}