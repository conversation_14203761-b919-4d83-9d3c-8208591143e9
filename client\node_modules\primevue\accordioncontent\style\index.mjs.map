{"version": 3, "file": "index.mjs", "sources": ["../../../src/accordioncontent/style/AccordionContentStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-accordioncontent',\n    content: 'p-accordioncontent-content'\n};\n\nexport default BaseStyle.extend({\n    name: 'accordioncontent',\n    classes\n});\n"], "names": ["classes", "root", "content", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,oBAAoB;AAC1BC,EAAAA,OAAO,EAAE;AACb,CAAC;AAED,4BAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,kBAAkB;AACxBL,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}