{"version": 3, "file": "index.mjs", "sources": ["../../src/autocomplete/BaseAutoComplete.vue", "../../src/autocomplete/AutoComplete.vue", "../../src/autocomplete/AutoComplete.vue?vue&type=template&id=6e1b3882&lang.js"], "sourcesContent": ["<script>\nimport BaseInput from '@primevue/core/baseinput';\nimport AutoCompleteStyle from 'primevue/autocomplete/style';\n\nexport default {\n    name: 'BaseAutoComplete',\n    extends: BaseInput,\n    props: {\n        suggestions: {\n            type: Array,\n            default: null\n        },\n        optionLabel: null,\n        optionDisabled: null,\n        optionGroupLabel: null,\n        optionGroupChildren: null,\n        scrollHeight: {\n            type: String,\n            default: '14rem'\n        },\n        dropdown: {\n            type: Boolean,\n            default: false\n        },\n        dropdownMode: {\n            type: String,\n            default: 'blank'\n        },\n        multiple: {\n            type: Boolean,\n            default: false\n        },\n        loading: {\n            type: Boolean,\n            default: false\n        },\n        placeholder: {\n            type: String,\n            default: null\n        },\n        dataKey: {\n            type: String,\n            default: null\n        },\n        minLength: {\n            type: Number,\n            default: 1\n        },\n        delay: {\n            type: Number,\n            default: 300\n        },\n        appendTo: {\n            type: [String, Object],\n            default: 'body'\n        },\n        forceSelection: {\n            type: Boolean,\n            default: false\n        },\n        completeOnFocus: {\n            type: Boolean,\n            default: false\n        },\n        inputId: {\n            type: String,\n            default: null\n        },\n        inputStyle: {\n            type: Object,\n            default: null\n        },\n        inputClass: {\n            type: [String, Object],\n            default: null\n        },\n        panelStyle: {\n            type: Object,\n            default: null\n        },\n        panelClass: {\n            type: [String, Object],\n            default: null\n        },\n        overlayStyle: {\n            type: Object,\n            default: null\n        },\n        overlayClass: {\n            type: [String, Object],\n            default: null\n        },\n        dropdownIcon: {\n            type: String,\n            default: null\n        },\n        dropdownClass: {\n            type: [String, Object],\n            default: null\n        },\n        loader: {\n            type: String,\n            default: null\n        },\n        loadingIcon: {\n            type: String,\n            default: null\n        },\n        removeTokenIcon: {\n            type: String,\n            default: null\n        },\n        chipIcon: {\n            type: String,\n            default: null\n        },\n        virtualScrollerOptions: {\n            type: Object,\n            default: null\n        },\n        autoOptionFocus: {\n            type: Boolean,\n            default: false\n        },\n        selectOnFocus: {\n            type: Boolean,\n            default: false\n        },\n        focusOnHover: {\n            type: Boolean,\n            default: true\n        },\n        searchLocale: {\n            type: String,\n            default: undefined\n        },\n        searchMessage: {\n            type: String,\n            default: null\n        },\n        selectionMessage: {\n            type: String,\n            default: null\n        },\n        emptySelectionMessage: {\n            type: String,\n            default: null\n        },\n        emptySearchMessage: {\n            type: String,\n            default: null\n        },\n        showEmptyMessage: {\n            type: Boolean,\n            default: true\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        },\n        typeahead: {\n            type: Boolean,\n            default: true\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        }\n    },\n    style: AutoCompleteStyle,\n    provide() {\n        return {\n            $pcAutoComplete: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div ref=\"container\" :class=\"cx('root')\" :style=\"sx('root')\" @click=\"onContainerClick\" :data-p=\"containerDataP\" v-bind=\"ptmi('root')\">\n        <InputText\n            v-if=\"!multiple\"\n            ref=\"focusInput\"\n            :id=\"inputId\"\n            type=\"text\"\n            :name=\"$formName\"\n            :class=\"[cx('pcInputText'), inputClass]\"\n            :style=\"inputStyle\"\n            :defaultValue=\"inputValue\"\n            :placeholder=\"placeholder\"\n            :tabindex=\"!disabled ? tabindex : -1\"\n            :fluid=\"$fluid\"\n            :disabled=\"disabled\"\n            :size=\"size\"\n            :invalid=\"invalid\"\n            :variant=\"variant\"\n            autocomplete=\"off\"\n            role=\"combobox\"\n            :aria-label=\"ariaLabel\"\n            :aria-labelledby=\"ariaLabelledby\"\n            aria-haspopup=\"listbox\"\n            aria-autocomplete=\"list\"\n            :aria-expanded=\"overlayVisible\"\n            :aria-controls=\"panelId\"\n            :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n            @focus=\"onFocus\"\n            @blur=\"onBlur\"\n            @keydown=\"onKeyDown\"\n            @input=\"onInput\"\n            @change=\"onChange\"\n            :unstyled=\"unstyled\"\n            :data-p-has-dropdown=\"dropdown\"\n            :pt=\"ptm('pcInputText')\"\n        />\n        <ul\n            v-if=\"multiple\"\n            ref=\"multiContainer\"\n            :class=\"cx('inputMultiple')\"\n            tabindex=\"-1\"\n            role=\"listbox\"\n            aria-orientation=\"horizontal\"\n            :aria-activedescendant=\"focused ? focusedMultipleOptionId : undefined\"\n            @focus=\"onMultipleContainerFocus\"\n            @blur=\"onMultipleContainerBlur\"\n            @keydown=\"onMultipleContainerKeyDown\"\n            :data-p-has-dropdown=\"dropdown\"\n            :data-p=\"inputMultipleDataP\"\n            v-bind=\"ptm('inputMultiple')\"\n        >\n            <li\n                v-for=\"(option, i) of d_value\"\n                :key=\"`${i}_${getOptionLabel(option)}`\"\n                :id=\"$id + '_multiple_option_' + i\"\n                :class=\"cx('chipItem', { i })\"\n                role=\"option\"\n                :aria-label=\"getOptionLabel(option)\"\n                :aria-selected=\"true\"\n                :aria-setsize=\"d_value.length\"\n                :aria-posinset=\"i + 1\"\n                v-bind=\"ptm('chipItem')\"\n            >\n                <slot name=\"chip\" :class=\"cx('pcChip')\" :value=\"option\" :index=\"i\" :removeCallback=\"(event) => removeOption(event, i)\" v-bind=\"ptm('pcChip')\">\n                    <!-- TODO: removetokenicon and removeTokenIcon  deprecated since v4.0. Use chipicon slot and chipIcon prop-->\n                    <Chip\n                        :class=\"cx('pcChip')\"\n                        :label=\"getOptionLabel(option)\"\n                        :removeIcon=\"chipIcon || removeTokenIcon\"\n                        removable\n                        :unstyled=\"unstyled\"\n                        @remove=\"removeOption($event, i)\"\n                        :data-p-focused=\"focusedMultipleOptionIndex === i\"\n                        :pt=\"ptm('pcChip')\"\n                    >\n                        <template #removeicon>\n                            <slot :name=\"$slots.chipicon ? 'chipicon' : 'removetokenicon'\" :class=\"cx('chipIcon')\" :index=\"i\" :removeCallback=\"(event) => removeOption(event, i)\" />\n                        </template>\n                    </Chip>\n                </slot>\n            </li>\n            <li :class=\"cx('inputChip')\" role=\"option\" v-bind=\"ptm('inputChip')\">\n                <input\n                    ref=\"focusInput\"\n                    :id=\"inputId\"\n                    type=\"text\"\n                    :style=\"inputStyle\"\n                    :class=\"inputClass\"\n                    :placeholder=\"placeholder\"\n                    :tabindex=\"!disabled ? tabindex : -1\"\n                    :disabled=\"disabled\"\n                    autocomplete=\"off\"\n                    role=\"combobox\"\n                    :aria-label=\"ariaLabel\"\n                    :aria-labelledby=\"ariaLabelledby\"\n                    aria-haspopup=\"listbox\"\n                    aria-autocomplete=\"list\"\n                    :aria-expanded=\"overlayVisible\"\n                    :aria-controls=\"$id + '_list'\"\n                    :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n                    :aria-invalid=\"invalid || undefined\"\n                    @focus=\"onFocus\"\n                    @blur=\"onBlur\"\n                    @keydown=\"onKeyDown\"\n                    @input=\"onInput\"\n                    @change=\"onChange\"\n                    v-bind=\"ptm('input')\"\n                />\n            </li>\n        </ul>\n        <slot v-if=\"searching || loading\" :class=\"cx('loader')\" :name=\"$slots.loader ? 'loader' : 'loadingicon'\">\n            <i v-if=\"loader || loadingIcon\" :class=\"['pi-spin', cx('loader'), loader, loadingIcon]\" aria-hidden=\"true\" :data-p-has-dropdown=\"dropdown\" v-bind=\"ptm('loader')\" />\n            <SpinnerIcon v-else :class=\"cx('loader')\" spin aria-hidden=\"true\" :data-p-has-dropdown=\"dropdown\" v-bind=\"ptm('loader')\" />\n        </slot>\n        <slot :name=\"$slots.dropdown ? 'dropdown' : 'dropdownbutton'\" :toggleCallback=\"(event) => onDropdownClick(event)\">\n            <button\n                v-if=\"dropdown\"\n                ref=\"dropdownButton\"\n                type=\"button\"\n                :class=\"[cx('dropdown'), dropdownClass]\"\n                :disabled=\"disabled\"\n                aria-haspopup=\"listbox\"\n                :aria-expanded=\"overlayVisible\"\n                :aria-controls=\"panelId\"\n                @click=\"onDropdownClick\"\n                v-bind=\"ptm('dropdown')\"\n            >\n                <slot name=\"dropdownicon\" :class=\"dropdownIcon\">\n                    <component :is=\"dropdownIcon ? 'span' : 'ChevronDownIcon'\" :class=\"dropdownIcon\" v-bind=\"ptm('dropdownIcon')\" />\n                </slot>\n            </button>\n        </slot>\n        <span v-if=\"typeahead\" role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\" v-bind=\"ptm('hiddenSearchResult')\" :data-p-hidden-accessible=\"true\">\n            {{ searchResultMessageText }}\n        </span>\n        <Portal :appendTo=\"appendTo\">\n            <transition name=\"p-connected-overlay\" @enter=\"onOverlayEnter\" @after-enter=\"onOverlayAfterEnter\" @leave=\"onOverlayLeave\" @after-leave=\"onOverlayAfterLeave\" v-bind=\"ptm('transition')\">\n                <div\n                    v-if=\"overlayVisible\"\n                    :ref=\"overlayRef\"\n                    :id=\"panelId\"\n                    :class=\"[cx('overlay'), panelClass, overlayClass]\"\n                    :style=\"{ ...panelStyle, ...overlayStyle }\"\n                    @click=\"onOverlayClick\"\n                    @keydown=\"onOverlayKeyDown\"\n                    :data-p=\"overlayDataP\"\n                    v-bind=\"ptm('overlay')\"\n                >\n                    <slot name=\"header\" :value=\"d_value\" :suggestions=\"visibleOptions\"></slot>\n                    <div :class=\"cx('listContainer')\" :style=\"{ 'max-height': virtualScrollerDisabled ? scrollHeight : '' }\" v-bind=\"ptm('listContainer')\">\n                        <VirtualScroller :ref=\"virtualScrollerRef\" v-bind=\"virtualScrollerOptions\" :style=\"{ height: scrollHeight }\" :items=\"visibleOptions\" :tabindex=\"-1\" :disabled=\"virtualScrollerDisabled\" :pt=\"ptm('virtualScroller')\">\n                            <template v-slot:content=\"{ styleClass, contentRef, items, getItemOptions, contentStyle, itemSize }\">\n                                <ul :ref=\"(el) => listRef(el, contentRef)\" :id=\"$id + '_list'\" :class=\"[cx('list'), styleClass]\" :style=\"contentStyle\" role=\"listbox\" :aria-label=\"listAriaLabel\" v-bind=\"ptm('list')\">\n                                    <template v-for=\"(option, i) of items\" :key=\"getOptionRenderKey(option, getOptionIndex(i, getItemOptions))\">\n                                        <li\n                                            v-if=\"isOptionGroup(option)\"\n                                            :id=\"$id + '_' + getOptionIndex(i, getItemOptions)\"\n                                            :style=\"{ height: itemSize ? itemSize + 'px' : undefined }\"\n                                            :class=\"cx('optionGroup')\"\n                                            role=\"option\"\n                                            v-bind=\"ptm('optionGroup')\"\n                                        >\n                                            <slot name=\"optiongroup\" :option=\"option.optionGroup\" :index=\"getOptionIndex(i, getItemOptions)\">{{ getOptionGroupLabel(option.optionGroup) }}</slot>\n                                        </li>\n                                        <li\n                                            v-else\n                                            :id=\"$id + '_' + getOptionIndex(i, getItemOptions)\"\n                                            v-ripple\n                                            :style=\"{ height: itemSize ? itemSize + 'px' : undefined }\"\n                                            :class=\"cx('option', { option, i, getItemOptions })\"\n                                            role=\"option\"\n                                            :aria-label=\"getOptionLabel(option)\"\n                                            :aria-selected=\"isSelected(option)\"\n                                            :aria-disabled=\"isOptionDisabled(option)\"\n                                            :aria-setsize=\"ariaSetSize\"\n                                            :aria-posinset=\"getAriaPosInset(getOptionIndex(i, getItemOptions))\"\n                                            @click=\"onOptionSelect($event, option)\"\n                                            @mousemove=\"onOptionMouseMove($event, getOptionIndex(i, getItemOptions))\"\n                                            :data-p-selected=\"isSelected(option)\"\n                                            :data-p-focused=\"focusedOptionIndex === getOptionIndex(i, getItemOptions)\"\n                                            :data-p-disabled=\"isOptionDisabled(option)\"\n                                            v-bind=\"getPTOptions(option, getItemOptions, i, 'option')\"\n                                        >\n                                            <slot name=\"option\" :option=\"option\" :index=\"getOptionIndex(i, getItemOptions)\">{{ getOptionLabel(option) }}</slot>\n                                        </li>\n                                    </template>\n                                    <li v-if=\"showEmptyMessage && (!items || (items && items.length === 0))\" :class=\"cx('emptyMessage')\" role=\"option\" v-bind=\"ptm('emptyMessage')\">\n                                        <slot name=\"empty\">{{ searchResultMessageText }}</slot>\n                                    </li>\n                                </ul>\n                            </template>\n                            <template v-if=\"$slots.loader\" v-slot:loader=\"{ options }\">\n                                <slot name=\"loader\" :options=\"options\"></slot>\n                            </template>\n                        </VirtualScroller>\n                    </div>\n                    <slot name=\"footer\" :value=\"d_value\" :suggestions=\"visibleOptions\"></slot>\n                    <span role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\" v-bind=\"ptm('hiddenSelectedMessage')\" :data-p-hidden-accessible=\"true\">\n                        {{ selectedMessageText }}\n                    </span>\n                </div>\n            </transition>\n        </Portal>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { absolutePosition, addStyle, findSingle, focus, getOuterWidth, isTouchDevice, relativePosition } from '@primeuix/utils/dom';\nimport { equals, findLastIndex, isEmpty, isNotEmpty, resolveFieldData } from '@primeuix/utils/object';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { ConnectedOverlayScrollHandler } from '@primevue/core/utils';\nimport ChevronDownIcon from '@primevue/icons/chevrondown';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport Chip from 'primevue/chip';\nimport InputText from 'primevue/inputtext';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport Ripple from 'primevue/ripple';\nimport VirtualScroller from 'primevue/virtualscroller';\nimport BaseAutoComplete from './BaseAutoComplete.vue';\n\nexport default {\n    name: 'AutoComplete',\n    extends: BaseAutoComplete,\n    inheritAttrs: false,\n    emits: ['change', 'focus', 'blur', 'item-select', 'item-unselect', 'option-select', 'option-unselect', 'dropdown-click', 'clear', 'complete', 'before-show', 'before-hide', 'show', 'hide'],\n    inject: {\n        $pcFluid: { default: null }\n    },\n    outsideClickListener: null,\n    resizeListener: null,\n    scrollHandler: null,\n    overlay: null,\n    virtualScroller: null,\n    searchTimeout: null,\n    dirty: false,\n    startRangeIndex: -1,\n    data() {\n        return {\n            clicked: false,\n            focused: false,\n            focusedOptionIndex: -1,\n            focusedMultipleOptionIndex: -1,\n            overlayVisible: false,\n            searching: false\n        };\n    },\n    watch: {\n        suggestions() {\n            if (this.searching) {\n                this.show();\n                this.focusedOptionIndex = this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n                this.searching = false;\n                !this.showEmptyMessage && this.visibleOptions.length === 0 && this.hide();\n            }\n\n            this.autoUpdateModel();\n        }\n    },\n    mounted() {\n        this.autoUpdateModel();\n    },\n    updated() {\n        if (this.overlayVisible) {\n            this.alignOverlay();\n        }\n    },\n    beforeUnmount() {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        if (this.overlay) {\n            ZIndex.clear(this.overlay);\n            this.overlay = null;\n        }\n    },\n    methods: {\n        getOptionIndex(index, fn) {\n            return this.virtualScrollerDisabled ? index : fn && fn(index)['index'];\n        },\n        getOptionLabel(option) {\n            return this.optionLabel ? resolveFieldData(option, this.optionLabel) : option;\n        },\n        getOptionValue(option) {\n            return option; // TODO: The 'optionValue' properties can be added.\n        },\n        getOptionRenderKey(option, index) {\n            return (this.dataKey ? resolveFieldData(option, this.dataKey) : this.getOptionLabel(option)) + '_' + index;\n        },\n        getPTOptions(option, itemOptions, index, key) {\n            return this.ptm(key, {\n                context: {\n                    option,\n                    index,\n                    selected: this.isSelected(option),\n                    focused: this.focusedOptionIndex === this.getOptionIndex(index, itemOptions),\n                    disabled: this.isOptionDisabled(option)\n                }\n            });\n        },\n        isOptionDisabled(option) {\n            return this.optionDisabled ? resolveFieldData(option, this.optionDisabled) : false;\n        },\n        isOptionGroup(option) {\n            return this.optionGroupLabel && option.optionGroup && option.group;\n        },\n        getOptionGroupLabel(optionGroup) {\n            return resolveFieldData(optionGroup, this.optionGroupLabel);\n        },\n        getOptionGroupChildren(optionGroup) {\n            return resolveFieldData(optionGroup, this.optionGroupChildren);\n        },\n        getAriaPosInset(index) {\n            return (this.optionGroupLabel ? index - this.visibleOptions.slice(0, index).filter((option) => this.isOptionGroup(option)).length : index) + 1;\n        },\n        show(isFocus) {\n            this.$emit('before-show');\n            this.dirty = true;\n            this.overlayVisible = true;\n            this.focusedOptionIndex = this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n            isFocus && focus(this.multiple ? this.$refs.focusInput : this.$refs.focusInput.$el);\n        },\n        hide(isFocus) {\n            const _hide = () => {\n                this.$emit('before-hide');\n                this.dirty = isFocus;\n                this.overlayVisible = false;\n                this.clicked = false;\n                this.focusedOptionIndex = -1;\n\n                isFocus && focus(this.multiple ? this.$refs.focusInput : this.$refs.focusInput?.$el);\n            };\n\n            setTimeout(() => {\n                _hide();\n            }, 0); // For ScreenReaders\n        },\n        onFocus(event) {\n            if (this.disabled) {\n                // For ScreenReaders\n                return;\n            }\n\n            if (!this.dirty && this.completeOnFocus) {\n                this.search(event, event.target.value, 'focus');\n            }\n\n            this.dirty = true;\n            this.focused = true;\n\n            if (this.overlayVisible) {\n                this.focusedOptionIndex = this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n                this.scrollInView(this.focusedOptionIndex);\n            }\n\n            this.$emit('focus', event);\n        },\n        onBlur(event) {\n            this.dirty = false;\n            this.focused = false;\n            this.focusedOptionIndex = -1;\n            this.$emit('blur', event);\n            this.formField.onBlur?.();\n        },\n        onKeyDown(event) {\n            if (this.disabled) {\n                event.preventDefault();\n\n                return;\n            }\n\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeftKey(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRightKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'PageDown':\n                    this.onPageDownKey(event);\n                    break;\n\n                case 'PageUp':\n                    this.onPageUpKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event);\n                    break;\n\n                case 'ShiftLeft':\n                case 'ShiftRight':\n                    this.onShiftKey(event);\n                    break;\n\n                case 'Backspace':\n                    this.onBackspaceKey(event);\n                    break;\n\n                default:\n                    break;\n            }\n\n            this.clicked = false;\n        },\n        onInput(event) {\n            if (this.typeahead) {\n                if (this.searchTimeout) {\n                    clearTimeout(this.searchTimeout);\n                }\n\n                let query = event.target.value;\n\n                if (!this.multiple) {\n                    this.updateModel(event, query);\n                }\n\n                if (query.length === 0) {\n                    this.hide();\n                    this.$emit('clear');\n                } else {\n                    if (query.length >= this.minLength) {\n                        this.focusedOptionIndex = -1;\n\n                        this.searchTimeout = setTimeout(() => {\n                            this.search(event, query, 'input');\n                        }, this.delay);\n                    } else {\n                        this.hide();\n                    }\n                }\n            }\n        },\n        onChange(event) {\n            if (this.forceSelection) {\n                let valid = false;\n\n                // when forceSelection is on, prevent called twice onOptionSelect()\n                if (this.visibleOptions && !this.multiple) {\n                    let value = this.multiple ? this.$refs.focusInput.value : this.$refs.focusInput?.$el?.value;\n                    const matchedValue = this.visibleOptions.find((option) => this.isOptionMatched(option, value || ''));\n\n                    if (matchedValue !== undefined) {\n                        valid = true;\n                        !this.isSelected(matchedValue) && this.onOptionSelect(event, matchedValue);\n                    }\n                }\n\n                if (!valid) {\n                    if (this.multiple) {\n                        this.$refs.focusInput.value = '';\n                    } else {\n                        const inputEl = this.$refs.focusInput?.$el;\n                        inputEl && (inputEl.value = '');\n                    }\n                    this.$emit('clear');\n                    !this.multiple && this.updateModel(event, null);\n                }\n            }\n        },\n        onMultipleContainerFocus() {\n            if (this.disabled) {\n                // For ScreenReaders\n                return;\n            }\n\n            this.focused = true;\n        },\n        onMultipleContainerBlur() {\n            this.focusedMultipleOptionIndex = -1;\n            this.focused = false;\n        },\n        onMultipleContainerKeyDown(event) {\n            if (this.disabled) {\n                event.preventDefault();\n\n                return;\n            }\n\n            switch (event.code) {\n                case 'ArrowLeft':\n                    this.onArrowLeftKeyOnMultiple(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRightKeyOnMultiple(event);\n                    break;\n\n                case 'Backspace':\n                    this.onBackspaceKeyOnMultiple(event);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onContainerClick(event) {\n            this.clicked = true;\n\n            if (this.disabled || this.searching || this.loading || this.isDropdownClicked(event)) {\n                return;\n            }\n\n            if (!this.overlay || !this.overlay.contains(event.target)) {\n                focus(this.multiple ? this.$refs.focusInput : this.$refs.focusInput.$el);\n            }\n        },\n        onDropdownClick(event) {\n            let query = undefined;\n\n            if (this.overlayVisible) {\n                this.hide(true);\n            } else {\n                let target = this.multiple ? this.$refs.focusInput : this.$refs.focusInput.$el;\n\n                focus(target);\n                query = target.value;\n\n                if (this.dropdownMode === 'blank') this.search(event, '', 'dropdown');\n                else if (this.dropdownMode === 'current') this.search(event, query, 'dropdown');\n            }\n\n            this.$emit('dropdown-click', { originalEvent: event, query });\n        },\n        onOptionSelect(event, option, isHide = true) {\n            const value = this.getOptionValue(option);\n\n            if (this.multiple) {\n                this.$refs.focusInput.value = '';\n\n                if (!this.isSelected(option)) {\n                    this.updateModel(event, [...(this.d_value || []), value]);\n                }\n            } else {\n                this.updateModel(event, value);\n            }\n\n            this.$emit('item-select', { originalEvent: event, value: option });\n            this.$emit('option-select', { originalEvent: event, value: option });\n\n            isHide && this.hide(true);\n        },\n        onOptionMouseMove(event, index) {\n            if (this.focusOnHover) {\n                this.changeFocusedOptionIndex(event, index);\n            }\n        },\n        onOptionSelectRange(event, start = -1, end = -1) {\n            start === -1 && (start = this.findNearestSelectedOptionIndex(end, true));\n            end === -1 && (end = this.findNearestSelectedOptionIndex(start));\n\n            if (start !== -1 && end !== -1) {\n                const rangeStart = Math.min(start, end);\n                const rangeEnd = Math.max(start, end);\n                const value = this.visibleOptions\n                    .slice(rangeStart, rangeEnd + 1)\n                    .filter((option) => this.isValidOption(option))\n                    .map((option) => this.getOptionValue(option));\n\n                this.updateModel(event, value);\n            }\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.$el\n            });\n        },\n        onOverlayKeyDown(event) {\n            switch (event.code) {\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDownKey(event) {\n            if (!this.overlayVisible) {\n                return;\n            }\n\n            const optionIndex = this.focusedOptionIndex !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex) : this.clicked ? this.findFirstOptionIndex() : this.findFirstFocusedOptionIndex();\n\n            if (this.multiple && event.shiftKey) {\n                this.onOptionSelectRange(event, this.startRangeIndex, optionIndex);\n            }\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n\n            event.preventDefault();\n        },\n        onArrowUpKey(event) {\n            if (!this.overlayVisible) {\n                return;\n            }\n\n            if (event.altKey) {\n                if (this.focusedOptionIndex !== -1) {\n                    this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n                }\n\n                this.overlayVisible && this.hide();\n                event.preventDefault();\n            } else {\n                const optionIndex = this.focusedOptionIndex !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex) : this.clicked ? this.findLastOptionIndex() : this.findLastFocusedOptionIndex();\n\n                if (this.multiple && event.shiftKey) {\n                    this.onOptionSelectRange(event, optionIndex, this.startRangeIndex);\n                }\n\n                this.changeFocusedOptionIndex(event, optionIndex);\n\n                event.preventDefault();\n            }\n        },\n        onArrowLeftKey(event) {\n            const target = event.currentTarget;\n\n            this.focusedOptionIndex = -1;\n\n            if (this.multiple) {\n                if (isEmpty(target.value) && this.$filled) {\n                    focus(this.$refs.multiContainer);\n                    this.focusedMultipleOptionIndex = this.d_value.length;\n                } else {\n                    event.stopPropagation(); // To prevent onArrowLeftKeyOnMultiple method\n                }\n            }\n        },\n        onArrowRightKey(event) {\n            this.focusedOptionIndex = -1;\n\n            this.multiple && event.stopPropagation(); // To prevent onArrowRightKeyOnMultiple method\n        },\n        onHomeKey(event) {\n            const { currentTarget } = event;\n            const len = currentTarget.value.length;\n            const metaKey = event.metaKey || event.ctrlKey;\n            const optionIndex = this.findFirstOptionIndex();\n\n            if (this.multiple && event.shiftKey && metaKey) {\n                this.onOptionSelectRange(event, optionIndex, this.startRangeIndex);\n            }\n\n            currentTarget.setSelectionRange(0, event.shiftKey ? len : 0);\n            this.focusedOptionIndex = -1;\n\n            event.preventDefault();\n        },\n        onEndKey(event) {\n            const { currentTarget } = event;\n            const len = currentTarget.value.length;\n            const metaKey = event.metaKey || event.ctrlKey;\n            const optionIndex = this.findLastOptionIndex();\n\n            if (this.multiple && event.shiftKey && metaKey) {\n                this.onOptionSelectRange(event, this.startRangeIndex, optionIndex);\n            }\n\n            currentTarget.setSelectionRange(event.shiftKey ? 0 : len, len);\n            this.focusedOptionIndex = -1;\n\n            event.preventDefault();\n        },\n        onPageUpKey(event) {\n            this.scrollInView(0);\n            event.preventDefault();\n        },\n        onPageDownKey(event) {\n            this.scrollInView(this.visibleOptions.length - 1);\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            if (!this.typeahead) {\n                if (this.multiple) {\n                    if (event.target.value.trim()) {\n                        this.updateModel(event, [...(this.d_value || []), event.target.value.trim()]);\n                        this.$refs.focusInput.value = '';\n                    }\n                }\n            } else {\n                if (!this.overlayVisible) {\n                    this.focusedOptionIndex = -1; // reset\n                    this.onArrowDownKey(event);\n                } else {\n                    if (this.focusedOptionIndex !== -1) {\n                        if (this.multiple && event.shiftKey) {\n                            this.onOptionSelectRange(event, this.focusedOptionIndex);\n                            event.preventDefault();\n                        } else {\n                            this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n                        }\n                    }\n\n                    this.hide();\n                }\n            }\n        },\n        onSpaceKey(event) {\n            if (this.focusedOptionIndex !== -1) {\n                this.onEnterKey(event);\n            }\n        },\n        onEscapeKey(event) {\n            this.overlayVisible && this.hide(true);\n            event.preventDefault();\n        },\n        onTabKey(event) {\n            if (this.focusedOptionIndex !== -1) {\n                this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n            }\n\n            this.overlayVisible && this.hide();\n        },\n        onShiftKey() {\n            this.startRangeIndex = this.focusedOptionIndex;\n        },\n        onBackspaceKey(event) {\n            if (this.multiple) {\n                if (isNotEmpty(this.d_value) && !this.$refs.focusInput.value) {\n                    const removedValue = this.d_value[this.d_value.length - 1];\n                    const newValue = this.d_value.slice(0, -1);\n\n                    this.writeValue(newValue, event);\n                    this.$emit('item-unselect', { originalEvent: event, value: removedValue });\n                    this.$emit('option-unselect', { originalEvent: event, value: removedValue });\n                }\n\n                event.stopPropagation(); // To prevent onBackspaceKeyOnMultiple method\n            }\n        },\n        onArrowLeftKeyOnMultiple() {\n            this.focusedMultipleOptionIndex = this.focusedMultipleOptionIndex < 1 ? 0 : this.focusedMultipleOptionIndex - 1;\n        },\n        onArrowRightKeyOnMultiple() {\n            this.focusedMultipleOptionIndex++;\n\n            if (this.focusedMultipleOptionIndex > this.d_value.length - 1) {\n                this.focusedMultipleOptionIndex = -1;\n                focus(this.$refs.focusInput);\n            }\n        },\n        onBackspaceKeyOnMultiple(event) {\n            if (this.focusedMultipleOptionIndex !== -1) {\n                this.removeOption(event, this.focusedMultipleOptionIndex);\n            }\n        },\n        onOverlayEnter(el) {\n            ZIndex.set('overlay', el, this.$primevue.config.zIndex.overlay);\n\n            addStyle(el, { position: 'absolute', top: '0' });\n            this.alignOverlay();\n\n            // Issue: #7508\n            this.$attrSelector && el.setAttribute(this.$attrSelector, '');\n        },\n        onOverlayAfterEnter() {\n            this.bindOutsideClickListener();\n            this.bindScrollListener();\n            this.bindResizeListener();\n\n            this.$emit('show');\n        },\n        onOverlayLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindScrollListener();\n            this.unbindResizeListener();\n\n            this.$emit('hide');\n            this.overlay = null;\n        },\n        onOverlayAfterLeave(el) {\n            ZIndex.clear(el);\n        },\n        alignOverlay() {\n            let target = this.multiple ? this.$refs.multiContainer : this.$refs.focusInput.$el;\n\n            if (this.appendTo === 'self') {\n                relativePosition(this.overlay, target);\n            } else {\n                this.overlay.style.minWidth = getOuterWidth(target) + 'px';\n                absolutePosition(this.overlay, target);\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = (event) => {\n                    if (this.overlayVisible && this.overlay && this.isOutsideClicked(event)) {\n                        this.hide();\n                    }\n                };\n\n                document.addEventListener('click', this.outsideClickListener, true);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener, true);\n                this.outsideClickListener = null;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.$refs.container, () => {\n                    if (this.overlayVisible) {\n                        this.hide();\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = () => {\n                    if (this.overlayVisible && !isTouchDevice()) {\n                        this.hide();\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        isOutsideClicked(event) {\n            return !this.overlay.contains(event.target) && !this.isInputClicked(event) && !this.isDropdownClicked(event);\n        },\n        isInputClicked(event) {\n            if (this.multiple) return event.target === this.$refs.multiContainer || this.$refs.multiContainer.contains(event.target);\n            else return event.target === this.$refs.focusInput.$el;\n        },\n        isDropdownClicked(event) {\n            return this.$refs.dropdownButton ? event.target === this.$refs.dropdownButton || this.$refs.dropdownButton.contains(event.target) : false;\n        },\n        isOptionMatched(option, value) {\n            return this.isValidOption(option) && this.getOptionLabel(option)?.toLocaleLowerCase(this.searchLocale) === value.toLocaleLowerCase(this.searchLocale);\n        },\n        isValidOption(option) {\n            return isNotEmpty(option) && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n        },\n        isValidSelectedOption(option) {\n            return this.isValidOption(option) && this.isSelected(option);\n        },\n        isEquals(value1, value2) {\n            return equals(value1, value2, this.equalityKey);\n        },\n        isSelected(option) {\n            const optionValue = this.getOptionValue(option);\n\n            return this.multiple ? (this.d_value || []).some((value) => this.isEquals(value, optionValue)) : this.isEquals(this.d_value, this.getOptionValue(option));\n        },\n        findFirstOptionIndex() {\n            return this.visibleOptions.findIndex((option) => this.isValidOption(option));\n        },\n        findLastOptionIndex() {\n            return findLastIndex(this.visibleOptions, (option) => this.isValidOption(option));\n        },\n        findNextOptionIndex(index) {\n            const matchedOptionIndex = index < this.visibleOptions.length - 1 ? this.visibleOptions.slice(index + 1).findIndex((option) => this.isValidOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n        },\n        findPrevOptionIndex(index) {\n            const matchedOptionIndex = index > 0 ? findLastIndex(this.visibleOptions.slice(0, index), (option) => this.isValidOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n        },\n        findSelectedOptionIndex() {\n            return this.$filled ? this.visibleOptions.findIndex((option) => this.isValidSelectedOption(option)) : -1;\n        },\n        findFirstFocusedOptionIndex() {\n            const selectedIndex = this.findSelectedOptionIndex();\n\n            return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n        },\n        findLastFocusedOptionIndex() {\n            const selectedIndex = this.findSelectedOptionIndex();\n\n            return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n        },\n        search(event, query, source) {\n            //allow empty string but not undefined or null\n            if (query === undefined || query === null) {\n                return;\n            }\n\n            //do not search blank values on input change\n            if (source === 'input' && query.trim().length === 0) {\n                return;\n            }\n\n            this.searching = true;\n            this.$emit('complete', { originalEvent: event, query });\n        },\n        removeOption(event, index) {\n            const removedOption = this.d_value[index];\n            const value = this.d_value.filter((_, i) => i !== index).map((option) => this.getOptionValue(option));\n\n            this.updateModel(event, value);\n            this.$emit('item-unselect', { originalEvent: event, value: removedOption });\n            this.$emit('option-unselect', { originalEvent: event, value: removedOption });\n            this.dirty = true;\n            focus(this.multiple ? this.$refs.focusInput : this.$refs.focusInput.$el);\n        },\n        changeFocusedOptionIndex(event, index) {\n            if (this.focusedOptionIndex !== index) {\n                this.focusedOptionIndex = index;\n                this.scrollInView();\n\n                if (this.selectOnFocus) {\n                    this.onOptionSelect(event, this.visibleOptions[index], false);\n                }\n            }\n        },\n        scrollInView(index = -1) {\n            this.$nextTick(() => {\n                const id = index !== -1 ? `${this.$id}_${index}` : this.focusedOptionId;\n                const element = findSingle(this.list, `li[id=\"${id}\"]`);\n\n                if (element) {\n                    element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'start' });\n                } else if (!this.virtualScrollerDisabled) {\n                    this.virtualScroller && this.virtualScroller.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex);\n                }\n            });\n        },\n        autoUpdateModel() {\n            if (this.selectOnFocus && this.autoOptionFocus && !this.$filled) {\n                this.focusedOptionIndex = this.findFirstFocusedOptionIndex();\n                this.onOptionSelect(null, this.visibleOptions[this.focusedOptionIndex], false);\n            }\n        },\n        updateModel(event, value) {\n            this.writeValue(value, event);\n            this.$emit('change', { originalEvent: event, value });\n        },\n        flatOptions(options) {\n            return (options || []).reduce((result, option, index) => {\n                result.push({ optionGroup: option, group: true, index });\n\n                const optionGroupChildren = this.getOptionGroupChildren(option);\n\n                optionGroupChildren && optionGroupChildren.forEach((o) => result.push(o));\n\n                return result;\n            }, []);\n        },\n        overlayRef(el) {\n            this.overlay = el;\n        },\n        listRef(el, contentRef) {\n            this.list = el;\n            contentRef && contentRef(el); // For VirtualScroller\n        },\n        virtualScrollerRef(el) {\n            this.virtualScroller = el;\n        },\n        findNextSelectedOptionIndex(index) {\n            const matchedOptionIndex = this.$filled && index < this.visibleOptions.length - 1 ? this.visibleOptions.slice(index + 1).findIndex((option) => this.isValidSelectedOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : -1;\n        },\n        findPrevSelectedOptionIndex(index) {\n            const matchedOptionIndex = this.$filled && index > 0 ? findLastIndex(this.visibleOptions.slice(0, index), (option) => this.isValidSelectedOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex : -1;\n        },\n        findNearestSelectedOptionIndex(index, firstCheckUp = false) {\n            let matchedOptionIndex = -1;\n\n            if (this.$filled) {\n                if (firstCheckUp) {\n                    matchedOptionIndex = this.findPrevSelectedOptionIndex(index);\n                    matchedOptionIndex = matchedOptionIndex === -1 ? this.findNextSelectedOptionIndex(index) : matchedOptionIndex;\n                } else {\n                    matchedOptionIndex = this.findNextSelectedOptionIndex(index);\n                    matchedOptionIndex = matchedOptionIndex === -1 ? this.findPrevSelectedOptionIndex(index) : matchedOptionIndex;\n                }\n            }\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n        }\n    },\n    computed: {\n        visibleOptions() {\n            return this.optionGroupLabel ? this.flatOptions(this.suggestions) : this.suggestions || [];\n        },\n        inputValue() {\n            if (this.$filled) {\n                if (typeof this.d_value === 'object') {\n                    const label = this.getOptionLabel(this.d_value);\n\n                    return label != null ? label : this.d_value;\n                } else {\n                    return this.d_value;\n                }\n            } else {\n                return '';\n            }\n        },\n        // @deprecated use $filled instead.\n        hasSelectedOption() {\n            return this.$filled;\n        },\n        equalityKey() {\n            // @todo: The 'optionValue' properties can be added.\n            return this.dataKey;\n        },\n        searchResultMessageText() {\n            return isNotEmpty(this.visibleOptions) && this.overlayVisible ? this.searchMessageText.replaceAll('{0}', this.visibleOptions.length) : this.emptySearchMessageText;\n        },\n        searchMessageText() {\n            return this.searchMessage || this.$primevue.config.locale.searchMessage || '';\n        },\n        emptySearchMessageText() {\n            return this.emptySearchMessage || this.$primevue.config.locale.emptySearchMessage || '';\n        },\n        selectionMessageText() {\n            return this.selectionMessage || this.$primevue.config.locale.selectionMessage || '';\n        },\n        emptySelectionMessageText() {\n            return this.emptySelectionMessage || this.$primevue.config.locale.emptySelectionMessage || '';\n        },\n        selectedMessageText() {\n            return this.$filled ? this.selectionMessageText.replaceAll('{0}', this.multiple ? this.d_value.length : '1') : this.emptySelectionMessageText;\n        },\n        listAriaLabel() {\n            return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.listLabel : undefined;\n        },\n        focusedOptionId() {\n            return this.focusedOptionIndex !== -1 ? `${this.$id}_${this.focusedOptionIndex}` : null;\n        },\n        focusedMultipleOptionId() {\n            return this.focusedMultipleOptionIndex !== -1 ? `${this.$id}_multiple_option_${this.focusedMultipleOptionIndex}` : null;\n        },\n        ariaSetSize() {\n            return this.visibleOptions.filter((option) => !this.isOptionGroup(option)).length;\n        },\n        virtualScrollerDisabled() {\n            return !this.virtualScrollerOptions;\n        },\n        panelId() {\n            return this.$id + '_panel';\n        },\n        containerDataP() {\n            return cn({\n                fluid: this.$fluid\n            });\n        },\n        overlayDataP() {\n            return cn({\n                ['portal-' + this.appendTo]: 'portal-' + this.appendTo\n            });\n        },\n        inputMultipleDataP() {\n            return cn({\n                invalid: this.$invalid,\n                disabled: this.disabled,\n                focus: this.focused,\n                fluid: this.$fluid,\n                filled: this.$variant === 'filled',\n                empty: !this.$filled,\n                [this.size]: this.size\n            });\n        }\n    },\n    components: {\n        InputText,\n        VirtualScroller,\n        Portal,\n        ChevronDownIcon,\n        SpinnerIcon,\n        Chip\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <div ref=\"container\" :class=\"cx('root')\" :style=\"sx('root')\" @click=\"onContainerClick\" :data-p=\"containerDataP\" v-bind=\"ptmi('root')\">\n        <InputText\n            v-if=\"!multiple\"\n            ref=\"focusInput\"\n            :id=\"inputId\"\n            type=\"text\"\n            :name=\"$formName\"\n            :class=\"[cx('pcInputText'), inputClass]\"\n            :style=\"inputStyle\"\n            :defaultValue=\"inputValue\"\n            :placeholder=\"placeholder\"\n            :tabindex=\"!disabled ? tabindex : -1\"\n            :fluid=\"$fluid\"\n            :disabled=\"disabled\"\n            :size=\"size\"\n            :invalid=\"invalid\"\n            :variant=\"variant\"\n            autocomplete=\"off\"\n            role=\"combobox\"\n            :aria-label=\"ariaLabel\"\n            :aria-labelledby=\"ariaLabelledby\"\n            aria-haspopup=\"listbox\"\n            aria-autocomplete=\"list\"\n            :aria-expanded=\"overlayVisible\"\n            :aria-controls=\"panelId\"\n            :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n            @focus=\"onFocus\"\n            @blur=\"onBlur\"\n            @keydown=\"onKeyDown\"\n            @input=\"onInput\"\n            @change=\"onChange\"\n            :unstyled=\"unstyled\"\n            :data-p-has-dropdown=\"dropdown\"\n            :pt=\"ptm('pcInputText')\"\n        />\n        <ul\n            v-if=\"multiple\"\n            ref=\"multiContainer\"\n            :class=\"cx('inputMultiple')\"\n            tabindex=\"-1\"\n            role=\"listbox\"\n            aria-orientation=\"horizontal\"\n            :aria-activedescendant=\"focused ? focusedMultipleOptionId : undefined\"\n            @focus=\"onMultipleContainerFocus\"\n            @blur=\"onMultipleContainerBlur\"\n            @keydown=\"onMultipleContainerKeyDown\"\n            :data-p-has-dropdown=\"dropdown\"\n            :data-p=\"inputMultipleDataP\"\n            v-bind=\"ptm('inputMultiple')\"\n        >\n            <li\n                v-for=\"(option, i) of d_value\"\n                :key=\"`${i}_${getOptionLabel(option)}`\"\n                :id=\"$id + '_multiple_option_' + i\"\n                :class=\"cx('chipItem', { i })\"\n                role=\"option\"\n                :aria-label=\"getOptionLabel(option)\"\n                :aria-selected=\"true\"\n                :aria-setsize=\"d_value.length\"\n                :aria-posinset=\"i + 1\"\n                v-bind=\"ptm('chipItem')\"\n            >\n                <slot name=\"chip\" :class=\"cx('pcChip')\" :value=\"option\" :index=\"i\" :removeCallback=\"(event) => removeOption(event, i)\" v-bind=\"ptm('pcChip')\">\n                    <!-- TODO: removetokenicon and removeTokenIcon  deprecated since v4.0. Use chipicon slot and chipIcon prop-->\n                    <Chip\n                        :class=\"cx('pcChip')\"\n                        :label=\"getOptionLabel(option)\"\n                        :removeIcon=\"chipIcon || removeTokenIcon\"\n                        removable\n                        :unstyled=\"unstyled\"\n                        @remove=\"removeOption($event, i)\"\n                        :data-p-focused=\"focusedMultipleOptionIndex === i\"\n                        :pt=\"ptm('pcChip')\"\n                    >\n                        <template #removeicon>\n                            <slot :name=\"$slots.chipicon ? 'chipicon' : 'removetokenicon'\" :class=\"cx('chipIcon')\" :index=\"i\" :removeCallback=\"(event) => removeOption(event, i)\" />\n                        </template>\n                    </Chip>\n                </slot>\n            </li>\n            <li :class=\"cx('inputChip')\" role=\"option\" v-bind=\"ptm('inputChip')\">\n                <input\n                    ref=\"focusInput\"\n                    :id=\"inputId\"\n                    type=\"text\"\n                    :style=\"inputStyle\"\n                    :class=\"inputClass\"\n                    :placeholder=\"placeholder\"\n                    :tabindex=\"!disabled ? tabindex : -1\"\n                    :disabled=\"disabled\"\n                    autocomplete=\"off\"\n                    role=\"combobox\"\n                    :aria-label=\"ariaLabel\"\n                    :aria-labelledby=\"ariaLabelledby\"\n                    aria-haspopup=\"listbox\"\n                    aria-autocomplete=\"list\"\n                    :aria-expanded=\"overlayVisible\"\n                    :aria-controls=\"$id + '_list'\"\n                    :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n                    :aria-invalid=\"invalid || undefined\"\n                    @focus=\"onFocus\"\n                    @blur=\"onBlur\"\n                    @keydown=\"onKeyDown\"\n                    @input=\"onInput\"\n                    @change=\"onChange\"\n                    v-bind=\"ptm('input')\"\n                />\n            </li>\n        </ul>\n        <slot v-if=\"searching || loading\" :class=\"cx('loader')\" :name=\"$slots.loader ? 'loader' : 'loadingicon'\">\n            <i v-if=\"loader || loadingIcon\" :class=\"['pi-spin', cx('loader'), loader, loadingIcon]\" aria-hidden=\"true\" :data-p-has-dropdown=\"dropdown\" v-bind=\"ptm('loader')\" />\n            <SpinnerIcon v-else :class=\"cx('loader')\" spin aria-hidden=\"true\" :data-p-has-dropdown=\"dropdown\" v-bind=\"ptm('loader')\" />\n        </slot>\n        <slot :name=\"$slots.dropdown ? 'dropdown' : 'dropdownbutton'\" :toggleCallback=\"(event) => onDropdownClick(event)\">\n            <button\n                v-if=\"dropdown\"\n                ref=\"dropdownButton\"\n                type=\"button\"\n                :class=\"[cx('dropdown'), dropdownClass]\"\n                :disabled=\"disabled\"\n                aria-haspopup=\"listbox\"\n                :aria-expanded=\"overlayVisible\"\n                :aria-controls=\"panelId\"\n                @click=\"onDropdownClick\"\n                v-bind=\"ptm('dropdown')\"\n            >\n                <slot name=\"dropdownicon\" :class=\"dropdownIcon\">\n                    <component :is=\"dropdownIcon ? 'span' : 'ChevronDownIcon'\" :class=\"dropdownIcon\" v-bind=\"ptm('dropdownIcon')\" />\n                </slot>\n            </button>\n        </slot>\n        <span v-if=\"typeahead\" role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\" v-bind=\"ptm('hiddenSearchResult')\" :data-p-hidden-accessible=\"true\">\n            {{ searchResultMessageText }}\n        </span>\n        <Portal :appendTo=\"appendTo\">\n            <transition name=\"p-connected-overlay\" @enter=\"onOverlayEnter\" @after-enter=\"onOverlayAfterEnter\" @leave=\"onOverlayLeave\" @after-leave=\"onOverlayAfterLeave\" v-bind=\"ptm('transition')\">\n                <div\n                    v-if=\"overlayVisible\"\n                    :ref=\"overlayRef\"\n                    :id=\"panelId\"\n                    :class=\"[cx('overlay'), panelClass, overlayClass]\"\n                    :style=\"{ ...panelStyle, ...overlayStyle }\"\n                    @click=\"onOverlayClick\"\n                    @keydown=\"onOverlayKeyDown\"\n                    :data-p=\"overlayDataP\"\n                    v-bind=\"ptm('overlay')\"\n                >\n                    <slot name=\"header\" :value=\"d_value\" :suggestions=\"visibleOptions\"></slot>\n                    <div :class=\"cx('listContainer')\" :style=\"{ 'max-height': virtualScrollerDisabled ? scrollHeight : '' }\" v-bind=\"ptm('listContainer')\">\n                        <VirtualScroller :ref=\"virtualScrollerRef\" v-bind=\"virtualScrollerOptions\" :style=\"{ height: scrollHeight }\" :items=\"visibleOptions\" :tabindex=\"-1\" :disabled=\"virtualScrollerDisabled\" :pt=\"ptm('virtualScroller')\">\n                            <template v-slot:content=\"{ styleClass, contentRef, items, getItemOptions, contentStyle, itemSize }\">\n                                <ul :ref=\"(el) => listRef(el, contentRef)\" :id=\"$id + '_list'\" :class=\"[cx('list'), styleClass]\" :style=\"contentStyle\" role=\"listbox\" :aria-label=\"listAriaLabel\" v-bind=\"ptm('list')\">\n                                    <template v-for=\"(option, i) of items\" :key=\"getOptionRenderKey(option, getOptionIndex(i, getItemOptions))\">\n                                        <li\n                                            v-if=\"isOptionGroup(option)\"\n                                            :id=\"$id + '_' + getOptionIndex(i, getItemOptions)\"\n                                            :style=\"{ height: itemSize ? itemSize + 'px' : undefined }\"\n                                            :class=\"cx('optionGroup')\"\n                                            role=\"option\"\n                                            v-bind=\"ptm('optionGroup')\"\n                                        >\n                                            <slot name=\"optiongroup\" :option=\"option.optionGroup\" :index=\"getOptionIndex(i, getItemOptions)\">{{ getOptionGroupLabel(option.optionGroup) }}</slot>\n                                        </li>\n                                        <li\n                                            v-else\n                                            :id=\"$id + '_' + getOptionIndex(i, getItemOptions)\"\n                                            v-ripple\n                                            :style=\"{ height: itemSize ? itemSize + 'px' : undefined }\"\n                                            :class=\"cx('option', { option, i, getItemOptions })\"\n                                            role=\"option\"\n                                            :aria-label=\"getOptionLabel(option)\"\n                                            :aria-selected=\"isSelected(option)\"\n                                            :aria-disabled=\"isOptionDisabled(option)\"\n                                            :aria-setsize=\"ariaSetSize\"\n                                            :aria-posinset=\"getAriaPosInset(getOptionIndex(i, getItemOptions))\"\n                                            @click=\"onOptionSelect($event, option)\"\n                                            @mousemove=\"onOptionMouseMove($event, getOptionIndex(i, getItemOptions))\"\n                                            :data-p-selected=\"isSelected(option)\"\n                                            :data-p-focused=\"focusedOptionIndex === getOptionIndex(i, getItemOptions)\"\n                                            :data-p-disabled=\"isOptionDisabled(option)\"\n                                            v-bind=\"getPTOptions(option, getItemOptions, i, 'option')\"\n                                        >\n                                            <slot name=\"option\" :option=\"option\" :index=\"getOptionIndex(i, getItemOptions)\">{{ getOptionLabel(option) }}</slot>\n                                        </li>\n                                    </template>\n                                    <li v-if=\"showEmptyMessage && (!items || (items && items.length === 0))\" :class=\"cx('emptyMessage')\" role=\"option\" v-bind=\"ptm('emptyMessage')\">\n                                        <slot name=\"empty\">{{ searchResultMessageText }}</slot>\n                                    </li>\n                                </ul>\n                            </template>\n                            <template v-if=\"$slots.loader\" v-slot:loader=\"{ options }\">\n                                <slot name=\"loader\" :options=\"options\"></slot>\n                            </template>\n                        </VirtualScroller>\n                    </div>\n                    <slot name=\"footer\" :value=\"d_value\" :suggestions=\"visibleOptions\"></slot>\n                    <span role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\" v-bind=\"ptm('hiddenSelectedMessage')\" :data-p-hidden-accessible=\"true\">\n                        {{ selectedMessageText }}\n                    </span>\n                </div>\n            </transition>\n        </Portal>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { absolutePosition, addStyle, findSingle, focus, getOuterWidth, isTouchDevice, relativePosition } from '@primeuix/utils/dom';\nimport { equals, findLastIndex, isEmpty, isNotEmpty, resolveFieldData } from '@primeuix/utils/object';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { ConnectedOverlayScrollHandler } from '@primevue/core/utils';\nimport ChevronDownIcon from '@primevue/icons/chevrondown';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport Chip from 'primevue/chip';\nimport InputText from 'primevue/inputtext';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport Ripple from 'primevue/ripple';\nimport VirtualScroller from 'primevue/virtualscroller';\nimport BaseAutoComplete from './BaseAutoComplete.vue';\n\nexport default {\n    name: 'AutoComplete',\n    extends: BaseAutoComplete,\n    inheritAttrs: false,\n    emits: ['change', 'focus', 'blur', 'item-select', 'item-unselect', 'option-select', 'option-unselect', 'dropdown-click', 'clear', 'complete', 'before-show', 'before-hide', 'show', 'hide'],\n    inject: {\n        $pcFluid: { default: null }\n    },\n    outsideClickListener: null,\n    resizeListener: null,\n    scrollHandler: null,\n    overlay: null,\n    virtualScroller: null,\n    searchTimeout: null,\n    dirty: false,\n    startRangeIndex: -1,\n    data() {\n        return {\n            clicked: false,\n            focused: false,\n            focusedOptionIndex: -1,\n            focusedMultipleOptionIndex: -1,\n            overlayVisible: false,\n            searching: false\n        };\n    },\n    watch: {\n        suggestions() {\n            if (this.searching) {\n                this.show();\n                this.focusedOptionIndex = this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n                this.searching = false;\n                !this.showEmptyMessage && this.visibleOptions.length === 0 && this.hide();\n            }\n\n            this.autoUpdateModel();\n        }\n    },\n    mounted() {\n        this.autoUpdateModel();\n    },\n    updated() {\n        if (this.overlayVisible) {\n            this.alignOverlay();\n        }\n    },\n    beforeUnmount() {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        if (this.overlay) {\n            ZIndex.clear(this.overlay);\n            this.overlay = null;\n        }\n    },\n    methods: {\n        getOptionIndex(index, fn) {\n            return this.virtualScrollerDisabled ? index : fn && fn(index)['index'];\n        },\n        getOptionLabel(option) {\n            return this.optionLabel ? resolveFieldData(option, this.optionLabel) : option;\n        },\n        getOptionValue(option) {\n            return option; // TODO: The 'optionValue' properties can be added.\n        },\n        getOptionRenderKey(option, index) {\n            return (this.dataKey ? resolveFieldData(option, this.dataKey) : this.getOptionLabel(option)) + '_' + index;\n        },\n        getPTOptions(option, itemOptions, index, key) {\n            return this.ptm(key, {\n                context: {\n                    option,\n                    index,\n                    selected: this.isSelected(option),\n                    focused: this.focusedOptionIndex === this.getOptionIndex(index, itemOptions),\n                    disabled: this.isOptionDisabled(option)\n                }\n            });\n        },\n        isOptionDisabled(option) {\n            return this.optionDisabled ? resolveFieldData(option, this.optionDisabled) : false;\n        },\n        isOptionGroup(option) {\n            return this.optionGroupLabel && option.optionGroup && option.group;\n        },\n        getOptionGroupLabel(optionGroup) {\n            return resolveFieldData(optionGroup, this.optionGroupLabel);\n        },\n        getOptionGroupChildren(optionGroup) {\n            return resolveFieldData(optionGroup, this.optionGroupChildren);\n        },\n        getAriaPosInset(index) {\n            return (this.optionGroupLabel ? index - this.visibleOptions.slice(0, index).filter((option) => this.isOptionGroup(option)).length : index) + 1;\n        },\n        show(isFocus) {\n            this.$emit('before-show');\n            this.dirty = true;\n            this.overlayVisible = true;\n            this.focusedOptionIndex = this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n            isFocus && focus(this.multiple ? this.$refs.focusInput : this.$refs.focusInput.$el);\n        },\n        hide(isFocus) {\n            const _hide = () => {\n                this.$emit('before-hide');\n                this.dirty = isFocus;\n                this.overlayVisible = false;\n                this.clicked = false;\n                this.focusedOptionIndex = -1;\n\n                isFocus && focus(this.multiple ? this.$refs.focusInput : this.$refs.focusInput?.$el);\n            };\n\n            setTimeout(() => {\n                _hide();\n            }, 0); // For ScreenReaders\n        },\n        onFocus(event) {\n            if (this.disabled) {\n                // For ScreenReaders\n                return;\n            }\n\n            if (!this.dirty && this.completeOnFocus) {\n                this.search(event, event.target.value, 'focus');\n            }\n\n            this.dirty = true;\n            this.focused = true;\n\n            if (this.overlayVisible) {\n                this.focusedOptionIndex = this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n                this.scrollInView(this.focusedOptionIndex);\n            }\n\n            this.$emit('focus', event);\n        },\n        onBlur(event) {\n            this.dirty = false;\n            this.focused = false;\n            this.focusedOptionIndex = -1;\n            this.$emit('blur', event);\n            this.formField.onBlur?.();\n        },\n        onKeyDown(event) {\n            if (this.disabled) {\n                event.preventDefault();\n\n                return;\n            }\n\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeftKey(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRightKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'PageDown':\n                    this.onPageDownKey(event);\n                    break;\n\n                case 'PageUp':\n                    this.onPageUpKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event);\n                    break;\n\n                case 'ShiftLeft':\n                case 'ShiftRight':\n                    this.onShiftKey(event);\n                    break;\n\n                case 'Backspace':\n                    this.onBackspaceKey(event);\n                    break;\n\n                default:\n                    break;\n            }\n\n            this.clicked = false;\n        },\n        onInput(event) {\n            if (this.typeahead) {\n                if (this.searchTimeout) {\n                    clearTimeout(this.searchTimeout);\n                }\n\n                let query = event.target.value;\n\n                if (!this.multiple) {\n                    this.updateModel(event, query);\n                }\n\n                if (query.length === 0) {\n                    this.hide();\n                    this.$emit('clear');\n                } else {\n                    if (query.length >= this.minLength) {\n                        this.focusedOptionIndex = -1;\n\n                        this.searchTimeout = setTimeout(() => {\n                            this.search(event, query, 'input');\n                        }, this.delay);\n                    } else {\n                        this.hide();\n                    }\n                }\n            }\n        },\n        onChange(event) {\n            if (this.forceSelection) {\n                let valid = false;\n\n                // when forceSelection is on, prevent called twice onOptionSelect()\n                if (this.visibleOptions && !this.multiple) {\n                    let value = this.multiple ? this.$refs.focusInput.value : this.$refs.focusInput?.$el?.value;\n                    const matchedValue = this.visibleOptions.find((option) => this.isOptionMatched(option, value || ''));\n\n                    if (matchedValue !== undefined) {\n                        valid = true;\n                        !this.isSelected(matchedValue) && this.onOptionSelect(event, matchedValue);\n                    }\n                }\n\n                if (!valid) {\n                    if (this.multiple) {\n                        this.$refs.focusInput.value = '';\n                    } else {\n                        const inputEl = this.$refs.focusInput?.$el;\n                        inputEl && (inputEl.value = '');\n                    }\n                    this.$emit('clear');\n                    !this.multiple && this.updateModel(event, null);\n                }\n            }\n        },\n        onMultipleContainerFocus() {\n            if (this.disabled) {\n                // For ScreenReaders\n                return;\n            }\n\n            this.focused = true;\n        },\n        onMultipleContainerBlur() {\n            this.focusedMultipleOptionIndex = -1;\n            this.focused = false;\n        },\n        onMultipleContainerKeyDown(event) {\n            if (this.disabled) {\n                event.preventDefault();\n\n                return;\n            }\n\n            switch (event.code) {\n                case 'ArrowLeft':\n                    this.onArrowLeftKeyOnMultiple(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRightKeyOnMultiple(event);\n                    break;\n\n                case 'Backspace':\n                    this.onBackspaceKeyOnMultiple(event);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onContainerClick(event) {\n            this.clicked = true;\n\n            if (this.disabled || this.searching || this.loading || this.isDropdownClicked(event)) {\n                return;\n            }\n\n            if (!this.overlay || !this.overlay.contains(event.target)) {\n                focus(this.multiple ? this.$refs.focusInput : this.$refs.focusInput.$el);\n            }\n        },\n        onDropdownClick(event) {\n            let query = undefined;\n\n            if (this.overlayVisible) {\n                this.hide(true);\n            } else {\n                let target = this.multiple ? this.$refs.focusInput : this.$refs.focusInput.$el;\n\n                focus(target);\n                query = target.value;\n\n                if (this.dropdownMode === 'blank') this.search(event, '', 'dropdown');\n                else if (this.dropdownMode === 'current') this.search(event, query, 'dropdown');\n            }\n\n            this.$emit('dropdown-click', { originalEvent: event, query });\n        },\n        onOptionSelect(event, option, isHide = true) {\n            const value = this.getOptionValue(option);\n\n            if (this.multiple) {\n                this.$refs.focusInput.value = '';\n\n                if (!this.isSelected(option)) {\n                    this.updateModel(event, [...(this.d_value || []), value]);\n                }\n            } else {\n                this.updateModel(event, value);\n            }\n\n            this.$emit('item-select', { originalEvent: event, value: option });\n            this.$emit('option-select', { originalEvent: event, value: option });\n\n            isHide && this.hide(true);\n        },\n        onOptionMouseMove(event, index) {\n            if (this.focusOnHover) {\n                this.changeFocusedOptionIndex(event, index);\n            }\n        },\n        onOptionSelectRange(event, start = -1, end = -1) {\n            start === -1 && (start = this.findNearestSelectedOptionIndex(end, true));\n            end === -1 && (end = this.findNearestSelectedOptionIndex(start));\n\n            if (start !== -1 && end !== -1) {\n                const rangeStart = Math.min(start, end);\n                const rangeEnd = Math.max(start, end);\n                const value = this.visibleOptions\n                    .slice(rangeStart, rangeEnd + 1)\n                    .filter((option) => this.isValidOption(option))\n                    .map((option) => this.getOptionValue(option));\n\n                this.updateModel(event, value);\n            }\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.$el\n            });\n        },\n        onOverlayKeyDown(event) {\n            switch (event.code) {\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDownKey(event) {\n            if (!this.overlayVisible) {\n                return;\n            }\n\n            const optionIndex = this.focusedOptionIndex !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex) : this.clicked ? this.findFirstOptionIndex() : this.findFirstFocusedOptionIndex();\n\n            if (this.multiple && event.shiftKey) {\n                this.onOptionSelectRange(event, this.startRangeIndex, optionIndex);\n            }\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n\n            event.preventDefault();\n        },\n        onArrowUpKey(event) {\n            if (!this.overlayVisible) {\n                return;\n            }\n\n            if (event.altKey) {\n                if (this.focusedOptionIndex !== -1) {\n                    this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n                }\n\n                this.overlayVisible && this.hide();\n                event.preventDefault();\n            } else {\n                const optionIndex = this.focusedOptionIndex !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex) : this.clicked ? this.findLastOptionIndex() : this.findLastFocusedOptionIndex();\n\n                if (this.multiple && event.shiftKey) {\n                    this.onOptionSelectRange(event, optionIndex, this.startRangeIndex);\n                }\n\n                this.changeFocusedOptionIndex(event, optionIndex);\n\n                event.preventDefault();\n            }\n        },\n        onArrowLeftKey(event) {\n            const target = event.currentTarget;\n\n            this.focusedOptionIndex = -1;\n\n            if (this.multiple) {\n                if (isEmpty(target.value) && this.$filled) {\n                    focus(this.$refs.multiContainer);\n                    this.focusedMultipleOptionIndex = this.d_value.length;\n                } else {\n                    event.stopPropagation(); // To prevent onArrowLeftKeyOnMultiple method\n                }\n            }\n        },\n        onArrowRightKey(event) {\n            this.focusedOptionIndex = -1;\n\n            this.multiple && event.stopPropagation(); // To prevent onArrowRightKeyOnMultiple method\n        },\n        onHomeKey(event) {\n            const { currentTarget } = event;\n            const len = currentTarget.value.length;\n            const metaKey = event.metaKey || event.ctrlKey;\n            const optionIndex = this.findFirstOptionIndex();\n\n            if (this.multiple && event.shiftKey && metaKey) {\n                this.onOptionSelectRange(event, optionIndex, this.startRangeIndex);\n            }\n\n            currentTarget.setSelectionRange(0, event.shiftKey ? len : 0);\n            this.focusedOptionIndex = -1;\n\n            event.preventDefault();\n        },\n        onEndKey(event) {\n            const { currentTarget } = event;\n            const len = currentTarget.value.length;\n            const metaKey = event.metaKey || event.ctrlKey;\n            const optionIndex = this.findLastOptionIndex();\n\n            if (this.multiple && event.shiftKey && metaKey) {\n                this.onOptionSelectRange(event, this.startRangeIndex, optionIndex);\n            }\n\n            currentTarget.setSelectionRange(event.shiftKey ? 0 : len, len);\n            this.focusedOptionIndex = -1;\n\n            event.preventDefault();\n        },\n        onPageUpKey(event) {\n            this.scrollInView(0);\n            event.preventDefault();\n        },\n        onPageDownKey(event) {\n            this.scrollInView(this.visibleOptions.length - 1);\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            if (!this.typeahead) {\n                if (this.multiple) {\n                    if (event.target.value.trim()) {\n                        this.updateModel(event, [...(this.d_value || []), event.target.value.trim()]);\n                        this.$refs.focusInput.value = '';\n                    }\n                }\n            } else {\n                if (!this.overlayVisible) {\n                    this.focusedOptionIndex = -1; // reset\n                    this.onArrowDownKey(event);\n                } else {\n                    if (this.focusedOptionIndex !== -1) {\n                        if (this.multiple && event.shiftKey) {\n                            this.onOptionSelectRange(event, this.focusedOptionIndex);\n                            event.preventDefault();\n                        } else {\n                            this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n                        }\n                    }\n\n                    this.hide();\n                }\n            }\n        },\n        onSpaceKey(event) {\n            if (this.focusedOptionIndex !== -1) {\n                this.onEnterKey(event);\n            }\n        },\n        onEscapeKey(event) {\n            this.overlayVisible && this.hide(true);\n            event.preventDefault();\n        },\n        onTabKey(event) {\n            if (this.focusedOptionIndex !== -1) {\n                this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n            }\n\n            this.overlayVisible && this.hide();\n        },\n        onShiftKey() {\n            this.startRangeIndex = this.focusedOptionIndex;\n        },\n        onBackspaceKey(event) {\n            if (this.multiple) {\n                if (isNotEmpty(this.d_value) && !this.$refs.focusInput.value) {\n                    const removedValue = this.d_value[this.d_value.length - 1];\n                    const newValue = this.d_value.slice(0, -1);\n\n                    this.writeValue(newValue, event);\n                    this.$emit('item-unselect', { originalEvent: event, value: removedValue });\n                    this.$emit('option-unselect', { originalEvent: event, value: removedValue });\n                }\n\n                event.stopPropagation(); // To prevent onBackspaceKeyOnMultiple method\n            }\n        },\n        onArrowLeftKeyOnMultiple() {\n            this.focusedMultipleOptionIndex = this.focusedMultipleOptionIndex < 1 ? 0 : this.focusedMultipleOptionIndex - 1;\n        },\n        onArrowRightKeyOnMultiple() {\n            this.focusedMultipleOptionIndex++;\n\n            if (this.focusedMultipleOptionIndex > this.d_value.length - 1) {\n                this.focusedMultipleOptionIndex = -1;\n                focus(this.$refs.focusInput);\n            }\n        },\n        onBackspaceKeyOnMultiple(event) {\n            if (this.focusedMultipleOptionIndex !== -1) {\n                this.removeOption(event, this.focusedMultipleOptionIndex);\n            }\n        },\n        onOverlayEnter(el) {\n            ZIndex.set('overlay', el, this.$primevue.config.zIndex.overlay);\n\n            addStyle(el, { position: 'absolute', top: '0' });\n            this.alignOverlay();\n\n            // Issue: #7508\n            this.$attrSelector && el.setAttribute(this.$attrSelector, '');\n        },\n        onOverlayAfterEnter() {\n            this.bindOutsideClickListener();\n            this.bindScrollListener();\n            this.bindResizeListener();\n\n            this.$emit('show');\n        },\n        onOverlayLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindScrollListener();\n            this.unbindResizeListener();\n\n            this.$emit('hide');\n            this.overlay = null;\n        },\n        onOverlayAfterLeave(el) {\n            ZIndex.clear(el);\n        },\n        alignOverlay() {\n            let target = this.multiple ? this.$refs.multiContainer : this.$refs.focusInput.$el;\n\n            if (this.appendTo === 'self') {\n                relativePosition(this.overlay, target);\n            } else {\n                this.overlay.style.minWidth = getOuterWidth(target) + 'px';\n                absolutePosition(this.overlay, target);\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = (event) => {\n                    if (this.overlayVisible && this.overlay && this.isOutsideClicked(event)) {\n                        this.hide();\n                    }\n                };\n\n                document.addEventListener('click', this.outsideClickListener, true);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener, true);\n                this.outsideClickListener = null;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.$refs.container, () => {\n                    if (this.overlayVisible) {\n                        this.hide();\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = () => {\n                    if (this.overlayVisible && !isTouchDevice()) {\n                        this.hide();\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        isOutsideClicked(event) {\n            return !this.overlay.contains(event.target) && !this.isInputClicked(event) && !this.isDropdownClicked(event);\n        },\n        isInputClicked(event) {\n            if (this.multiple) return event.target === this.$refs.multiContainer || this.$refs.multiContainer.contains(event.target);\n            else return event.target === this.$refs.focusInput.$el;\n        },\n        isDropdownClicked(event) {\n            return this.$refs.dropdownButton ? event.target === this.$refs.dropdownButton || this.$refs.dropdownButton.contains(event.target) : false;\n        },\n        isOptionMatched(option, value) {\n            return this.isValidOption(option) && this.getOptionLabel(option)?.toLocaleLowerCase(this.searchLocale) === value.toLocaleLowerCase(this.searchLocale);\n        },\n        isValidOption(option) {\n            return isNotEmpty(option) && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n        },\n        isValidSelectedOption(option) {\n            return this.isValidOption(option) && this.isSelected(option);\n        },\n        isEquals(value1, value2) {\n            return equals(value1, value2, this.equalityKey);\n        },\n        isSelected(option) {\n            const optionValue = this.getOptionValue(option);\n\n            return this.multiple ? (this.d_value || []).some((value) => this.isEquals(value, optionValue)) : this.isEquals(this.d_value, this.getOptionValue(option));\n        },\n        findFirstOptionIndex() {\n            return this.visibleOptions.findIndex((option) => this.isValidOption(option));\n        },\n        findLastOptionIndex() {\n            return findLastIndex(this.visibleOptions, (option) => this.isValidOption(option));\n        },\n        findNextOptionIndex(index) {\n            const matchedOptionIndex = index < this.visibleOptions.length - 1 ? this.visibleOptions.slice(index + 1).findIndex((option) => this.isValidOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n        },\n        findPrevOptionIndex(index) {\n            const matchedOptionIndex = index > 0 ? findLastIndex(this.visibleOptions.slice(0, index), (option) => this.isValidOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n        },\n        findSelectedOptionIndex() {\n            return this.$filled ? this.visibleOptions.findIndex((option) => this.isValidSelectedOption(option)) : -1;\n        },\n        findFirstFocusedOptionIndex() {\n            const selectedIndex = this.findSelectedOptionIndex();\n\n            return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n        },\n        findLastFocusedOptionIndex() {\n            const selectedIndex = this.findSelectedOptionIndex();\n\n            return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n        },\n        search(event, query, source) {\n            //allow empty string but not undefined or null\n            if (query === undefined || query === null) {\n                return;\n            }\n\n            //do not search blank values on input change\n            if (source === 'input' && query.trim().length === 0) {\n                return;\n            }\n\n            this.searching = true;\n            this.$emit('complete', { originalEvent: event, query });\n        },\n        removeOption(event, index) {\n            const removedOption = this.d_value[index];\n            const value = this.d_value.filter((_, i) => i !== index).map((option) => this.getOptionValue(option));\n\n            this.updateModel(event, value);\n            this.$emit('item-unselect', { originalEvent: event, value: removedOption });\n            this.$emit('option-unselect', { originalEvent: event, value: removedOption });\n            this.dirty = true;\n            focus(this.multiple ? this.$refs.focusInput : this.$refs.focusInput.$el);\n        },\n        changeFocusedOptionIndex(event, index) {\n            if (this.focusedOptionIndex !== index) {\n                this.focusedOptionIndex = index;\n                this.scrollInView();\n\n                if (this.selectOnFocus) {\n                    this.onOptionSelect(event, this.visibleOptions[index], false);\n                }\n            }\n        },\n        scrollInView(index = -1) {\n            this.$nextTick(() => {\n                const id = index !== -1 ? `${this.$id}_${index}` : this.focusedOptionId;\n                const element = findSingle(this.list, `li[id=\"${id}\"]`);\n\n                if (element) {\n                    element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'start' });\n                } else if (!this.virtualScrollerDisabled) {\n                    this.virtualScroller && this.virtualScroller.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex);\n                }\n            });\n        },\n        autoUpdateModel() {\n            if (this.selectOnFocus && this.autoOptionFocus && !this.$filled) {\n                this.focusedOptionIndex = this.findFirstFocusedOptionIndex();\n                this.onOptionSelect(null, this.visibleOptions[this.focusedOptionIndex], false);\n            }\n        },\n        updateModel(event, value) {\n            this.writeValue(value, event);\n            this.$emit('change', { originalEvent: event, value });\n        },\n        flatOptions(options) {\n            return (options || []).reduce((result, option, index) => {\n                result.push({ optionGroup: option, group: true, index });\n\n                const optionGroupChildren = this.getOptionGroupChildren(option);\n\n                optionGroupChildren && optionGroupChildren.forEach((o) => result.push(o));\n\n                return result;\n            }, []);\n        },\n        overlayRef(el) {\n            this.overlay = el;\n        },\n        listRef(el, contentRef) {\n            this.list = el;\n            contentRef && contentRef(el); // For VirtualScroller\n        },\n        virtualScrollerRef(el) {\n            this.virtualScroller = el;\n        },\n        findNextSelectedOptionIndex(index) {\n            const matchedOptionIndex = this.$filled && index < this.visibleOptions.length - 1 ? this.visibleOptions.slice(index + 1).findIndex((option) => this.isValidSelectedOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : -1;\n        },\n        findPrevSelectedOptionIndex(index) {\n            const matchedOptionIndex = this.$filled && index > 0 ? findLastIndex(this.visibleOptions.slice(0, index), (option) => this.isValidSelectedOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex : -1;\n        },\n        findNearestSelectedOptionIndex(index, firstCheckUp = false) {\n            let matchedOptionIndex = -1;\n\n            if (this.$filled) {\n                if (firstCheckUp) {\n                    matchedOptionIndex = this.findPrevSelectedOptionIndex(index);\n                    matchedOptionIndex = matchedOptionIndex === -1 ? this.findNextSelectedOptionIndex(index) : matchedOptionIndex;\n                } else {\n                    matchedOptionIndex = this.findNextSelectedOptionIndex(index);\n                    matchedOptionIndex = matchedOptionIndex === -1 ? this.findPrevSelectedOptionIndex(index) : matchedOptionIndex;\n                }\n            }\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n        }\n    },\n    computed: {\n        visibleOptions() {\n            return this.optionGroupLabel ? this.flatOptions(this.suggestions) : this.suggestions || [];\n        },\n        inputValue() {\n            if (this.$filled) {\n                if (typeof this.d_value === 'object') {\n                    const label = this.getOptionLabel(this.d_value);\n\n                    return label != null ? label : this.d_value;\n                } else {\n                    return this.d_value;\n                }\n            } else {\n                return '';\n            }\n        },\n        // @deprecated use $filled instead.\n        hasSelectedOption() {\n            return this.$filled;\n        },\n        equalityKey() {\n            // @todo: The 'optionValue' properties can be added.\n            return this.dataKey;\n        },\n        searchResultMessageText() {\n            return isNotEmpty(this.visibleOptions) && this.overlayVisible ? this.searchMessageText.replaceAll('{0}', this.visibleOptions.length) : this.emptySearchMessageText;\n        },\n        searchMessageText() {\n            return this.searchMessage || this.$primevue.config.locale.searchMessage || '';\n        },\n        emptySearchMessageText() {\n            return this.emptySearchMessage || this.$primevue.config.locale.emptySearchMessage || '';\n        },\n        selectionMessageText() {\n            return this.selectionMessage || this.$primevue.config.locale.selectionMessage || '';\n        },\n        emptySelectionMessageText() {\n            return this.emptySelectionMessage || this.$primevue.config.locale.emptySelectionMessage || '';\n        },\n        selectedMessageText() {\n            return this.$filled ? this.selectionMessageText.replaceAll('{0}', this.multiple ? this.d_value.length : '1') : this.emptySelectionMessageText;\n        },\n        listAriaLabel() {\n            return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.listLabel : undefined;\n        },\n        focusedOptionId() {\n            return this.focusedOptionIndex !== -1 ? `${this.$id}_${this.focusedOptionIndex}` : null;\n        },\n        focusedMultipleOptionId() {\n            return this.focusedMultipleOptionIndex !== -1 ? `${this.$id}_multiple_option_${this.focusedMultipleOptionIndex}` : null;\n        },\n        ariaSetSize() {\n            return this.visibleOptions.filter((option) => !this.isOptionGroup(option)).length;\n        },\n        virtualScrollerDisabled() {\n            return !this.virtualScrollerOptions;\n        },\n        panelId() {\n            return this.$id + '_panel';\n        },\n        containerDataP() {\n            return cn({\n                fluid: this.$fluid\n            });\n        },\n        overlayDataP() {\n            return cn({\n                ['portal-' + this.appendTo]: 'portal-' + this.appendTo\n            });\n        },\n        inputMultipleDataP() {\n            return cn({\n                invalid: this.$invalid,\n                disabled: this.disabled,\n                focus: this.focused,\n                fluid: this.$fluid,\n                filled: this.$variant === 'filled',\n                empty: !this.$filled,\n                [this.size]: this.size\n            });\n        }\n    },\n    components: {\n        InputText,\n        VirtualScroller,\n        Portal,\n        ChevronDownIcon,\n        SpinnerIcon,\n        Chip\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n"], "names": ["name", "BaseInput", "props", "suggestions", "type", "Array", "optionLabel", "optionDisabled", "optionGroupLabel", "optionGroupChildren", "scrollHeight", "String", "dropdown", "Boolean", "dropdownMode", "multiple", "loading", "placeholder", "dataKey", "<PERSON><PERSON><PERSON><PERSON>", "Number", "delay", "appendTo", "Object", "forceSelection", "completeOnFocus", "inputId", "inputStyle", "inputClass", "panelStyle", "panelClass", "overlayStyle", "overlayClass", "dropdownIcon", "dropdownClass", "loader", "loadingIcon", "removeTokenIcon", "chipIcon", "virtualScrollerOptions", "autoOptionFocus", "selectOnFocus", "focusOnHover", "searchLocale", "undefined", "searchMessage", "selectionMessage", "emptySelectionMessage", "emptySearchMessage", "showEmptyMessage", "tabindex", "typeahead", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "AutoCompleteStyle", "provide", "$pcAutoComplete", "$parentInstance", "BaseAutoComplete", "inheritAttrs", "emits", "inject", "$pcFluid", "outsideClickListener", "resizeListener", "<PERSON><PERSON><PERSON><PERSON>", "overlay", "virtualScroller", "searchTimeout", "dirty", "startRangeIndex", "data", "clicked", "focused", "focusedOptionIndex", "focusedMultipleOptionIndex", "overlayVisible", "searching", "watch", "show", "findFirstFocusedOptionIndex", "visibleOptions", "length", "hide", "autoUpdateModel", "mounted", "updated", "alignOverlay", "beforeUnmount", "unbindOutsideClickListener", "unbindResizeListener", "destroy", "ZIndex", "clear", "methods", "getOptionIndex", "index", "fn", "virtualScrollerDisabled", "getOptionLabel", "option", "resolveFieldData", "getOptionValue", "getOptionRenderKey", "getPTOptions", "itemOptions", "key", "ptm", "context", "selected", "isSelected", "disabled", "isOptionDisabled", "isOptionGroup", "optionGroup", "group", "getOptionGroupLabel", "getOptionGroupChildren", "getAriaPosInset", "_this", "slice", "filter", "isFocus", "$emit", "focus", "$refs", "focusInput", "$el", "_this2", "_hide", "_this2$$refs$focusInp", "setTimeout", "onFocus", "event", "search", "target", "value", "scrollInView", "onBlur", "_this$formField$onBlu", "_this$formField", "formField", "call", "onKeyDown", "preventDefault", "code", "onArrowDownKey", "onArrowUpKey", "onArrowLeftKey", "onArrowRightKey", "onHomeKey", "onEndKey", "onPageDownKey", "onPageUpKey", "onEnterKey", "onSpaceKey", "onEscapeKey", "onTabKey", "onShiftKey", "onBackspaceKey", "onInput", "_this3", "clearTimeout", "query", "updateModel", "onChange", "_this4", "valid", "_this$$refs$focusInpu", "matchedValue", "find", "isOptionMatched", "onOptionSelect", "_this$$refs$focusInpu2", "inputEl", "onMultipleContainerFocus", "onMultipleContainerBlur", "onMultipleContainerKeyDown", "onArrowLeftKeyOnMultiple", "onArrowRightKeyOnMultiple", "onBackspaceKeyOnMultiple", "onContainerClick", "isDropdownClicked", "contains", "onDropdownClick", "originalEvent", "isHide", "arguments", "concat", "_toConsumableArray", "d_value", "onOptionMouseMove", "changeFocusedOptionIndex", "onOptionSelectRange", "_this5", "start", "end", "findNearestSelectedOptionIndex", "rangeStart", "Math", "min", "rangeEnd", "max", "isValidOption", "map", "onOverlayClick", "OverlayEventBus", "emit", "onOverlayKeyDown", "optionIndex", "findNextOptionIndex", "findFirstOptionIndex", "shift<PERSON>ey", "altKey", "findPrevOptionIndex", "findLastOptionIndex", "findLastFocusedOptionIndex", "currentTarget", "isEmpty", "$filled", "multiContainer", "stopPropagation", "len", "metaKey", "ctrl<PERSON>ey", "setSelectionRange", "trim", "isNotEmpty", "removedValue", "newValue", "writeValue", "removeOption", "onOverlayEnter", "el", "set", "$primevue", "config", "zIndex", "addStyle", "position", "top", "$attrSelector", "setAttribute", "onOverlayAfterEnter", "bindOutsideClickListener", "bindScrollListener", "bindResizeListener", "onOverlayLeave", "unbindScrollListener", "onOverlayAfterLeave", "relativePosition", "min<PERSON><PERSON><PERSON>", "getOuterWidth", "absolutePosition", "_this6", "isOutsideClicked", "document", "addEventListener", "removeEventListener", "_this7", "ConnectedOverlayScrollHandler", "container", "_this8", "isTouchDevice", "window", "isInputClicked", "dropdownButton", "_this$getOptionLabel", "toLocaleLowerCase", "isValidSelectedOption", "isEquals", "value1", "value2", "equals", "equalityKey", "_this9", "optionValue", "some", "_this0", "findIndex", "_this1", "findLastIndex", "_this10", "matchedOptionIndex", "_this11", "findSelectedOptionIndex", "_this12", "selectedIndex", "source", "_this13", "removedOption", "_", "i", "_this14", "$nextTick", "id", "$id", "focusedOptionId", "element", "findSingle", "list", "scrollIntoView", "block", "inline", "scrollToIndex", "flatOptions", "options", "_this15", "reduce", "result", "push", "for<PERSON>ach", "o", "overlayRef", "listRef", "contentRef", "virtualScrollerRef", "findNextSelectedOptionIndex", "_this16", "findPrevSelectedOptionIndex", "_this17", "firstCheckUp", "computed", "inputValue", "_typeof", "label", "hasSelectedOption", "searchResultMessageText", "searchMessageText", "replaceAll", "emptySearchMessageText", "locale", "selectionMessageText", "emptySelectionMessageText", "selectedMessageText", "listAriaLabel", "aria", "listLabel", "focusedMultipleOptionId", "ariaSetSize", "_this18", "panelId", "containerDataP", "cn", "fluid", "$fluid", "overlayDataP", "_defineProperty", "inputMultipleDataP", "invalid", "$invalid", "filled", "$variant", "empty", "size", "components", "InputText", "VirtualScroller", "Portal", "ChevronDownIcon", "SpinnerIcon", "Chip", "directives", "ripple", "<PERSON><PERSON><PERSON>", "_openBlock", "_createElementBlock", "_mergeProps", "ref", "_ctx", "cx", "sx", "onClick", "$options", "apply", "ptmi", "_createBlock", "_component_InputText", "$formName", "_normalizeClass", "defaultValue", "variant", "autocomplete", "role", "$data", "onKeydown", "unstyled", "pt", "_Fragment", "_renderList", "_renderSlot", "removeCallback", "_createVNode", "_component_Chip", "removeIcon", "removable", "onRemove", "$event", "removeicon", "$slots", "chipicon", "_createElementVNode", "_hoisted_4", "_hoisted_5", "_component_SpinnerIcon", "spin", "toggleCallback", "_resolveDynamicComponent", "_component_Portal", "_Transition", "onEnter", "onAfterEnter", "onLeave", "onAfterLeave", "_objectSpread", "_component_VirtualScroller", "items", "content", "_withCtx", "_ref", "styleClass", "getItemOptions", "contentStyle", "itemSize", "height", "_withDirectives", "onMousemove", "_createTextVNode", "_toDisplayString", "_ref2"], "mappings": ";;;;;;;;;;;;;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,kBAAkB;AACxB,EAAA,SAAA,EAASC,SAAS;AAClBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,WAAW,EAAE;AACTC,MAAAA,IAAI,EAAEC,KAAK;MACX,SAAS,EAAA;KACZ;AACDC,IAAAA,WAAW,EAAE,IAAI;AACjBC,IAAAA,cAAc,EAAE,IAAI;AACpBC,IAAAA,gBAAgB,EAAE,IAAI;AACtBC,IAAAA,mBAAmB,EAAE,IAAI;AACzBC,IAAAA,YAAY,EAAE;AACVN,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNR,MAAAA,IAAI,EAAES,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,YAAY,EAAE;AACVV,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDI,IAAAA,QAAQ,EAAE;AACNX,MAAAA,IAAI,EAAES,OAAO;MACb,SAAS,EAAA;KACZ;AACDG,IAAAA,OAAO,EAAE;AACLZ,MAAAA,IAAI,EAAES,OAAO;MACb,SAAS,EAAA;KACZ;AACDI,IAAAA,WAAW,EAAE;AACTb,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDO,IAAAA,OAAO,EAAE;AACLd,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDQ,IAAAA,SAAS,EAAE;AACPf,MAAAA,IAAI,EAAEgB,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,KAAK,EAAE;AACHjB,MAAAA,IAAI,EAAEgB,MAAM;MACZ,SAAS,EAAA;KACZ;AACDE,IAAAA,QAAQ,EAAE;AACNlB,MAAAA,IAAI,EAAE,CAACO,MAAM,EAAEY,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDC,IAAAA,cAAc,EAAE;AACZpB,MAAAA,IAAI,EAAES,OAAO;MACb,SAAS,EAAA;KACZ;AACDY,IAAAA,eAAe,EAAE;AACbrB,MAAAA,IAAI,EAAES,OAAO;MACb,SAAS,EAAA;KACZ;AACDa,IAAAA,OAAO,EAAE;AACLtB,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDgB,IAAAA,UAAU,EAAE;AACRvB,MAAAA,IAAI,EAAEmB,MAAM;MACZ,SAAS,EAAA;KACZ;AACDK,IAAAA,UAAU,EAAE;AACRxB,MAAAA,IAAI,EAAE,CAACO,MAAM,EAAEY,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDM,IAAAA,UAAU,EAAE;AACRzB,MAAAA,IAAI,EAAEmB,MAAM;MACZ,SAAS,EAAA;KACZ;AACDO,IAAAA,UAAU,EAAE;AACR1B,MAAAA,IAAI,EAAE,CAACO,MAAM,EAAEY,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDQ,IAAAA,YAAY,EAAE;AACV3B,MAAAA,IAAI,EAAEmB,MAAM;MACZ,SAAS,EAAA;KACZ;AACDS,IAAAA,YAAY,EAAE;AACV5B,MAAAA,IAAI,EAAE,CAACO,MAAM,EAAEY,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDU,IAAAA,YAAY,EAAE;AACV7B,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDuB,IAAAA,aAAa,EAAE;AACX9B,MAAAA,IAAI,EAAE,CAACO,MAAM,EAAEY,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDY,IAAAA,MAAM,EAAE;AACJ/B,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDyB,IAAAA,WAAW,EAAE;AACThC,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACD0B,IAAAA,eAAe,EAAE;AACbjC,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACD2B,IAAAA,QAAQ,EAAE;AACNlC,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACD4B,IAAAA,sBAAsB,EAAE;AACpBnC,MAAAA,IAAI,EAAEmB,MAAM;MACZ,SAAS,EAAA;KACZ;AACDiB,IAAAA,eAAe,EAAE;AACbpC,MAAAA,IAAI,EAAES,OAAO;MACb,SAAS,EAAA;KACZ;AACD4B,IAAAA,aAAa,EAAE;AACXrC,MAAAA,IAAI,EAAES,OAAO;MACb,SAAS,EAAA;KACZ;AACD6B,IAAAA,YAAY,EAAE;AACVtC,MAAAA,IAAI,EAAES,OAAO;MACb,SAAS,EAAA;KACZ;AACD8B,IAAAA,YAAY,EAAE;AACVvC,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAASiC,EAAAA;KACZ;AACDC,IAAAA,aAAa,EAAE;AACXzC,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDmC,IAAAA,gBAAgB,EAAE;AACd1C,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDoC,IAAAA,qBAAqB,EAAE;AACnB3C,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDqC,IAAAA,kBAAkB,EAAE;AAChB5C,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDsC,IAAAA,gBAAgB,EAAE;AACd7C,MAAAA,IAAI,EAAES,OAAO;MACb,SAAS,EAAA;KACZ;AACDqC,IAAAA,QAAQ,EAAE;AACN9C,MAAAA,IAAI,EAAEgB,MAAM;MACZ,SAAS,EAAA;KACZ;AACD+B,IAAAA,SAAS,EAAE;AACP/C,MAAAA,IAAI,EAAES,OAAO;MACb,SAAS,EAAA;KACZ;AACDuC,IAAAA,SAAS,EAAE;AACPhD,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACD0C,IAAAA,cAAc,EAAE;AACZjD,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACD2C,EAAAA,KAAK,EAAEC,iBAAiB;EACxBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,eAAe,EAAE,IAAI;AACrBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;;;;;;;AC0CD,aAAe;AACX1D,EAAAA,IAAI,EAAE,cAAc;AACpB,EAAA,SAAA,EAAS2D,QAAgB;AACzBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC;AAC3LC,EAAAA,MAAM,EAAE;AACJC,IAAAA,QAAQ,EAAE;MAAE,SAAS,EAAA;AAAK;GAC7B;AACDC,EAAAA,oBAAoB,EAAE,IAAI;AAC1BC,EAAAA,cAAc,EAAE,IAAI;AACpBC,EAAAA,aAAa,EAAE,IAAI;AACnBC,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,eAAe,EAAE,IAAI;AACrBC,EAAAA,aAAa,EAAE,IAAI;AACnBC,EAAAA,KAAK,EAAE,KAAK;EACZC,eAAe,EAAE,EAAE;EACnBC,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACHC,MAAAA,OAAO,EAAE,KAAK;AACdC,MAAAA,OAAO,EAAE,KAAK;MACdC,kBAAkB,EAAE,EAAE;MACtBC,0BAA0B,EAAE,EAAE;AAC9BC,MAAAA,cAAc,EAAE,KAAK;AACrBC,MAAAA,SAAS,EAAE;KACd;GACJ;AACDC,EAAAA,KAAK,EAAE;IACH5E,WAAW,EAAA,SAAXA,WAAWA,GAAG;MACV,IAAI,IAAI,CAAC2E,SAAS,EAAE;QAChB,IAAI,CAACE,IAAI,EAAE;AACX,QAAA,IAAI,CAACL,kBAAiB,GAAI,IAAI,CAACE,cAAe,IAAG,IAAI,CAACrC,kBAAkB,IAAI,CAACyC,2BAA2B,EAAC,GAAI,EAAE;QAC/G,IAAI,CAACH,SAAU,GAAE,KAAK;AACtB,QAAA,CAAC,IAAI,CAAC7B,gBAAe,IAAK,IAAI,CAACiC,cAAc,CAACC,MAAK,KAAM,CAAA,IAAK,IAAI,CAACC,IAAI,EAAE;AAC7E;MAEA,IAAI,CAACC,eAAe,EAAE;AAC1B;GACH;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,CAACD,eAAe,EAAE;GACzB;EACDE,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,IAAI,CAACV,cAAc,EAAE;MACrB,IAAI,CAACW,YAAY,EAAE;AACvB;GACH;EACDC,aAAa,EAAA,SAAbA,aAAaA,GAAG;IACZ,IAAI,CAACC,0BAA0B,EAAE;IACjC,IAAI,CAACC,oBAAoB,EAAE;IAE3B,IAAI,IAAI,CAACzB,aAAa,EAAE;AACpB,MAAA,IAAI,CAACA,aAAa,CAAC0B,OAAO,EAAE;MAC5B,IAAI,CAAC1B,gBAAgB,IAAI;AAC7B;IAEA,IAAI,IAAI,CAACC,OAAO,EAAE;AACd0B,MAAAA,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC3B,OAAO,CAAC;MAC1B,IAAI,CAACA,OAAQ,GAAE,IAAI;AACvB;GACH;AACD4B,EAAAA,OAAO,EAAE;AACLC,IAAAA,cAAc,WAAdA,cAAcA,CAACC,KAAK,EAAEC,EAAE,EAAE;AACtB,MAAA,OAAO,IAAI,CAACC,uBAAwB,GAAEF,KAAM,GAAEC,EAAC,IAAKA,EAAE,CAACD,KAAK,CAAC,CAAC,OAAO,CAAC;KACzE;AACDG,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACC,MAAM,EAAE;AACnB,MAAA,OAAO,IAAI,CAAC/F,WAAY,GAAEgG,gBAAgB,CAACD,MAAM,EAAE,IAAI,CAAC/F,WAAW,CAAA,GAAI+F,MAAM;KAChF;AACDE,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACF,MAAM,EAAE;MACnB,OAAOA,MAAM,CAAA;KAChB;AACDG,IAAAA,kBAAkB,WAAlBA,kBAAkBA,CAACH,MAAM,EAAEJ,KAAK,EAAE;MAC9B,OAAO,CAAC,IAAI,CAAC/E,OAAQ,GAAEoF,gBAAgB,CAACD,MAAM,EAAE,IAAI,CAACnF,OAAO,CAAA,GAAI,IAAI,CAACkF,cAAc,CAACC,MAAM,CAAC,IAAI,GAAE,GAAIJ,KAAK;KAC7G;IACDQ,YAAY,EAAA,SAAZA,YAAYA,CAACJ,MAAM,EAAEK,WAAW,EAAET,KAAK,EAAEU,GAAG,EAAE;AAC1C,MAAA,OAAO,IAAI,CAACC,GAAG,CAACD,GAAG,EAAE;AACjBE,QAAAA,OAAO,EAAE;AACLR,UAAAA,MAAM,EAANA,MAAM;AACNJ,UAAAA,KAAK,EAALA,KAAK;AACLa,UAAAA,QAAQ,EAAE,IAAI,CAACC,UAAU,CAACV,MAAM,CAAC;AACjC3B,UAAAA,OAAO,EAAE,IAAI,CAACC,kBAAiB,KAAM,IAAI,CAACqB,cAAc,CAACC,KAAK,EAAES,WAAW,CAAC;AAC5EM,UAAAA,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAACZ,MAAM;AAC1C;AACJ,OAAC,CAAC;KACL;AACDY,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAACZ,MAAM,EAAE;AACrB,MAAA,OAAO,IAAI,CAAC9F,cAAa,GAAI+F,gBAAgB,CAACD,MAAM,EAAE,IAAI,CAAC9F,cAAc,IAAI,KAAK;KACrF;AACD2G,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAACb,MAAM,EAAE;MAClB,OAAO,IAAI,CAAC7F,gBAAiB,IAAG6F,MAAM,CAACc,WAAY,IAAGd,MAAM,CAACe,KAAK;KACrE;AACDC,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAACF,WAAW,EAAE;AAC7B,MAAA,OAAOb,gBAAgB,CAACa,WAAW,EAAE,IAAI,CAAC3G,gBAAgB,CAAC;KAC9D;AACD8G,IAAAA,sBAAsB,EAAtBA,SAAAA,sBAAsBA,CAACH,WAAW,EAAE;AAChC,MAAA,OAAOb,gBAAgB,CAACa,WAAW,EAAE,IAAI,CAAC1G,mBAAmB,CAAC;KACjE;AACD8G,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAACtB,KAAK,EAAE;AAAA,MAAA,IAAAuB,KAAA,GAAA,IAAA;MACnB,OAAO,CAAC,IAAI,CAAChH,gBAAiB,GAAEyF,KAAI,GAAI,IAAI,CAACf,cAAc,CAACuC,KAAK,CAAC,CAAC,EAAExB,KAAK,CAAC,CAACyB,MAAM,CAAC,UAACrB,MAAM,EAAA;AAAA,QAAA,OAAKmB,KAAI,CAACN,aAAa,CAACb,MAAM,CAAC;AAAA,OAAA,CAAC,CAAClB,MAAO,GAAEc,KAAK,IAAI,CAAC;KACjJ;AACDjB,IAAAA,IAAI,EAAJA,SAAAA,IAAIA,CAAC2C,OAAO,EAAE;AACV,MAAA,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC;MACzB,IAAI,CAACtD,KAAM,GAAE,IAAI;MACjB,IAAI,CAACO,iBAAiB,IAAI;MAC1B,IAAI,CAACF,kBAAmB,GAAE,IAAI,CAACA,kBAAiB,KAAM,EAAC,GAAI,IAAI,CAACA,kBAAmB,GAAE,IAAI,CAACnC,eAAgB,GAAE,IAAI,CAACyC,2BAA2B,EAAG,GAAE,EAAE;MACnJ0C,OAAQ,IAAGE,KAAK,CAAC,IAAI,CAAC9G,QAAO,GAAI,IAAI,CAAC+G,KAAK,CAACC,UAAS,GAAI,IAAI,CAACD,KAAK,CAACC,UAAU,CAACC,GAAG,CAAC;KACtF;AACD5C,IAAAA,IAAI,EAAJA,SAAAA,IAAIA,CAACuC,OAAO,EAAE;AAAA,MAAA,IAAAM,MAAA,GAAA,IAAA;AACV,MAAA,IAAMC,KAAM,GAAE,SAARA,KAAMA,GAAQ;AAAA,QAAA,IAAAC,qBAAA;AAChBF,QAAAA,MAAI,CAACL,KAAK,CAAC,aAAa,CAAC;QACzBK,MAAI,CAAC3D,KAAM,GAAEqD,OAAO;QACpBM,MAAI,CAACpD,cAAa,GAAI,KAAK;QAC3BoD,MAAI,CAACxD,UAAU,KAAK;AACpBwD,QAAAA,MAAI,CAACtD,kBAAiB,GAAI,EAAE;QAE5BgD,WAAWE,KAAK,CAACI,MAAI,CAAClH,QAAS,GAAEkH,MAAI,CAACH,KAAK,CAACC,UAAW,GAAA,CAAAI,qBAAA,GAAEF,MAAI,CAACH,KAAK,CAACC,UAAU,MAAAI,IAAAA,IAAAA,qBAAA,KAArBA,MAAAA,GAAAA,MAAAA,GAAAA,qBAAA,CAAuBH,GAAG,CAAC;OACvF;AAEDI,MAAAA,UAAU,CAAC,YAAM;AACbF,QAAAA,KAAK,EAAE;AACX,OAAC,EAAE,CAAC,CAAC,CAAA;KACR;AACDG,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACC,KAAK,EAAE;MACX,IAAI,IAAI,CAACtB,QAAQ,EAAE;AACf;AACA,QAAA;AACJ;MAEA,IAAI,CAAC,IAAI,CAAC1C,KAAM,IAAG,IAAI,CAAC7C,eAAe,EAAE;AACrC,QAAA,IAAI,CAAC8G,MAAM,CAACD,KAAK,EAAEA,KAAK,CAACE,MAAM,CAACC,KAAK,EAAE,OAAO,CAAC;AACnD;MAEA,IAAI,CAACnE,KAAM,GAAE,IAAI;MACjB,IAAI,CAACI,OAAQ,GAAE,IAAI;MAEnB,IAAI,IAAI,CAACG,cAAc,EAAE;AACrB,QAAA,IAAI,CAACF,kBAAiB,GAAI,IAAI,CAACA,kBAAmB,KAAI,EAAG,GAAE,IAAI,CAACA,qBAAqB,IAAI,CAACE,cAAe,IAAG,IAAI,CAACrC,eAAgB,GAAE,IAAI,CAACyC,2BAA2B,EAAG,GAAE,EAAE;AAC1K,QAAA,IAAI,CAACyD,YAAY,CAAC,IAAI,CAAC/D,kBAAkB,CAAC;AAC9C;AAEA,MAAA,IAAI,CAACiD,KAAK,CAAC,OAAO,EAAEU,KAAK,CAAC;KAC7B;AACDK,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAACL,KAAK,EAAE;MAAA,IAAAM,qBAAA,EAAAC,eAAA;MACV,IAAI,CAACvE,KAAI,GAAI,KAAK;MAClB,IAAI,CAACI,UAAU,KAAK;AACpB,MAAA,IAAI,CAACC,kBAAiB,GAAI,EAAE;AAC5B,MAAA,IAAI,CAACiD,KAAK,CAAC,MAAM,EAAEU,KAAK,CAAC;AACzB,MAAA,CAAAM,qBAAA,GAAAC,CAAAA,eAAA,GAAI,IAAA,CAACC,SAAS,EAACH,MAAM,MAAAC,IAAAA,IAAAA,qBAAA,eAArBA,qBAAA,CAAAG,IAAA,CAAAF,eAAwB,CAAC;KAC5B;AACDG,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACV,KAAK,EAAE;MACb,IAAI,IAAI,CAACtB,QAAQ,EAAE;QACfsB,KAAK,CAACW,cAAc,EAAE;AAEtB,QAAA;AACJ;MAEA,QAAQX,KAAK,CAACY,IAAI;AACd,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACC,cAAc,CAACb,KAAK,CAAC;AAC1B,UAAA;AAEJ,QAAA,KAAK,SAAS;AACV,UAAA,IAAI,CAACc,YAAY,CAACd,KAAK,CAAC;AACxB,UAAA;AAEJ,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACe,cAAc,CAACf,KAAK,CAAC;AAC1B,UAAA;AAEJ,QAAA,KAAK,YAAY;AACb,UAAA,IAAI,CAACgB,eAAe,CAAChB,KAAK,CAAC;AAC3B,UAAA;AAEJ,QAAA,KAAK,MAAM;AACP,UAAA,IAAI,CAACiB,SAAS,CAACjB,KAAK,CAAC;AACrB,UAAA;AAEJ,QAAA,KAAK,KAAK;AACN,UAAA,IAAI,CAACkB,QAAQ,CAAClB,KAAK,CAAC;AACpB,UAAA;AAEJ,QAAA,KAAK,UAAU;AACX,UAAA,IAAI,CAACmB,aAAa,CAACnB,KAAK,CAAC;AACzB,UAAA;AAEJ,QAAA,KAAK,QAAQ;AACT,UAAA,IAAI,CAACoB,WAAW,CAACpB,KAAK,CAAC;AACvB,UAAA;AAEJ,QAAA,KAAK,OAAO;AACZ,QAAA,KAAK,aAAa;AACd,UAAA,IAAI,CAACqB,UAAU,CAACrB,KAAK,CAAC;AACtB,UAAA;AAEJ,QAAA,KAAK,OAAO;AACR,UAAA,IAAI,CAACsB,UAAU,CAACtB,KAAK,CAAC;AACtB,UAAA;AAEJ,QAAA,KAAK,QAAQ;AACT,UAAA,IAAI,CAACuB,WAAW,CAACvB,KAAK,CAAC;AACvB,UAAA;AAEJ,QAAA,KAAK,KAAK;AACN,UAAA,IAAI,CAACwB,QAAQ,CAACxB,KAAK,CAAC;AACpB,UAAA;AAEJ,QAAA,KAAK,WAAW;AAChB,QAAA,KAAK,YAAY;AACb,UAAA,IAAI,CAACyB,UAAU,CAACzB,KAAK,CAAC;AACtB,UAAA;AAEJ,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAAC0B,cAAc,CAAC1B,KAAK,CAAC;AAC1B,UAAA;AAIR;MAEA,IAAI,CAAC7D,UAAU,KAAK;KACvB;AACDwF,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAAC3B,KAAK,EAAE;AAAA,MAAA,IAAA4B,MAAA,GAAA,IAAA;MACX,IAAI,IAAI,CAAC/G,SAAS,EAAE;QAChB,IAAI,IAAI,CAACkB,aAAa,EAAE;AACpB8F,UAAAA,YAAY,CAAC,IAAI,CAAC9F,aAAa,CAAC;AACpC;AAEA,QAAA,IAAI+F,KAAM,GAAE9B,KAAK,CAACE,MAAM,CAACC,KAAK;AAE9B,QAAA,IAAI,CAAC,IAAI,CAAC1H,QAAQ,EAAE;AAChB,UAAA,IAAI,CAACsJ,WAAW,CAAC/B,KAAK,EAAE8B,KAAK,CAAC;AAClC;AAEA,QAAA,IAAIA,KAAK,CAACjF,MAAK,KAAM,CAAC,EAAE;UACpB,IAAI,CAACC,IAAI,EAAE;AACX,UAAA,IAAI,CAACwC,KAAK,CAAC,OAAO,CAAC;AACvB,SAAE,MAAK;AACH,UAAA,IAAIwC,KAAK,CAACjF,MAAO,IAAG,IAAI,CAAChE,SAAS,EAAE;AAChC,YAAA,IAAI,CAACwD,kBAAiB,GAAI,EAAE;AAE5B,YAAA,IAAI,CAACN,aAAc,GAAE+D,UAAU,CAAC,YAAM;cAClC8B,MAAI,CAAC3B,MAAM,CAACD,KAAK,EAAE8B,KAAK,EAAE,OAAO,CAAC;AACtC,aAAC,EAAE,IAAI,CAAC/I,KAAK,CAAC;AAClB,WAAE,MAAK;YACH,IAAI,CAAC+D,IAAI,EAAE;AACf;AACJ;AACJ;KACH;AACDkF,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAAChC,KAAK,EAAE;AAAA,MAAA,IAAAiC,MAAA,GAAA,IAAA;MACZ,IAAI,IAAI,CAAC/I,cAAc,EAAE;QACrB,IAAIgJ,KAAM,GAAE,KAAK;;AAEjB;QACA,IAAI,IAAI,CAACtF,kBAAkB,CAAC,IAAI,CAACnE,QAAQ,EAAE;AAAA,UAAA,IAAA0J,qBAAA;AACvC,UAAA,IAAIhC,KAAM,GAAE,IAAI,CAAC1H,QAAO,GAAI,IAAI,CAAC+G,KAAK,CAACC,UAAU,CAACU,iCAAQ,IAAI,CAACX,KAAK,CAACC,UAAU,MAAA0C,IAAAA,IAAAA,qBAAA,gBAAAA,qBAAA,GAArBA,qBAAA,CAAuBzC,GAAG,MAAAyC,IAAAA,IAAAA,qBAAA,KAA1BA,MAAAA,GAAAA,MAAAA,GAAAA,qBAAA,CAA4BhC,KAAK;UAC3F,IAAMiC,eAAe,IAAI,CAACxF,cAAc,CAACyF,IAAI,CAAC,UAACtE,MAAM,EAAA;YAAA,OAAKkE,MAAI,CAACK,eAAe,CAACvE,MAAM,EAAEoC,KAAM,IAAG,EAAE,CAAC;WAAC,CAAA;UAEpG,IAAIiC,iBAAiB9H,SAAS,EAAE;AAC5B4H,YAAAA,KAAI,GAAI,IAAI;AACZ,YAAA,CAAC,IAAI,CAACzD,UAAU,CAAC2D,YAAY,CAAA,IAAK,IAAI,CAACG,cAAc,CAACvC,KAAK,EAAEoC,YAAY,CAAC;AAC9E;AACJ;QAEA,IAAI,CAACF,KAAK,EAAE;UACR,IAAI,IAAI,CAACzJ,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC+G,KAAK,CAACC,UAAU,CAACU,KAAI,GAAI,EAAE;AACpC,WAAE,MAAK;AAAA,YAAA,IAAAqC,sBAAA;AACH,YAAA,IAAMC,oCAAU,IAAI,CAACjD,KAAK,CAACC,UAAU,MAAA+C,IAAAA,IAAAA,sBAAA,KAArBA,MAAAA,GAAAA,MAAAA,GAAAA,sBAAA,CAAuB9C,GAAG;AAC1C+C,YAAAA,YAAYA,OAAO,CAACtC,KAAM,GAAE,EAAE,CAAC;AACnC;AACA,UAAA,IAAI,CAACb,KAAK,CAAC,OAAO,CAAC;UACnB,CAAC,IAAI,CAAC7G,YAAY,IAAI,CAACsJ,WAAW,CAAC/B,KAAK,EAAE,IAAI,CAAC;AACnD;AACJ;KACH;IACD0C,wBAAwB,EAAA,SAAxBA,wBAAwBA,GAAG;MACvB,IAAI,IAAI,CAAChE,QAAQ,EAAE;AACf;AACA,QAAA;AACJ;MAEA,IAAI,CAACtC,OAAQ,GAAE,IAAI;KACtB;IACDuG,uBAAuB,EAAA,SAAvBA,uBAAuBA,GAAG;AACtB,MAAA,IAAI,CAACrG,0BAA2B,GAAE,EAAE;MACpC,IAAI,CAACF,UAAU,KAAK;KACvB;AACDwG,IAAAA,0BAA0B,EAA1BA,SAAAA,0BAA0BA,CAAC5C,KAAK,EAAE;MAC9B,IAAI,IAAI,CAACtB,QAAQ,EAAE;QACfsB,KAAK,CAACW,cAAc,EAAE;AAEtB,QAAA;AACJ;MAEA,QAAQX,KAAK,CAACY,IAAI;AACd,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACiC,wBAAwB,CAAC7C,KAAK,CAAC;AACpC,UAAA;AAEJ,QAAA,KAAK,YAAY;AACb,UAAA,IAAI,CAAC8C,yBAAyB,CAAC9C,KAAK,CAAC;AACrC,UAAA;AAEJ,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAAC+C,wBAAwB,CAAC/C,KAAK,CAAC;AACpC,UAAA;AAIR;KACH;AACDgD,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAAChD,KAAK,EAAE;MACpB,IAAI,CAAC7D,OAAQ,GAAE,IAAI;AAEnB,MAAA,IAAI,IAAI,CAACuC,QAAO,IAAK,IAAI,CAAClC,aAAa,IAAI,CAAC9D,OAAM,IAAK,IAAI,CAACuK,iBAAiB,CAACjD,KAAK,CAAC,EAAE;AAClF,QAAA;AACJ;AAEA,MAAA,IAAI,CAAC,IAAI,CAACnE,OAAM,IAAK,CAAC,IAAI,CAACA,OAAO,CAACqH,QAAQ,CAAClD,KAAK,CAACE,MAAM,CAAC,EAAE;AACvDX,QAAAA,KAAK,CAAC,IAAI,CAAC9G,QAAS,GAAE,IAAI,CAAC+G,KAAK,CAACC,UAAW,GAAE,IAAI,CAACD,KAAK,CAACC,UAAU,CAACC,GAAG,CAAC;AAC5E;KACH;AACDyD,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAACnD,KAAK,EAAE;MACnB,IAAI8B,KAAM,GAAExH,SAAS;MAErB,IAAI,IAAI,CAACiC,cAAc,EAAE;AACrB,QAAA,IAAI,CAACO,IAAI,CAAC,IAAI,CAAC;AACnB,OAAE,MAAK;AACH,QAAA,IAAIoD,MAAO,GAAE,IAAI,CAACzH,QAAO,GAAI,IAAI,CAAC+G,KAAK,CAACC,UAAW,GAAE,IAAI,CAACD,KAAK,CAACC,UAAU,CAACC,GAAG;QAE9EH,KAAK,CAACW,MAAM,CAAC;QACb4B,KAAM,GAAE5B,MAAM,CAACC,KAAK;AAEpB,QAAA,IAAI,IAAI,CAAC3H,YAAa,KAAI,OAAO,EAAE,IAAI,CAACyH,MAAM,CAACD,KAAK,EAAE,EAAE,EAAE,UAAU,CAAC,CAAA,KAChE,IAAI,IAAI,CAACxH,iBAAiB,SAAS,EAAE,IAAI,CAACyH,MAAM,CAACD,KAAK,EAAE8B,KAAK,EAAE,UAAU,CAAC;AACnF;AAEA,MAAA,IAAI,CAACxC,KAAK,CAAC,gBAAgB,EAAE;AAAE8D,QAAAA,aAAa,EAAEpD,KAAK;AAAE8B,QAAAA,KAAM,EAANA;AAAM,OAAC,CAAC;KAChE;AACDS,IAAAA,cAAc,WAAdA,cAAcA,CAACvC,KAAK,EAAEjC,MAAM,EAAiB;AAAA,MAAA,IAAfsF,MAAO,GAAAC,SAAA,CAAAzG,MAAA,GAAA,CAAA,IAAAyG,SAAA,CAAA,CAAA,CAAA,KAAAhJ,SAAA,GAAAgJ,SAAA,CAAA,CAAA,CAAA,GAAE,IAAI;AACvC,MAAA,IAAMnD,KAAM,GAAE,IAAI,CAAClC,cAAc,CAACF,MAAM,CAAC;MAEzC,IAAI,IAAI,CAACtF,QAAQ,EAAE;AACf,QAAA,IAAI,CAAC+G,KAAK,CAACC,UAAU,CAACU,KAAI,GAAI,EAAE;AAEhC,QAAA,IAAI,CAAC,IAAI,CAAC1B,UAAU,CAACV,MAAM,CAAC,EAAE;AAC1B,UAAA,IAAI,CAACgE,WAAW,CAAC/B,KAAK,EAAA,EAAA,CAAAuD,MAAA,CAAAC,kBAAA,CAAO,IAAI,CAACC,OAAM,IAAK,EAAE,CAAGtD,EAAAA,CAAAA,KAAK,EAAC,CAAC;AAC7D;AACJ,OAAE,MAAK;AACH,QAAA,IAAI,CAAC4B,WAAW,CAAC/B,KAAK,EAAEG,KAAK,CAAC;AAClC;AAEA,MAAA,IAAI,CAACb,KAAK,CAAC,aAAa,EAAE;AAAE8D,QAAAA,aAAa,EAAEpD,KAAK;AAAEG,QAAAA,KAAK,EAAEpC;AAAO,OAAC,CAAC;AAClE,MAAA,IAAI,CAACuB,KAAK,CAAC,eAAe,EAAE;AAAE8D,QAAAA,aAAa,EAAEpD,KAAK;AAAEG,QAAAA,KAAK,EAAEpC;AAAO,OAAC,CAAC;AAEpEsF,MAAAA,UAAU,IAAI,CAACvG,IAAI,CAAC,IAAI,CAAC;KAC5B;AACD4G,IAAAA,iBAAiB,WAAjBA,iBAAiBA,CAAC1D,KAAK,EAAErC,KAAK,EAAE;MAC5B,IAAI,IAAI,CAACvD,YAAY,EAAE;AACnB,QAAA,IAAI,CAACuJ,wBAAwB,CAAC3D,KAAK,EAAErC,KAAK,CAAC;AAC/C;KACH;AACDiG,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAAC5D,KAAK,EAAwB;AAAA,MAAA,IAAA6D,MAAA,GAAA,IAAA;AAAA,MAAA,IAAtBC,KAAM,GAAAR,SAAA,CAAAzG,MAAA,GAAA,CAAA,IAAAyG,SAAA,CAAA,CAAA,CAAA,KAAAhJ,SAAA,GAAAgJ,SAAA,CAAA,CAAA,CAAA,GAAE,EAAE;AAAA,MAAA,IAAES,0EAAM,EAAE;AAC3CD,MAAAA,KAAI,KAAM,EAAG,KAAIA,KAAM,GAAE,IAAI,CAACE,8BAA8B,CAACD,GAAG,EAAE,IAAI,CAAC,CAAC;AACxEA,MAAAA,GAAI,KAAI,EAAC,KAAMA,GAAE,GAAI,IAAI,CAACC,8BAA8B,CAACF,KAAK,CAAC,CAAC;MAEhE,IAAIA,KAAI,KAAM,MAAMC,GAAE,KAAM,EAAE,EAAE;QAC5B,IAAME,aAAaC,IAAI,CAACC,GAAG,CAACL,KAAK,EAAEC,GAAG,CAAC;QACvC,IAAMK,QAAO,GAAIF,IAAI,CAACG,GAAG,CAACP,KAAK,EAAEC,GAAG,CAAC;AACrC,QAAA,IAAM5D,KAAI,GAAI,IAAI,CAACvD,cAAa,CAC3BuC,KAAK,CAAC8E,UAAU,EAAEG,QAAS,GAAE,CAAC,CAAA,CAC9BhF,MAAM,CAAC,UAACrB,MAAM,EAAA;AAAA,UAAA,OAAK8F,MAAI,CAACS,aAAa,CAACvG,MAAM,CAAC;AAAA,SAAA,CAAA,CAC7CwG,GAAG,CAAC,UAACxG,MAAM,EAAA;AAAA,UAAA,OAAK8F,MAAI,CAAC5F,cAAc,CAACF,MAAM,CAAC;SAAC,CAAA;AAEjD,QAAA,IAAI,CAACgE,WAAW,CAAC/B,KAAK,EAAEG,KAAK,CAAC;AAClC;KACH;AACDqE,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACxE,KAAK,EAAE;AAClByE,MAAAA,eAAe,CAACC,IAAI,CAAC,eAAe,EAAE;AAClCtB,QAAAA,aAAa,EAAEpD,KAAK;QACpBE,MAAM,EAAE,IAAI,CAACR;AACjB,OAAC,CAAC;KACL;AACDiF,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAAC3E,KAAK,EAAE;MACpB,QAAQA,KAAK,CAACY,IAAI;AACd,QAAA,KAAK,QAAQ;AACT,UAAA,IAAI,CAACW,WAAW,CAACvB,KAAK,CAAC;AACvB,UAAA;AAIR;KACH;AACDa,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACb,KAAK,EAAE;AAClB,MAAA,IAAI,CAAC,IAAI,CAACzD,cAAc,EAAE;AACtB,QAAA;AACJ;AAEA,MAAA,IAAMqI,WAAY,GAAE,IAAI,CAACvI,uBAAuB,KAAK,IAAI,CAACwI,mBAAmB,CAAC,IAAI,CAACxI,kBAAkB,CAAA,GAAI,IAAI,CAACF,OAAM,GAAI,IAAI,CAAC2I,oBAAoB,EAAC,GAAI,IAAI,CAACnI,2BAA2B,EAAE;AAExL,MAAA,IAAI,IAAI,CAAClE,QAAO,IAAKuH,KAAK,CAAC+E,QAAQ,EAAE;QACjC,IAAI,CAACnB,mBAAmB,CAAC5D,KAAK,EAAE,IAAI,CAAC/D,eAAe,EAAE2I,WAAW,CAAC;AACtE;AAEA,MAAA,IAAI,CAACjB,wBAAwB,CAAC3D,KAAK,EAAE4E,WAAW,CAAC;MAEjD5E,KAAK,CAACW,cAAc,EAAE;KACzB;AACDG,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACd,KAAK,EAAE;AAChB,MAAA,IAAI,CAAC,IAAI,CAACzD,cAAc,EAAE;AACtB,QAAA;AACJ;MAEA,IAAIyD,KAAK,CAACgF,MAAM,EAAE;AACd,QAAA,IAAI,IAAI,CAAC3I,kBAAiB,KAAM,EAAE,EAAE;AAChC,UAAA,IAAI,CAACkG,cAAc,CAACvC,KAAK,EAAE,IAAI,CAACpD,cAAc,CAAC,IAAI,CAACP,kBAAkB,CAAC,CAAC;AAC5E;AAEA,QAAA,IAAI,CAACE,cAAe,IAAG,IAAI,CAACO,IAAI,EAAE;QAClCkD,KAAK,CAACW,cAAc,EAAE;AAC1B,OAAE,MAAK;AACH,QAAA,IAAMiE,WAAY,GAAE,IAAI,CAACvI,kBAAmB,KAAI,EAAG,GAAE,IAAI,CAAC4I,mBAAmB,CAAC,IAAI,CAAC5I,kBAAkB,CAAE,GAAE,IAAI,CAACF,UAAU,IAAI,CAAC+I,mBAAmB,KAAK,IAAI,CAACC,0BAA0B,EAAE;AAEtL,QAAA,IAAI,IAAI,CAAC1M,QAAO,IAAKuH,KAAK,CAAC+E,QAAQ,EAAE;UACjC,IAAI,CAACnB,mBAAmB,CAAC5D,KAAK,EAAE4E,WAAW,EAAE,IAAI,CAAC3I,eAAe,CAAC;AACtE;AAEA,QAAA,IAAI,CAAC0H,wBAAwB,CAAC3D,KAAK,EAAE4E,WAAW,CAAC;QAEjD5E,KAAK,CAACW,cAAc,EAAE;AAC1B;KACH;AACDI,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACf,KAAK,EAAE;AAClB,MAAA,IAAME,MAAK,GAAIF,KAAK,CAACoF,aAAa;AAElC,MAAA,IAAI,CAAC/I,kBAAiB,GAAI,EAAE;MAE5B,IAAI,IAAI,CAAC5D,QAAQ,EAAE;QACf,IAAI4M,OAAO,CAACnF,MAAM,CAACC,KAAK,CAAA,IAAK,IAAI,CAACmF,OAAO,EAAE;AACvC/F,UAAAA,KAAK,CAAC,IAAI,CAACC,KAAK,CAAC+F,cAAc,CAAC;AAChC,UAAA,IAAI,CAACjJ,0BAAyB,GAAI,IAAI,CAACmH,OAAO,CAAC5G,MAAM;AACzD,SAAE,MAAK;AACHmD,UAAAA,KAAK,CAACwF,eAAe,EAAE,CAAE;AAC7B;AACJ;KACH;AACDxE,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAAChB,KAAK,EAAE;AACnB,MAAA,IAAI,CAAC3D,kBAAiB,GAAI,EAAE;MAE5B,IAAI,CAAC5D,QAAS,IAAGuH,KAAK,CAACwF,eAAe,EAAE,CAAE;KAC7C;AACDvE,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACjB,KAAK,EAAE;AACb,MAAA,IAAQoF,aAAc,GAAIpF,KAAK,CAAvBoF,aAAc;AACtB,MAAA,IAAMK,GAAE,GAAIL,aAAa,CAACjF,KAAK,CAACtD,MAAM;MACtC,IAAM6I,UAAU1F,KAAK,CAAC0F,WAAW1F,KAAK,CAAC2F,OAAO;AAC9C,MAAA,IAAMf,WAAU,GAAI,IAAI,CAACE,oBAAoB,EAAE;MAE/C,IAAI,IAAI,CAACrM,YAAYuH,KAAK,CAAC+E,QAAO,IAAKW,OAAO,EAAE;QAC5C,IAAI,CAAC9B,mBAAmB,CAAC5D,KAAK,EAAE4E,WAAW,EAAE,IAAI,CAAC3I,eAAe,CAAC;AACtE;AAEAmJ,MAAAA,aAAa,CAACQ,iBAAiB,CAAC,CAAC,EAAE5F,KAAK,CAAC+E,QAAO,GAAIU,GAAE,GAAI,CAAC,CAAC;AAC5D,MAAA,IAAI,CAACpJ,kBAAiB,GAAI,EAAE;MAE5B2D,KAAK,CAACW,cAAc,EAAE;KACzB;AACDO,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAAClB,KAAK,EAAE;AACZ,MAAA,IAAQoF,aAAc,GAAIpF,KAAK,CAAvBoF,aAAc;AACtB,MAAA,IAAMK,GAAE,GAAIL,aAAa,CAACjF,KAAK,CAACtD,MAAM;MACtC,IAAM6I,UAAU1F,KAAK,CAAC0F,WAAW1F,KAAK,CAAC2F,OAAO;AAC9C,MAAA,IAAMf,cAAc,IAAI,CAACM,mBAAmB,EAAE;MAE9C,IAAI,IAAI,CAACzM,YAAYuH,KAAK,CAAC+E,QAAO,IAAKW,OAAO,EAAE;QAC5C,IAAI,CAAC9B,mBAAmB,CAAC5D,KAAK,EAAE,IAAI,CAAC/D,eAAe,EAAE2I,WAAW,CAAC;AACtE;AAEAQ,MAAAA,aAAa,CAACQ,iBAAiB,CAAC5F,KAAK,CAAC+E,QAAO,GAAI,CAAA,GAAIU,GAAG,EAAEA,GAAG,CAAC;AAC9D,MAAA,IAAI,CAACpJ,kBAAiB,GAAI,EAAE;MAE5B2D,KAAK,CAACW,cAAc,EAAE;KACzB;AACDS,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACpB,KAAK,EAAE;AACf,MAAA,IAAI,CAACI,YAAY,CAAC,CAAC,CAAC;MACpBJ,KAAK,CAACW,cAAc,EAAE;KACzB;AACDQ,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAACnB,KAAK,EAAE;MACjB,IAAI,CAACI,YAAY,CAAC,IAAI,CAACxD,cAAc,CAACC,MAAK,GAAI,CAAC,CAAC;MACjDmD,KAAK,CAACW,cAAc,EAAE;KACzB;AACDU,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACrB,KAAK,EAAE;AACd,MAAA,IAAI,CAAC,IAAI,CAACnF,SAAS,EAAE;QACjB,IAAI,IAAI,CAACpC,QAAQ,EAAE;UACf,IAAIuH,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC0F,IAAI,EAAE,EAAE;YAC3B,IAAI,CAAC9D,WAAW,CAAC/B,KAAK,EAAA,EAAA,CAAAuD,MAAA,CAAAC,kBAAA,CAAO,IAAI,CAACC,OAAM,IAAK,EAAE,CAAA,EAAA,CAAGzD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC0F,IAAI,EAAE,CAAA,CAAC,CAAC;AAC7E,YAAA,IAAI,CAACrG,KAAK,CAACC,UAAU,CAACU,KAAI,GAAI,EAAE;AACpC;AACJ;AACJ,OAAE,MAAK;AACH,QAAA,IAAI,CAAC,IAAI,CAAC5D,cAAc,EAAE;AACtB,UAAA,IAAI,CAACF,qBAAqB,EAAE,CAAA;AAC5B,UAAA,IAAI,CAACwE,cAAc,CAACb,KAAK,CAAC;AAC9B,SAAE,MAAK;AACH,UAAA,IAAI,IAAI,CAAC3D,kBAAiB,KAAM,EAAE,EAAE;AAChC,YAAA,IAAI,IAAI,CAAC5D,QAAO,IAAKuH,KAAK,CAAC+E,QAAQ,EAAE;cACjC,IAAI,CAACnB,mBAAmB,CAAC5D,KAAK,EAAE,IAAI,CAAC3D,kBAAkB,CAAC;cACxD2D,KAAK,CAACW,cAAc,EAAE;AAC1B,aAAE,MAAK;AACH,cAAA,IAAI,CAAC4B,cAAc,CAACvC,KAAK,EAAE,IAAI,CAACpD,cAAc,CAAC,IAAI,CAACP,kBAAkB,CAAC,CAAC;AAC5E;AACJ;UAEA,IAAI,CAACS,IAAI,EAAE;AACf;AACJ;KACH;AACDwE,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACtB,KAAK,EAAE;AACd,MAAA,IAAI,IAAI,CAAC3D,kBAAiB,KAAM,EAAE,EAAE;AAChC,QAAA,IAAI,CAACgF,UAAU,CAACrB,KAAK,CAAC;AAC1B;KACH;AACDuB,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACvB,KAAK,EAAE;MACf,IAAI,CAACzD,cAAe,IAAG,IAAI,CAACO,IAAI,CAAC,IAAI,CAAC;MACtCkD,KAAK,CAACW,cAAc,EAAE;KACzB;AACDa,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAACxB,KAAK,EAAE;AACZ,MAAA,IAAI,IAAI,CAAC3D,kBAAiB,KAAM,EAAE,EAAE;AAChC,QAAA,IAAI,CAACkG,cAAc,CAACvC,KAAK,EAAE,IAAI,CAACpD,cAAc,CAAC,IAAI,CAACP,kBAAkB,CAAC,CAAC;AAC5E;AAEA,MAAA,IAAI,CAACE,cAAe,IAAG,IAAI,CAACO,IAAI,EAAE;KACrC;IACD2E,UAAU,EAAA,SAAVA,UAAUA,GAAG;AACT,MAAA,IAAI,CAACxF,kBAAkB,IAAI,CAACI,kBAAkB;KACjD;AACDqF,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAAC1B,KAAK,EAAE;MAClB,IAAI,IAAI,CAACvH,QAAQ,EAAE;AACf,QAAA,IAAIqN,UAAU,CAAC,IAAI,CAACrC,OAAO,CAAA,IAAK,CAAC,IAAI,CAACjE,KAAK,CAACC,UAAU,CAACU,KAAK,EAAE;AAC1D,UAAA,IAAM4F,YAAW,GAAI,IAAI,CAACtC,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC5G,MAAK,GAAI,CAAC,CAAC;AAC1D,UAAA,IAAMmJ,QAAS,GAAE,IAAI,CAACvC,OAAO,CAACtE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;AAE1C,UAAA,IAAI,CAAC8G,UAAU,CAACD,QAAQ,EAAEhG,KAAK,CAAC;AAChC,UAAA,IAAI,CAACV,KAAK,CAAC,eAAe,EAAE;AAAE8D,YAAAA,aAAa,EAAEpD,KAAK;AAAEG,YAAAA,KAAK,EAAE4F;AAAa,WAAC,CAAC;AAC1E,UAAA,IAAI,CAACzG,KAAK,CAAC,iBAAiB,EAAE;AAAE8D,YAAAA,aAAa,EAAEpD,KAAK;AAAEG,YAAAA,KAAK,EAAE4F;AAAa,WAAC,CAAC;AAChF;AAEA/F,QAAAA,KAAK,CAACwF,eAAe,EAAE,CAAE;AAC7B;KACH;IACD3C,wBAAwB,EAAA,SAAxBA,wBAAwBA,GAAG;AACvB,MAAA,IAAI,CAACvG,6BAA6B,IAAI,CAACA,0BAAyB,GAAI,CAAE,GAAE,IAAI,IAAI,CAACA,0BAA2B,GAAE,CAAC;KAClH;IACDwG,yBAAyB,EAAA,SAAzBA,yBAAyBA,GAAG;MACxB,IAAI,CAACxG,0BAA0B,EAAE;MAEjC,IAAI,IAAI,CAACA,0BAA2B,GAAE,IAAI,CAACmH,OAAO,CAAC5G,MAAK,GAAI,CAAC,EAAE;AAC3D,QAAA,IAAI,CAACP,0BAA2B,GAAE,EAAE;AACpCiD,QAAAA,KAAK,CAAC,IAAI,CAACC,KAAK,CAACC,UAAU,CAAC;AAChC;KACH;AACDsD,IAAAA,wBAAwB,EAAxBA,SAAAA,wBAAwBA,CAAC/C,KAAK,EAAE;AAC5B,MAAA,IAAI,IAAI,CAAC1D,0BAA2B,KAAI,EAAE,EAAE;QACxC,IAAI,CAAC4J,YAAY,CAAClG,KAAK,EAAE,IAAI,CAAC1D,0BAA0B,CAAC;AAC7D;KACH;AACD6J,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACC,EAAE,EAAE;AACf7I,MAAAA,MAAM,CAAC8I,GAAG,CAAC,SAAS,EAAED,EAAE,EAAE,IAAI,CAACE,SAAS,CAACC,MAAM,CAACC,MAAM,CAAC3K,OAAO,CAAC;MAE/D4K,QAAQ,CAACL,EAAE,EAAE;AAAEM,QAAAA,QAAQ,EAAE,UAAU;AAAEC,QAAAA,GAAG,EAAE;AAAI,OAAC,CAAC;MAChD,IAAI,CAACzJ,YAAY,EAAE;;AAEnB;AACA,MAAA,IAAI,CAAC0J,aAAc,IAAGR,EAAE,CAACS,YAAY,CAAC,IAAI,CAACD,aAAa,EAAE,EAAE,CAAC;KAChE;IACDE,mBAAmB,EAAA,SAAnBA,mBAAmBA,GAAG;MAClB,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,kBAAkB,EAAE;MACzB,IAAI,CAACC,kBAAkB,EAAE;AAEzB,MAAA,IAAI,CAAC3H,KAAK,CAAC,MAAM,CAAC;KACrB;IACD4H,cAAc,EAAA,SAAdA,cAAcA,GAAG;MACb,IAAI,CAAC9J,0BAA0B,EAAE;MACjC,IAAI,CAAC+J,oBAAoB,EAAE;MAC3B,IAAI,CAAC9J,oBAAoB,EAAE;AAE3B,MAAA,IAAI,CAACiC,KAAK,CAAC,MAAM,CAAC;MAClB,IAAI,CAACzD,OAAQ,GAAE,IAAI;KACtB;AACDuL,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAAChB,EAAE,EAAE;AACpB7I,MAAAA,MAAM,CAACC,KAAK,CAAC4I,EAAE,CAAC;KACnB;IACDlJ,YAAY,EAAA,SAAZA,YAAYA,GAAG;AACX,MAAA,IAAIgD,MAAO,GAAE,IAAI,CAACzH,QAAS,GAAE,IAAI,CAAC+G,KAAK,CAAC+F,iBAAiB,IAAI,CAAC/F,KAAK,CAACC,UAAU,CAACC,GAAG;AAElF,MAAA,IAAI,IAAI,CAAC1G,QAAO,KAAM,MAAM,EAAE;AAC1BqO,QAAAA,gBAAgB,CAAC,IAAI,CAACxL,OAAO,EAAEqE,MAAM,CAAC;AAC1C,OAAE,MAAK;AACH,QAAA,IAAI,CAACrE,OAAO,CAACb,KAAK,CAACsM,QAAO,GAAIC,aAAa,CAACrH,MAAM,IAAI,IAAI;AAC1DsH,QAAAA,gBAAgB,CAAC,IAAI,CAAC3L,OAAO,EAAEqE,MAAM,CAAC;AAC1C;KACH;IACD6G,wBAAwB,EAAA,SAAxBA,wBAAwBA,GAAG;AAAA,MAAA,IAAAU,MAAA,GAAA,IAAA;AACvB,MAAA,IAAI,CAAC,IAAI,CAAC/L,oBAAoB,EAAE;AAC5B,QAAA,IAAI,CAACA,oBAAqB,GAAE,UAACsE,KAAK,EAAK;AACnC,UAAA,IAAIyH,MAAI,CAAClL,cAAa,IAAKkL,MAAI,CAAC5L,OAAQ,IAAG4L,MAAI,CAACC,gBAAgB,CAAC1H,KAAK,CAAC,EAAE;YACrEyH,MAAI,CAAC3K,IAAI,EAAE;AACf;SACH;QAED6K,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAClM,oBAAoB,EAAE,IAAI,CAAC;AACvE;KACH;IACD0B,0BAA0B,EAAA,SAA1BA,0BAA0BA,GAAG;MACzB,IAAI,IAAI,CAAC1B,oBAAoB,EAAE;QAC3BiM,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACnM,oBAAoB,EAAE,IAAI,CAAC;QACtE,IAAI,CAACA,oBAAmB,GAAI,IAAI;AACpC;KACH;IACDsL,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;AAAA,MAAA,IAAAc,MAAA,GAAA,IAAA;AACjB,MAAA,IAAI,CAAC,IAAI,CAAClM,aAAa,EAAE;AACrB,QAAA,IAAI,CAACA,aAAY,GAAI,IAAImM,6BAA6B,CAAC,IAAI,CAACvI,KAAK,CAACwI,SAAS,EAAE,YAAM;UAC/E,IAAIF,MAAI,CAACvL,cAAc,EAAE;YACrBuL,MAAI,CAAChL,IAAI,EAAE;AACf;AACJ,SAAC,CAAC;AACN;AAEA,MAAA,IAAI,CAAClB,aAAa,CAACoL,kBAAkB,EAAE;KAC1C;IACDG,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;MACnB,IAAI,IAAI,CAACvL,aAAa,EAAE;AACpB,QAAA,IAAI,CAACA,aAAa,CAACuL,oBAAoB,EAAE;AAC7C;KACH;IACDF,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;AAAA,MAAA,IAAAgB,MAAA,GAAA,IAAA;AACjB,MAAA,IAAI,CAAC,IAAI,CAACtM,cAAc,EAAE;QACtB,IAAI,CAACA,iBAAiB,YAAM;UACxB,IAAIsM,MAAI,CAAC1L,cAAa,IAAK,CAAC2L,aAAa,EAAE,EAAE;YACzCD,MAAI,CAACnL,IAAI,EAAE;AACf;SACH;QAEDqL,MAAM,CAACP,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACjM,cAAc,CAAC;AAC1D;KACH;IACD0B,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;MACnB,IAAI,IAAI,CAAC1B,cAAc,EAAE;QACrBwM,MAAM,CAACN,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAClM,cAAc,CAAC;QACzD,IAAI,CAACA,iBAAiB,IAAI;AAC9B;KACH;AACD+L,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAAC1H,KAAK,EAAE;MACpB,OAAO,CAAC,IAAI,CAACnE,OAAO,CAACqH,QAAQ,CAAClD,KAAK,CAACE,MAAM,KAAK,CAAC,IAAI,CAACkI,cAAc,CAACpI,KAAK,KAAK,CAAC,IAAI,CAACiD,iBAAiB,CAACjD,KAAK,CAAC;KAC/G;AACDoI,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACpI,KAAK,EAAE;AAClB,MAAA,IAAI,IAAI,CAACvH,QAAQ,EAAE,OAAOuH,KAAK,CAACE,WAAW,IAAI,CAACV,KAAK,CAAC+F,cAAa,IAAK,IAAI,CAAC/F,KAAK,CAAC+F,cAAc,CAACrC,QAAQ,CAAClD,KAAK,CAACE,MAAM,CAAC,CAAA,KACnH,OAAOF,KAAK,CAACE,MAAO,KAAI,IAAI,CAACV,KAAK,CAACC,UAAU,CAACC,GAAG;KACzD;AACDuD,IAAAA,iBAAiB,EAAjBA,SAAAA,iBAAiBA,CAACjD,KAAK,EAAE;AACrB,MAAA,OAAO,IAAI,CAACR,KAAK,CAAC6I,cAAa,GAAIrI,KAAK,CAACE,MAAO,KAAI,IAAI,CAACV,KAAK,CAAC6I,cAAa,IAAK,IAAI,CAAC7I,KAAK,CAAC6I,cAAc,CAACnF,QAAQ,CAAClD,KAAK,CAACE,MAAM,CAAA,GAAI,KAAK;KAC5I;AACDoC,IAAAA,eAAe,WAAfA,eAAeA,CAACvE,MAAM,EAAEoC,KAAK,EAAE;AAAA,MAAA,IAAAmI,oBAAA;AAC3B,MAAA,OAAO,IAAI,CAAChE,aAAa,CAACvG,MAAM,CAAA,IAAK,CAAAuK,CAAAA,oBAAA,OAAI,CAACxK,cAAc,CAACC,MAAM,CAAC,MAAAuK,IAAAA,IAAAA,oBAAA,KAA3BA,MAAAA,GAAAA,MAAAA,GAAAA,oBAAA,CAA6BC,iBAAiB,CAAC,IAAI,CAAClO,YAAY,CAAA,MAAM8F,KAAK,CAACoI,iBAAiB,CAAC,IAAI,CAAClO,YAAY,CAAC;KACxJ;AACDiK,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAACvG,MAAM,EAAE;AAClB,MAAA,OAAO+H,UAAU,CAAC/H,MAAM,KAAK,EAAE,IAAI,CAACY,gBAAgB,CAACZ,MAAM,KAAK,IAAI,CAACa,aAAa,CAACb,MAAM,CAAC,CAAC;KAC9F;AACDyK,IAAAA,qBAAqB,EAArBA,SAAAA,qBAAqBA,CAACzK,MAAM,EAAE;AAC1B,MAAA,OAAO,IAAI,CAACuG,aAAa,CAACvG,MAAM,CAAE,IAAG,IAAI,CAACU,UAAU,CAACV,MAAM,CAAC;KAC/D;AACD0K,IAAAA,QAAQ,WAARA,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAE;MACrB,OAAOC,MAAM,CAACF,MAAM,EAAEC,MAAM,EAAE,IAAI,CAACE,WAAW,CAAC;KAClD;AACDpK,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACV,MAAM,EAAE;AAAA,MAAA,IAAA+K,MAAA,GAAA,IAAA;AACf,MAAA,IAAMC,WAAU,GAAI,IAAI,CAAC9K,cAAc,CAACF,MAAM,CAAC;AAE/C,MAAA,OAAO,IAAI,CAACtF,QAAO,GAAI,CAAC,IAAI,CAACgL,WAAW,EAAE,EAAEuF,IAAI,CAAC,UAAC7I,KAAK,EAAA;AAAA,QAAA,OAAK2I,MAAI,CAACL,QAAQ,CAACtI,KAAK,EAAE4I,WAAW,CAAC;AAAA,OAAA,CAAA,GAAI,IAAI,CAACN,QAAQ,CAAC,IAAI,CAAChF,OAAO,EAAE,IAAI,CAACxF,cAAc,CAACF,MAAM,CAAC,CAAC;KAC5J;IACD+G,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;AAAA,MAAA,IAAAmE,MAAA,GAAA,IAAA;AACnB,MAAA,OAAO,IAAI,CAACrM,cAAc,CAACsM,SAAS,CAAC,UAACnL,MAAM,EAAA;AAAA,QAAA,OAAKkL,MAAI,CAAC3E,aAAa,CAACvG,MAAM,CAAC;OAAC,CAAA;KAC/E;IACDmH,mBAAmB,EAAA,SAAnBA,mBAAmBA,GAAG;AAAA,MAAA,IAAAiE,MAAA,GAAA,IAAA;AAClB,MAAA,OAAOC,aAAa,CAAC,IAAI,CAACxM,cAAc,EAAE,UAACmB,MAAM,EAAA;AAAA,QAAA,OAAKoL,MAAI,CAAC7E,aAAa,CAACvG,MAAM,CAAC;OAAC,CAAA;KACpF;AACD8G,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAAClH,KAAK,EAAE;AAAA,MAAA,IAAA0L,OAAA,GAAA,IAAA;MACvB,IAAMC,kBAAiB,GAAI3L,KAAM,GAAE,IAAI,CAACf,cAAc,CAACC,MAAO,GAAE,CAAE,GAAE,IAAI,CAACD,cAAc,CAACuC,KAAK,CAACxB,KAAI,GAAI,CAAC,CAAC,CAACuL,SAAS,CAAC,UAACnL,MAAM,EAAA;AAAA,QAAA,OAAKsL,OAAI,CAAC/E,aAAa,CAACvG,MAAM,CAAC;OAAE,CAAA,GAAE,EAAE;MAE/J,OAAOuL,kBAAiB,GAAI,EAAG,GAAEA,kBAAmB,GAAE3L,KAAM,GAAE,CAAE,GAAEA,KAAK;KAC1E;AACDsH,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAACtH,KAAK,EAAE;AAAA,MAAA,IAAA4L,OAAA,GAAA,IAAA;MACvB,IAAMD,kBAAiB,GAAI3L,KAAI,GAAI,CAAE,GAAEyL,aAAa,CAAC,IAAI,CAACxM,cAAc,CAACuC,KAAK,CAAC,CAAC,EAAExB,KAAK,CAAC,EAAE,UAACI,MAAM,EAAA;AAAA,QAAA,OAAKwL,OAAI,CAACjF,aAAa,CAACvG,MAAM,CAAC;OAAA,CAAA,GAAI,EAAE;AAEtI,MAAA,OAAOuL,kBAAiB,GAAI,KAAKA,kBAAiB,GAAI3L,KAAK;KAC9D;IACD6L,uBAAuB,EAAA,SAAvBA,uBAAuBA,GAAG;AAAA,MAAA,IAAAC,OAAA,GAAA,IAAA;MACtB,OAAO,IAAI,CAACnE,UAAU,IAAI,CAAC1I,cAAc,CAACsM,SAAS,CAAC,UAACnL,MAAM,EAAA;AAAA,QAAA,OAAK0L,OAAI,CAACjB,qBAAqB,CAACzK,MAAM,CAAC;OAAA,CAAA,GAAI,EAAE;KAC3G;IACDpB,2BAA2B,EAAA,SAA3BA,2BAA2BA,GAAG;AAC1B,MAAA,IAAM+M,gBAAgB,IAAI,CAACF,uBAAuB,EAAE;MAEpD,OAAOE,aAAY,GAAI,CAAA,GAAI,IAAI,CAAC5E,oBAAoB,EAAC,GAAI4E,aAAa;KACzE;IACDvE,0BAA0B,EAAA,SAA1BA,0BAA0BA,GAAG;AACzB,MAAA,IAAMuE,gBAAgB,IAAI,CAACF,uBAAuB,EAAE;MAEpD,OAAOE,aAAY,GAAI,CAAA,GAAI,IAAI,CAACxE,mBAAmB,EAAG,GAAEwE,aAAa;KACxE;IACDzJ,MAAM,EAAA,SAANA,MAAMA,CAACD,KAAK,EAAE8B,KAAK,EAAE6H,MAAM,EAAE;AACzB;AACA,MAAA,IAAI7H,KAAI,KAAMxH,SAAU,IAAGwH,KAAM,KAAI,IAAI,EAAE;AACvC,QAAA;AACJ;;AAEA;AACA,MAAA,IAAI6H,MAAO,KAAI,OAAM,IAAK7H,KAAK,CAAC+D,IAAI,EAAE,CAAChJ,MAAK,KAAM,CAAC,EAAE;AACjD,QAAA;AACJ;MAEA,IAAI,CAACL,SAAQ,GAAI,IAAI;AACrB,MAAA,IAAI,CAAC8C,KAAK,CAAC,UAAU,EAAE;AAAE8D,QAAAA,aAAa,EAAEpD,KAAK;AAAE8B,QAAAA,KAAM,EAANA;AAAM,OAAC,CAAC;KAC1D;AACDoE,IAAAA,YAAY,WAAZA,YAAYA,CAAClG,KAAK,EAAErC,KAAK,EAAE;AAAA,MAAA,IAAAiM,OAAA,GAAA,IAAA;AACvB,MAAA,IAAMC,aAAc,GAAE,IAAI,CAACpG,OAAO,CAAC9F,KAAK,CAAC;MACzC,IAAMwC,KAAI,GAAI,IAAI,CAACsD,OAAO,CAACrE,MAAM,CAAC,UAAC0K,CAAC,EAAEC,CAAC,EAAA;QAAA,OAAKA,CAAA,KAAMpM,KAAK;AAAA,OAAA,CAAC,CAAC4G,GAAG,CAAC,UAACxG,MAAM,EAAA;AAAA,QAAA,OAAK6L,OAAI,CAAC3L,cAAc,CAACF,MAAM,CAAC;OAAC,CAAA;AAErG,MAAA,IAAI,CAACgE,WAAW,CAAC/B,KAAK,EAAEG,KAAK,CAAC;AAC9B,MAAA,IAAI,CAACb,KAAK,CAAC,eAAe,EAAE;AAAE8D,QAAAA,aAAa,EAAEpD,KAAK;AAAEG,QAAAA,KAAK,EAAE0J;AAAc,OAAC,CAAC;AAC3E,MAAA,IAAI,CAACvK,KAAK,CAAC,iBAAiB,EAAE;AAAE8D,QAAAA,aAAa,EAAEpD,KAAK;AAAEG,QAAAA,KAAK,EAAE0J;AAAc,OAAC,CAAC;MAC7E,IAAI,CAAC7N,KAAM,GAAE,IAAI;AACjBuD,MAAAA,KAAK,CAAC,IAAI,CAAC9G,QAAS,GAAE,IAAI,CAAC+G,KAAK,CAACC,UAAW,GAAE,IAAI,CAACD,KAAK,CAACC,UAAU,CAACC,GAAG,CAAC;KAC3E;AACDiE,IAAAA,wBAAwB,WAAxBA,wBAAwBA,CAAC3D,KAAK,EAAErC,KAAK,EAAE;AACnC,MAAA,IAAI,IAAI,CAACtB,kBAAiB,KAAMsB,KAAK,EAAE;QACnC,IAAI,CAACtB,kBAAmB,GAAEsB,KAAK;QAC/B,IAAI,CAACyC,YAAY,EAAE;QAEnB,IAAI,IAAI,CAACjG,aAAa,EAAE;AACpB,UAAA,IAAI,CAACoI,cAAc,CAACvC,KAAK,EAAE,IAAI,CAACpD,cAAc,CAACe,KAAK,CAAC,EAAE,KAAK,CAAC;AACjE;AACJ;KACH;IACDyC,YAAY,EAAA,SAAZA,YAAYA,GAAa;AAAA,MAAA,IAAA4J,OAAA,GAAA,IAAA;AAAA,MAAA,IAAZrM,4EAAQ,EAAE;MACnB,IAAI,CAACsM,SAAS,CAAC,YAAM;AACjB,QAAA,IAAMC,EAAC,GAAIvM,KAAI,KAAM,EAAG,MAAA4F,MAAA,CAAKyG,OAAI,CAACG,GAAG,OAAA5G,MAAA,CAAI5F,KAAK,CAAKqM,GAAAA,OAAI,CAACI,eAAe;AACvE,QAAA,IAAMC,OAAM,GAAIC,UAAU,CAACN,OAAI,CAACO,IAAI,EAAA,UAAA,CAAAhH,MAAA,CAAY2G,EAAE,EAAA,KAAA,CAAI,CAAC;AAEvD,QAAA,IAAIG,OAAO,EAAE;AACTA,UAAAA,OAAO,CAACG,kBAAkBH,OAAO,CAACG,cAAc,CAAC;AAAEC,YAAAA,KAAK,EAAE,SAAS;AAAEC,YAAAA,MAAM,EAAE;AAAQ,WAAC,CAAC;AAC3F,SAAA,MAAO,IAAI,CAACV,OAAI,CAACnM,uBAAuB,EAAE;AACtCmM,UAAAA,OAAI,CAAClO,eAAc,IAAKkO,OAAI,CAAClO,eAAe,CAAC6O,aAAa,CAAChN,UAAU,EAAC,GAAIA,KAAM,GAAEqM,OAAI,CAAC3N,kBAAkB,CAAC;AAC9G;AACJ,OAAC,CAAC;KACL;IACDU,eAAe,EAAA,SAAfA,eAAeA,GAAG;AACd,MAAA,IAAI,IAAI,CAAC5C,aAAY,IAAK,IAAI,CAACD,eAAgB,IAAG,CAAC,IAAI,CAACoL,OAAO,EAAE;AAC7D,QAAA,IAAI,CAACjJ,kBAAiB,GAAI,IAAI,CAACM,2BAA2B,EAAE;AAC5D,QAAA,IAAI,CAAC4F,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC3F,cAAc,CAAC,IAAI,CAACP,kBAAkB,CAAC,EAAE,KAAK,CAAC;AAClF;KACH;AACD0F,IAAAA,WAAW,WAAXA,WAAWA,CAAC/B,KAAK,EAAEG,KAAK,EAAE;AACtB,MAAA,IAAI,CAAC8F,UAAU,CAAC9F,KAAK,EAAEH,KAAK,CAAC;AAC7B,MAAA,IAAI,CAACV,KAAK,CAAC,QAAQ,EAAE;AAAE8D,QAAAA,aAAa,EAAEpD,KAAK;AAAEG,QAAAA,KAAM,EAANA;AAAM,OAAC,CAAC;KACxD;AACDyK,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACC,OAAO,EAAE;AAAA,MAAA,IAAAC,OAAA,GAAA,IAAA;AACjB,MAAA,OAAO,CAACD,OAAQ,IAAG,EAAE,EAAEE,MAAM,CAAC,UAACC,MAAM,EAAEjN,MAAM,EAAEJ,KAAK,EAAK;QACrDqN,MAAM,CAACC,IAAI,CAAC;AAAEpM,UAAAA,WAAW,EAAEd,MAAM;AAAEe,UAAAA,KAAK,EAAE,IAAI;AAAEnB,UAAAA,KAAM,EAANA;AAAM,SAAC,CAAC;AAExD,QAAA,IAAMxF,mBAAkB,GAAI2S,OAAI,CAAC9L,sBAAsB,CAACjB,MAAM,CAAC;AAE/D5F,QAAAA,mBAAoB,IAAGA,mBAAmB,CAAC+S,OAAO,CAAC,UAACC,CAAC,EAAA;AAAA,UAAA,OAAKH,MAAM,CAACC,IAAI,CAACE,CAAC,CAAC;SAAC,CAAA;AAEzE,QAAA,OAAOH,MAAM;OAChB,EAAE,EAAE,CAAC;KACT;AACDI,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAAChF,EAAE,EAAE;MACX,IAAI,CAACvK,OAAQ,GAAEuK,EAAE;KACpB;AACDiF,IAAAA,OAAO,WAAPA,OAAOA,CAACjF,EAAE,EAAEkF,UAAU,EAAE;MACpB,IAAI,CAACf,IAAG,GAAInE,EAAE;AACdkF,MAAAA,UAAW,IAAGA,UAAU,CAAClF,EAAE,CAAC;KAC/B;AACDmF,IAAAA,kBAAkB,EAAlBA,SAAAA,kBAAkBA,CAACnF,EAAE,EAAE;MACnB,IAAI,CAACtK,eAAc,GAAIsK,EAAE;KAC5B;AACDoF,IAAAA,2BAA2B,EAA3BA,SAAAA,2BAA2BA,CAAC7N,KAAK,EAAE;AAAA,MAAA,IAAA8N,OAAA,GAAA,IAAA;AAC/B,MAAA,IAAMnC,qBAAqB,IAAI,CAAChE,WAAW3H,KAAI,GAAI,IAAI,CAACf,cAAc,CAACC,SAAS,CAAA,GAAI,IAAI,CAACD,cAAc,CAACuC,KAAK,CAACxB,KAAI,GAAI,CAAC,CAAC,CAACuL,SAAS,CAAC,UAACnL,MAAM,EAAA;AAAA,QAAA,OAAK0N,OAAI,CAACjD,qBAAqB,CAACzK,MAAM,CAAC;OAAE,CAAA,GAAE,EAAE;AAEvL,MAAA,OAAOuL,kBAAiB,GAAI,EAAG,GAAEA,kBAAiB,GAAI3L,KAAI,GAAI,CAAA,GAAI,EAAE;KACvE;AACD+N,IAAAA,2BAA2B,EAA3BA,SAAAA,2BAA2BA,CAAC/N,KAAK,EAAE;AAAA,MAAA,IAAAgO,OAAA,GAAA,IAAA;MAC/B,IAAMrC,kBAAiB,GAAI,IAAI,CAAChE,OAAQ,IAAG3H,KAAM,GAAE,CAAE,GAAEyL,aAAa,CAAC,IAAI,CAACxM,cAAc,CAACuC,KAAK,CAAC,CAAC,EAAExB,KAAK,CAAC,EAAE,UAACI,MAAM,EAAA;AAAA,QAAA,OAAK4N,OAAI,CAACnD,qBAAqB,CAACzK,MAAM,CAAC;OAAE,CAAA,GAAE,EAAE;MAE9J,OAAOuL,kBAAiB,GAAI,EAAC,GAAIA,kBAAiB,GAAI,EAAE;KAC3D;AACDtF,IAAAA,8BAA8B,EAA9BA,SAAAA,8BAA8BA,CAACrG,KAAK,EAAwB;AAAA,MAAA,IAAtBiO,YAAW,GAAAtI,SAAA,CAAAzG,MAAA,GAAA,CAAA,IAAAyG,SAAA,CAAA,CAAA,CAAA,KAAAhJ,SAAA,GAAAgJ,SAAA,CAAA,CAAA,CAAA,GAAI,KAAK;MACtD,IAAIgG,kBAAiB,GAAI,EAAE;MAE3B,IAAI,IAAI,CAAChE,OAAO,EAAE;AACd,QAAA,IAAIsG,YAAY,EAAE;AACdtC,UAAAA,kBAAmB,GAAE,IAAI,CAACoC,2BAA2B,CAAC/N,KAAK,CAAC;AAC5D2L,UAAAA,qBAAqBA,kBAAiB,KAAM,EAAG,GAAE,IAAI,CAACkC,2BAA2B,CAAC7N,KAAK,CAAA,GAAI2L,kBAAkB;AACjH,SAAE,MAAK;AACHA,UAAAA,kBAAmB,GAAE,IAAI,CAACkC,2BAA2B,CAAC7N,KAAK,CAAC;AAC5D2L,UAAAA,qBAAqBA,kBAAiB,KAAM,EAAG,GAAE,IAAI,CAACoC,2BAA2B,CAAC/N,KAAK,CAAA,GAAI2L,kBAAkB;AACjH;AACJ;AAEA,MAAA,OAAOA,kBAAiB,GAAI,KAAKA,kBAAiB,GAAI3L,KAAK;AAC/D;GACH;AACDkO,EAAAA,QAAQ,EAAE;IACNjP,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,OAAO,IAAI,CAAC1E,gBAAe,GAAI,IAAI,CAAC0S,WAAW,CAAC,IAAI,CAAC/S,WAAW,CAAA,GAAI,IAAI,CAACA,eAAe,EAAE;KAC7F;IACDiU,UAAU,EAAA,SAAVA,UAAUA,GAAG;MACT,IAAI,IAAI,CAACxG,OAAO,EAAE;AACd,QAAA,IAAIyG,SAAA,CAAO,IAAI,CAACtI,OAAQ,CAAA,KAAI,QAAQ,EAAE;UAClC,IAAMuI,KAAI,GAAI,IAAI,CAAClO,cAAc,CAAC,IAAI,CAAC2F,OAAO,CAAC;UAE/C,OAAOuI,SAAS,IAAK,GAAEA,KAAM,GAAE,IAAI,CAACvI,OAAO;AAC/C,SAAE,MAAK;UACH,OAAO,IAAI,CAACA,OAAO;AACvB;AACJ,OAAE,MAAK;AACH,QAAA,OAAO,EAAE;AACb;KACH;AACD;IACAwI,iBAAiB,EAAA,SAAjBA,iBAAiBA,GAAG;MAChB,OAAO,IAAI,CAAC3G,OAAO;KACtB;IACDuD,WAAW,EAAA,SAAXA,WAAWA,GAAG;AACV;MACA,OAAO,IAAI,CAACjQ,OAAO;KACtB;IACDsT,uBAAuB,EAAA,SAAvBA,uBAAuBA,GAAG;AACtB,MAAA,OAAOpG,UAAU,CAAC,IAAI,CAAClJ,cAAc,CAAE,IAAG,IAAI,CAACL,cAAe,GAAE,IAAI,CAAC4P,iBAAiB,CAACC,UAAU,CAAC,KAAK,EAAE,IAAI,CAACxP,cAAc,CAACC,MAAM,CAAE,GAAE,IAAI,CAACwP,sBAAsB;KACrK;IACDF,iBAAiB,EAAA,SAAjBA,iBAAiBA,GAAG;AAChB,MAAA,OAAO,IAAI,CAAC5R,iBAAiB,IAAI,CAAC+L,SAAS,CAACC,MAAM,CAAC+F,MAAM,CAAC/R,aAAY,IAAK,EAAE;KAChF;IACD8R,sBAAsB,EAAA,SAAtBA,sBAAsBA,GAAG;AACrB,MAAA,OAAO,IAAI,CAAC3R,kBAAmB,IAAG,IAAI,CAAC4L,SAAS,CAACC,MAAM,CAAC+F,MAAM,CAAC5R,sBAAsB,EAAE;KAC1F;IACD6R,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;AACnB,MAAA,OAAO,IAAI,CAAC/R,oBAAoB,IAAI,CAAC8L,SAAS,CAACC,MAAM,CAAC+F,MAAM,CAAC9R,gBAAe,IAAK,EAAE;KACtF;IACDgS,yBAAyB,EAAA,SAAzBA,yBAAyBA,GAAG;AACxB,MAAA,OAAO,IAAI,CAAC/R,qBAAoB,IAAK,IAAI,CAAC6L,SAAS,CAACC,MAAM,CAAC+F,MAAM,CAAC7R,yBAAyB,EAAE;KAChG;IACDgS,mBAAmB,EAAA,SAAnBA,mBAAmBA,GAAG;MAClB,OAAO,IAAI,CAACnH,OAAQ,GAAE,IAAI,CAACiH,oBAAoB,CAACH,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC3T,QAAO,GAAI,IAAI,CAACgL,OAAO,CAAC5G,MAAK,GAAI,GAAG,CAAA,GAAI,IAAI,CAAC2P,yBAAyB;KAChJ;IACDE,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,OAAO,IAAI,CAACpG,SAAS,CAACC,MAAM,CAAC+F,MAAM,CAACK,IAAK,GAAE,IAAI,CAACrG,SAAS,CAACC,MAAM,CAAC+F,MAAM,CAACK,IAAI,CAACC,YAAYtS,SAAS;KACrG;IACD8P,eAAe,EAAA,SAAfA,eAAeA,GAAG;AACd,MAAA,OAAO,IAAI,CAAC/N,kBAAiB,KAAM,EAAC,MAAAkH,MAAA,CAAO,IAAI,CAAC4G,GAAG,OAAA5G,MAAA,CAAI,IAAI,CAAClH,kBAAkB,IAAK,IAAI;KAC1F;IACDwQ,uBAAuB,EAAA,SAAvBA,uBAAuBA,GAAG;AACtB,MAAA,OAAO,IAAI,CAACvQ,0BAAyB,KAAM,eAAQ,IAAI,CAAC6N,GAAG,uBAAA5G,MAAA,CAAoB,IAAI,CAACjH,0BAA0B,IAAK,IAAI;KAC1H;IACDwQ,WAAW,EAAA,SAAXA,WAAWA,GAAG;AAAA,MAAA,IAAAC,OAAA,GAAA,IAAA;AACV,MAAA,OAAO,IAAI,CAACnQ,cAAc,CAACwC,MAAM,CAAC,UAACrB,MAAM,EAAA;AAAA,QAAA,OAAK,CAACgP,OAAI,CAACnO,aAAa,CAACb,MAAM,CAAC;AAAA,OAAA,CAAC,CAAClB,MAAM;KACpF;IACDgB,uBAAuB,EAAA,SAAvBA,uBAAuBA,GAAG;MACtB,OAAO,CAAC,IAAI,CAAC5D,sBAAsB;KACtC;IACD+S,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,MAAA,OAAO,IAAI,CAAC7C,GAAE,GAAI,QAAQ;KAC7B;IACD8C,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,OAAOC,EAAE,CAAC;QACNC,KAAK,EAAE,IAAI,CAACC;AAChB,OAAC,CAAC;KACL;IACDC,YAAY,EAAA,SAAZA,YAAYA,GAAG;AACX,MAAA,OAAOH,EAAE,CAAAI,iBAAA,CACJ,EAAA,EAAA,SAAU,GAAE,IAAI,CAACtU,QAAQ,EAAG,SAAU,GAAE,IAAI,CAACA,QAAO,CACxD,CAAC;KACL;IACDuU,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;MACjB,OAAOL,EAAE,CAAAI,iBAAA,CAAA;QACLE,OAAO,EAAE,IAAI,CAACC,QAAQ;QACtB/O,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBa,KAAK,EAAE,IAAI,CAACnD,OAAO;QACnB+Q,KAAK,EAAE,IAAI,CAACC,MAAM;AAClBM,QAAAA,MAAM,EAAE,IAAI,CAACC,QAAS,KAAI,QAAQ;QAClCC,KAAK,EAAE,CAAC,IAAI,CAACtI;OACZ,EAAA,IAAI,CAACuI,IAAI,EAAG,IAAI,CAACA,IAAG,CACxB,CAAC;AACN;GACH;AACDC,EAAAA,UAAU,EAAE;AACRC,IAAAA,SAAS,EAATA,SAAS;AACTC,IAAAA,eAAe,EAAfA,eAAe;AACfC,IAAAA,MAAM,EAANA,MAAM;AACNC,IAAAA,eAAe,EAAfA,eAAe;AACfC,IAAAA,WAAW,EAAXA,WAAW;AACXC,IAAAA,IAAG,EAAHA;GACH;AACDC,EAAAA,UAAU,EAAE;AACRC,IAAAA,MAAM,EAAEC;AACZ;AACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;ECrmCG,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CA0MK,OA1MLC,UA0MK,CAAA;AA1MAC,IAAAA,GAAG,EAAC,WAAU;AAAG,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;AAAW7T,IAAAA,KAAK,EAAE4T,IAAE,CAAAE,EAAA,CAAA,MAAA,CAAA;IAAWC,OAAK;aAAEC,QAAgB,CAAAhM,gBAAA,IAAAgM,QAAA,CAAAhM,gBAAA,CAAAiM,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;AAAA,KAAA,CAAA;IAAG,QAAM,EAAE0L,QAAc,CAAA/B;KAAU2B,IAAI,CAAAM,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAE7GN,IAAQ,CAAAnW,QAAA,iBADnB0W,WAiCC,CAAAC,oBAAA,EAAA;;AA/BGT,IAAAA,GAAG,EAAC,YAAW;IACdzE,EAAE,EAAE0E,IAAO,CAAAxV,OAAA;AACZtB,IAAAA,IAAI,EAAC,MAAK;IACTJ,IAAI,EAAEkX,IAAS,CAAAS,SAAA;AACf,IAAA,OAAA,EAAKC,cAAA,CAAA,CAAGV,IAAE,CAAAC,EAAA,CAAA,aAAA,CAAA,EAAiBD,IAAU,CAAAtV,UAAA,CAAA,CAAA;AACrC0B,IAAAA,KAAK,iBAAE4T,IAAU,CAAAvV,UAAA,CAAA;IACjBkW,YAAY,EAAEP,QAAU,CAAAlD,UAAA;IACxBnT,WAAW,EAAEiW,IAAW,CAAAjW,WAAA;IACxBiC,QAAQ,EAAA,CAAGgU,IAAO,CAAAlQ,QAAA,GAAIkQ,IAAS,CAAAhU,QAAA,GAAA,EAAA;IAC/BuS,KAAK,EAAEyB,IAAM,CAAAxB,MAAA;IACb1O,QAAQ,EAAEkQ,IAAQ,CAAAlQ,QAAA;IAClBmP,IAAI,EAAEe,IAAI,CAAAf,IAAA;IACVL,OAAO,EAAEoB,IAAO,CAAApB,OAAA;IAChBgC,OAAO,EAAEZ,IAAO,CAAAY,OAAA;AACjBC,IAAAA,YAAY,EAAC,KAAI;AACjBC,IAAAA,IAAI,EAAC,UAAS;IACb,YAAU,EAAEd,IAAS,CAAA9T,SAAA;IACrB,iBAAe,EAAE8T,IAAc,CAAA7T,cAAA;AAChC,IAAA,eAAa,EAAC,SAAQ;AACtB,IAAA,mBAAiB,EAAC,MAAK;IACtB,eAAa,EAAE4U,KAAc,CAAApT,cAAA;IAC7B,eAAa,EAAEyS,QAAO,CAAAhC,OAAA;IACtB,uBAAqB,EAAE2C,KAAA,CAAAvT,OAAM,GAAI4S,QAAA,CAAA5E,eAAgB,GAAE9P,SAAS;IAC5DyF,OAAK,EAAEiP,QAAO,CAAAjP,OAAA;IACdM,MAAI,EAAE2O,QAAM,CAAA3O,MAAA;IACZuP,SAAO,EAAEZ,QAAS,CAAAtO,SAAA;IAClBiB,OAAK,EAAEqN,QAAO,CAAArN,OAAA;IACdK,QAAM,EAAEgN,QAAQ,CAAAhN,QAAA;IAChB6N,QAAQ,EAAEjB,IAAQ,CAAAiB,QAAA;IAClB,qBAAmB,EAAEjB,IAAQ,CAAAtW,QAAA;AAC7BwX,IAAAA,EAAE,EAAElB,IAAG,CAAAtQ,GAAA,CAAA,aAAA;6WAGFsQ,IAAQ,CAAAnW,QAAA,IADlB+V,SAAA,EAAA,EAAAC,kBAAA,CAyEI,MAzEJC,UAyEI,CAAA;;AAvEAC,IAAAA,GAAG,EAAC,gBAAe;AAClB,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,eAAA,CAAA;AACVjU,IAAAA,QAAQ,EAAC,IAAG;AACZ8U,IAAAA,IAAI,EAAC,SAAQ;AACb,IAAA,kBAAgB,EAAC,YAAW;IAC3B,uBAAqB,EAAEC,KAAA,CAAAvT,OAAQ,GAAE4S,QAAA,CAAAnC,uBAAsB,GAAIvS,SAAS;IACpEyF,OAAK;aAAEiP,QAAwB,CAAAtM,wBAAA,IAAAsM,QAAA,CAAAtM,wBAAA,CAAAuM,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;AAAA,KAAA,CAAA;IAC/BjD,MAAI;aAAE2O,QAAuB,CAAArM,uBAAA,IAAAqM,QAAA,CAAArM,uBAAA,CAAAsM,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;AAAA,KAAA,CAAA;IAC7BsM,SAAO;aAAEZ,QAA0B,CAAApM,0BAAA,IAAAoM,QAAA,CAAApM,0BAAA,CAAAqM,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;AAAA,KAAA,CAAA;IACnC,qBAAmB,EAAEsL,IAAQ,CAAAtW,QAAA;IAC7B,QAAM,EAAE0W,QAAkB,CAAAzB;KACnBqB,IAAG,CAAAtQ,GAAA,CAAA,eAAA,CAAA,CAAA,EAAA,EAEXkQ,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CA6BIsB,QA5BsB,EAAA,IAAA,EAAAC,UAAA,CAAApB,IAAA,CAAAnL,OAAO,EAArB,UAAA1F,MAAM,EAAEgM,CAAC,EAAA;IADrB,OAAAyE,SAAA,EAAA,EAAAC,kBAAA,CA6BI,MA7BJC,UA6BI,CAAA;AA3BCrQ,MAAAA,GAAG,EAAAkF,EAAAA,CAAAA,MAAA,CAAKwG,CAAC,EAAAxG,GAAAA,CAAAA,CAAAA,MAAA,CAAIyL,QAAA,CAAAlR,cAAc,CAACC,MAAM,CAAA,CAAA;AAClCmM,MAAAA,EAAE,EAAE0E,IAAI,CAAAzE,GAAA,GAAA,mBAAA,GAAwBJ,CAAC;AACjC,MAAA,OAAA,EAAO6E,IAAE,CAAAC,EAAA,CAAA,UAAA,EAAA;AAAe9E,QAAAA,CAAE,EAAFA;AAAE,OAAA,CAAA;AAC3B2F,MAAAA,IAAI,EAAC,QAAO;AACX,MAAA,YAAU,EAAEV,QAAc,CAAAlR,cAAA,CAACC,MAAM,CAAA;AACjC,MAAA,eAAa,EAAE,IAAI;AACnB,MAAA,cAAY,EAAE6Q,IAAO,CAAAnL,OAAA,CAAC5G,MAAM;MAC5B,eAAa,EAAEkN,CAAA,GAAA,CAAA;;OACR6E,IAAG,CAAAtQ,GAAA,CAAA,UAAA,CAAA,CAAA,EAAA,CAEX2R,UAAA,CAgBMrB,qBAhBNF,UAgBM,CAAA;AAhBa,MAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,QAAA,CAAA;AAAa1O,MAAAA,KAAK,EAAEpC,MAAM;AAAGJ,MAAAA,KAAK,EAAEoM,CAAC;AAAGmG,MAAAA,cAAc,WAAdA,cAAcA,CAAGlQ,KAAK,EAAA;AAAA,QAAA,OAAKgP,qBAAY,CAAChP,KAAK,EAAE+J,CAAC,CAAA;AAAA,OAAA;;KAAW,EAAA6E,IAAA,CAAAtQ,GAAG,aAAlI,YAAA;AAAA,MAAA,OAgBM,CAdF6R,WAaM,CAAAC,eAAA,EAAA;QAZD,wBAAOxB,IAAE,CAAAC,EAAA,CAAA,QAAA,CAAA,CAAA;AACT7C,QAAAA,KAAK,EAAEgD,QAAc,CAAAlR,cAAA,CAACC,MAAM,CAAA;AAC5BsS,QAAAA,UAAU,EAAEzB,IAAO,CAAA5U,QAAA,IAAK4U,IAAe,CAAA7U,eAAA;AACxCuW,QAAAA,SAAQ,EAAR,EAAQ;QACPT,QAAQ,EAAEjB,IAAQ,CAAAiB,QAAA;AAClBU,QAAAA,QAAM,EAAE,SAARA,QAAMA,CAAEC,MAAA,EAAA;AAAA,UAAA,OAAAxB,QAAA,CAAA9I,YAAY,CAACsK,MAAM,EAAEzG,CAAC,CAAA;SAAA;AAC9B,QAAA,gBAAc,EAAE4F,KAA2B,CAAArT,0BAAA,KAAIyN,CAAC;AAChD+F,QAAAA,EAAE,EAAElB,IAAG,CAAAtQ,GAAA,CAAA,QAAA;;QAEGmS,UAAU,UACjB,YAAA;AAAA,UAAA,OAAuJ,CAAvJR,UAAuJ,CAAArB,IAAA,CAAA8B,MAAA,EAA1I9B,IAAM,CAAA8B,MAAA,CAACC,QAAO,GAAA,UAAA,GAAA,iBAAA,EAAA;YAAqC,wBAAO/B,IAAE,CAAAC,EAAA,CAAA,UAAA,CAAA,CAAA;AAAelR,YAAAA,KAAK,EAAEoM,CAAC;AAAGmG,YAAAA,cAAc,WAAdA,cAAcA,CAAGlQ,KAAK,EAAA;AAAA,cAAA,OAAKgP,qBAAY,CAAChP,KAAK,EAAE+J,CAAC,CAAA;AAAA;;;;;;aAKnK6G,kBAAA,CA2BI,MA3BJlC,UA2BI,CAAA;AA3BC,IAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,WAAA,CAAA;AAAea,IAAAA,IAAI,EAAC;KAAiBd,IAAG,CAAAtQ,GAAA,CAAA,WAAA,CAAA,CAAA,EAAA,CAClDsS,kBAAA,CAyBC,SAzBDlC,UAyBC,CAAA;AAxBGC,IAAAA,GAAG,EAAC,YAAW;IACdzE,EAAE,EAAE0E,IAAO,CAAAxV,OAAA;AACZtB,IAAAA,IAAI,EAAC,MAAK;IACTkD,KAAK,EAAE4T,IAAU,CAAAvV,UAAA;IACjB,OAAOuV,EAAAA,IAAU,CAAAtV,UAAA;IACjBX,WAAW,EAAEiW,IAAW,CAAAjW,WAAA;IACxBiC,QAAQ,EAAA,CAAGgU,IAAO,CAAAlQ,QAAA,GAAIkQ,IAAS,CAAAhU,QAAA,GAAA,EAAA;IAC/B8D,QAAQ,EAAEkQ,IAAQ,CAAAlQ,QAAA;AACnB+Q,IAAAA,YAAY,EAAC,KAAI;AACjBC,IAAAA,IAAI,EAAC,UAAS;IACb,YAAU,EAAEd,IAAS,CAAA9T,SAAA;IACrB,iBAAe,EAAE8T,IAAc,CAAA7T,cAAA;AAChC,IAAA,eAAa,EAAC,SAAQ;AACtB,IAAA,mBAAiB,EAAC,MAAK;IACtB,eAAa,EAAE4U,KAAc,CAAApT,cAAA;AAC7B,IAAA,eAAa,EAAEqS,IAAI,CAAAzE,GAAA,GAAA,OAAA;IACnB,uBAAqB,EAAEwF,KAAA,CAAAvT,OAAM,GAAI4S,QAAA,CAAA5E,eAAgB,GAAE9P,SAAS;AAC5D,IAAA,cAAY,EAAEsU,IAAM,CAAApB,OAAA,IAAKlT,SAAS;IAClCyF,OAAK;aAAEiP,QAAO,CAAAjP,OAAA,IAAAiP,QAAA,CAAAjP,OAAA,CAAAkP,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;AAAA,KAAA,CAAA;IACdjD,MAAI;aAAE2O,QAAM,CAAA3O,MAAA,IAAA2O,QAAA,CAAA3O,MAAA,CAAA4O,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;AAAA,KAAA,CAAA;IACZsM,SAAO;aAAEZ,QAAS,CAAAtO,SAAA,IAAAsO,QAAA,CAAAtO,SAAA,CAAAuO,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;AAAA,KAAA,CAAA;IAClB3B,OAAK;aAAEqN,QAAO,CAAArN,OAAA,IAAAqN,QAAA,CAAArN,OAAA,CAAAsN,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;AAAA,KAAA,CAAA;IACdtB,QAAM;aAAEgN,QAAQ,CAAAhN,QAAA,IAAAgN,QAAA,CAAAhN,QAAA,CAAAiN,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;KAAA;KACTsL,IAAG,CAAAtQ,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAAuS,UAAA,CAAA,0DAIXlB,KAAA,CAAAnT,SAAQ,IAAKoS,IAAO,CAAAlW,OAAA,GAAhCuX,UAGM,CAAArB,IAAA,CAAA8B,MAAA,EAHyD9B,IAAM,CAAA8B,MAAA,CAAC7W,MAAK,GAAA,QAAA,GAAA,aAAA,EAAA;;AAAxC,IAAA,OAAA,iBAAO+U,IAAE,CAAAC,EAAA,CAAA,QAAA,CAAA;KAA5C,YAAA;AAAA,IAAA,OAGM,CAFOD,IAAA,CAAA/U,MAAO,IAAG+U,IAAW,CAAA9U,WAAA,IAA9B0U,SAAA,EAAA,EAAAC,kBAAA,CAAmK,KAAnKC,UAAmK,CAAA;;AAAlI,MAAA,OAAA,EAAmB,CAAA,SAAA,EAAAE,IAAA,CAAAC,EAAE,CAAY,QAAA,CAAA,EAAAD,IAAA,CAAA/U,MAAM,EAAE+U,IAAW,CAAA9U,WAAA,CAAA;AAAG,MAAA,aAAW,EAAC,MAAO;MAAC,qBAAmB,EAAE8U,IAAQ,CAAAtW;OAAUsW,IAAG,CAAAtQ,GAAA,CAAA,QAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAAwS,UAAA,CAAA,KACtJtC,SAAA,EAAA,EAAAW,WAAA,CAA0H4B,wBAA1HrC,UAA0H,CAAA;;AAArG,MAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,QAAA,CAAA;AAAYmC,MAAAA,MAAA;AAAK,MAAA,aAAW,EAAC,MAAO;MAAC,qBAAmB,EAAEpC,IAAQ,CAAAtW;OAAUsW,IAAG,CAAAtQ,GAAA,CAAA,QAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,EAAA,qBAAA,CAAA,CAAA,CAAA;qCAEjH2R,UAiBM,CAAArB,IAAA,CAAA8B,MAAA,EAjBO9B,IAAM,CAAA8B,MAAA,CAACpY,QAAS,GAAA,UAAA,GAAA,gBAAA,EAAA;AAAkC2Y,IAAAA,cAAc,EAAG,SAAjBA,cAAcA,CAAGjR,KAAK,EAAA;AAAA,MAAA,OAAKgP,QAAA,CAAA7L,eAAe,CAACnD,KAAK,CAAA;AAAA;KAA/G,YAAA;AAAA,IAAA,OAiBM,CAfQ4O,IAAQ,CAAAtW,QAAA,IADlBkW,SAAA,EAAA,EAAAC,kBAAA,CAeQ,UAfRC,UAeQ,CAAA;;AAbJC,MAAAA,GAAG,EAAC,gBAAe;AACnB7W,MAAAA,IAAI,EAAC,QAAO;MACX,OAAK,EAAA,CAAG8W,IAAE,CAAAC,EAAA,CAAA,UAAA,CAAA,EAAcD,IAAa,CAAAhV,aAAA,CAAA;MACrC8E,QAAQ,EAAEkQ,IAAQ,CAAAlQ,QAAA;AACnB,MAAA,eAAa,EAAC,SAAQ;MACrB,eAAa,EAAEiR,KAAc,CAAApT,cAAA;MAC7B,eAAa,EAAEyS,QAAO,CAAAhC,OAAA;MACtB+B,OAAK;eAAEC,QAAe,CAAA7L,eAAA,IAAA6L,QAAA,CAAA7L,eAAA,CAAA8L,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;OAAA;OACfsL,IAAG,CAAAtQ,GAAA,CAAA,UAAA,CAAA,CAAA,EAAA,CAEX2R,UAEM,CAAArB,IAAA,CAAA8B,MAAA,EAAA,cAAA,EAAA;AAFqB,MAAA,OAAA,iBAAO9B,IAAY,CAAAjV,YAAA;OAA9C,YAAA;AAAA,MAAA,OAEM,eADFwV,WAA+G,CAAA+B,uBAAA,CAA/FtC,iBAAa,GAA7B,MAAA,GAAA,iBAAA,CAAA,EAAAF,UAAA,CAA+G;AAAnD,QAAA,OAAA,EAAOE,IAAY,CAAAjV;AAAA,OAAA,EAAUiV,IAAG,CAAAtQ,GAAA,CAAA,cAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA;;MAI5FsQ,IAAS,CAAA/T,SAAA,IAArB2T,SAAA,EAAA,EAAAC,kBAAA,CAEM,QAFNC,UAEM,CAAA;;AAFiBgB,IAAAA,IAAI,EAAC,QAAS;AAAA,IAAA,WAAS,EAAC,QAAS;IAAA,OAAM,EAAA;AAA8B,GAAA,EAAAd,IAAA,CAAAtQ,GAAG,CAAyB,oBAAA,CAAA,EAAA;AAAA,IAAA,0BAAwB,EAAE;sBAC3I0Q,QAAsB,CAAA9C,uBAAA,CAAA,EAAA,EAAA,CAAA,kCAE7BiE,WAmEQ,CAAAgB,iBAAA,EAAA;IAnECnY,QAAQ,EAAE4V,IAAQ,CAAA5V;AAAA,GAAA,EAAA;uBACvB,YAAA;AAAA,MAAA,OAiEY,CAjEZmX,WAAA,CAiEYiB,YAjEZ1C,UAiEY,CAAA;AAjEAhX,QAAAA,IAAI,EAAC,qBAAoB;QAAG2Z,OAAK,EAAErC,QAAc,CAAA7I,cAAA;QAAGmL,YAAW,EAAEtC,QAAmB,CAAAlI,mBAAA;QAAGyK,OAAK,EAAEvC,QAAc,CAAA9H,cAAA;QAAGsK,YAAW,EAAExC,QAAmB,CAAA5H;SAAUwH,IAAG,CAAAtQ,GAAA,CAAA,YAAA,CAAA,CAAA,EAAA;2BACpK,YAAA;AAAA,UAAA,OA+DK,CA9DKqR,KAAc,CAAApT,cAAA,IADxBiS,SAAA,EAAA,EAAAC,kBAAA,CA+DK,OA/DLC,UA+DK,CAAA;;YA7DAC,GAAG,EAAEK,QAAU,CAAA5D,UAAA;YACflB,EAAE,EAAE8E,QAAO,CAAAhC,OAAA;AACX,YAAA,OAAA,EAAQ,CAAA4B,IAAA,CAAAC,EAAE,CAAa,SAAA,CAAA,EAAAD,IAAA,CAAApV,UAAU,EAAEoV,IAAY,CAAAlV,YAAA,CAAA;AAC/CsB,YAAAA,KAAK,EAAAyW,aAAA,CAAAA,aAAA,CAAO7C,EAAAA,EAAAA,IAAU,CAAArV,UAAA,CAAKqV,EAAAA;YAC3BG,OAAK;qBAAEC,QAAc,CAAAxK,cAAA,IAAAwK,QAAA,CAAAxK,cAAA,CAAAyK,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;AAAA,aAAA,CAAA;YACrBsM,SAAO;qBAAEZ,QAAgB,CAAArK,gBAAA,IAAAqK,QAAA,CAAArK,gBAAA,CAAAsK,KAAA,CAAAD,QAAA,EAAA1L,SAAA,CAAA;AAAA,aAAA,CAAA;YACzB,QAAM,EAAE0L,QAAY,CAAA3B;aACbuB,IAAG,CAAAtQ,GAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAEX2R,UAAyE,CAAArB,IAAA,CAAA8B,MAAA,EAAA,QAAA,EAAA;YAApDvQ,KAAK,EAAEyO,IAAO,CAAAnL,OAAA;YAAG5L,WAAW,EAAEmX,QAAc,CAAApS;cACjEgU,kBAAA,CA8CK,OA9CLlC,UA8CK,CAAA;AA9CC,YAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,eAAA,CAAA;AAAoB7T,YAAAA,KAAK,EAAA;cAAA,YAAA,EAAkBgU,QAAsB,CAAAnR,uBAAA,GAAI+Q,IAAa,CAAAxW,YAAA,GAAA;AAAA;aAAgBwW,IAAG,CAAAtQ,GAAA,CAAA,eAAA,CAAA,CAAA,EAAA,CAChH6R,WAAA,CA4CiBuB,4BA5CjBhD,UA4CiB,CAAA;YA5CCC,GAAG,EAAEK,QAAA,CAAAzD;aAA4BqD,IAAsB,CAAA3U,sBAAA,EAAA;AAAGe,YAAAA,KAAK;sBAAY4T,IAAa,CAAAxW;aAAA;YAAIuZ,KAAK,EAAE3C,QAAc,CAAApS,cAAA;YAAGhC,QAAQ,EAAE,EAAE;YAAG8D,QAAQ,EAAEsQ,QAAuB,CAAAnR,uBAAA;AAAGiS,YAAAA,EAAE,EAAElB,IAAG,CAAAtQ,GAAA,CAAA,iBAAA;;AAC3KsT,YAAAA,OAAO,EACpBC,OAAA,CAAA,UAAAC,IAAA,EAAA;AAAA,cAAA,IADwBC,UAAU,GAAAD,IAAA,CAAVC,UAAU;gBAAEzG,UAAU,GAAAwG,IAAA,CAAVxG,UAAU;gBAAEqG,KAAK,GAAAG,IAAA,CAALH,KAAK;gBAAEK,cAAc,GAAAF,IAAA,CAAdE,cAAc;gBAAEC,YAAY,GAAAH,IAAA,CAAZG,YAAY;gBAAEC,QAAO,GAAAJ,IAAA,CAAPI,QAAO;AAAA,cAAA,OAAA,CAC5FtB,kBAAA,CAqCI,MArCJlC,UAqCI,CAAA;AArCCC,gBAAAA,GAAG,WAAHA,GAAGA,CAAGvI,EAAE,EAAA;AAAA,kBAAA,OAAK4I,gBAAO,CAAC5I,EAAE,EAAEkF,UAAU,CAAA;AAAA,iBAAA;AAAIpB,gBAAAA,EAAE,EAAE0E,IAAE,CAAAzE,GAAA,GAAA,OAAA;gBAAc,OAAK,EAAA,CAAGyE,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA,EAAUkD,UAAU,CAAA;AAAI/W,gBAAAA,KAAK,EAAEiX,YAAY;AAAEvC,gBAAAA,IAAI,EAAC;gBAAW,YAAU,EAAEV,QAAa,CAAAtC;iBAAUkC,IAAG,CAAAtQ,GAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EACzKkQ,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAgCUsB,QAhCsB,EAAA,IAAA,EAAAC,UAAA,CAAA2B,KAAK,EAAnB,UAAA5T,MAAM,EAAEgM,CAAC,EAAA;;AAAkB1L,kBAAAA,GAAA,EAAA2Q,QAAA,CAAA9Q,kBAAkB,CAACH,MAAM,EAAEiR,uBAAc,CAACjF,CAAC,EAAEiI,cAAc,CAAA;oBAE1FhD,QAAA,CAAApQ,aAAa,CAACb,MAAM,CAAA,IAD9ByQ,SAAA,EAAA,EAAAC,kBAAA,CASI,MATJC,UASI,CAAA;;AAPCxE,kBAAAA,EAAE,EAAE0E,iBAAYI,uBAAc,CAACjF,CAAC,EAAEiI,cAAc,CAAA;AAChDhX,kBAAAA,KAAK,EAAY;AAAAmX,oBAAAA,MAAA,EAAAD,QAAO,GAAIA,QAAO,UAAW5X;mBAAQ;AACtD,kBAAA,OAAA,EAAOsU,IAAE,CAAAC,EAAA,CAAA,aAAA,CAAA;AACVa,kBAAAA,IAAI,EAAC,QAAO;;mBACJd,IAAG,CAAAtQ,GAAA,CAAA,aAAA,CAAA,CAAA,EAAA,CAEX2R,UAAoJ,CAAArB,IAAA,CAAA8B,MAAA,EAAA,aAAA,EAAA;kBAA1H3S,MAAM,EAAEA,MAAM,CAACc,WAAW;AAAGlB,kBAAAA,KAAK,EAAEqR,QAAA,CAAAtR,cAAc,CAACqM,CAAC,EAAEiI,cAAc;mBAA9F,YAAA;AAAA,kBAAA,OAAoJ,iCAAhDhD,QAAmB,CAAAjQ,mBAAA,CAAChB,MAAM,CAACc,WAAW,CAAA,CAAA,EAAA,CAAA,CAAA;wCAE9IuT,cAAA,EAAA5D,SAAA,EAAA,EAAAC,kBAAA,CAoBI,MApBJC,UAoBI,CAAA;;AAlBCxE,kBAAAA,EAAE,EAAE0E,iBAAYI,uBAAc,CAACjF,CAAC,EAAEiI,cAAc,CAAA;AAEhDhX,kBAAAA,KAAK,EAAY;AAAAmX,oBAAAA,MAAA,EAAAD,QAAO,GAAIA,QAAO,UAAW5X;mBAAQ;AACtD,kBAAA,OAAA,EAAOsU,IAAE,CAAAC,EAAA,CAAA,QAAA,EAAA;AAAa9Q,oBAAAA,MAAM,EAANA,MAAM;AAAEgM,oBAAAA,CAAC,EAADA,CAAC;AAAEiI,oBAAAA,cAAa,EAAbA;AAAa,mBAAA,CAAA;AAC/CtC,kBAAAA,IAAI,EAAC,QAAO;AACX,kBAAA,YAAU,EAAEV,QAAc,CAAAlR,cAAA,CAACC,MAAM,CAAA;AACjC,kBAAA,eAAa,EAAEiR,QAAU,CAAAvQ,UAAA,CAACV,MAAM,CAAA;AAChC,kBAAA,eAAa,EAAEiR,QAAgB,CAAArQ,gBAAA,CAACZ,MAAM,CAAA;kBACtC,cAAY,EAAEiR,QAAW,CAAAlC,WAAA;AACzB,kBAAA,eAAa,EAAEkC,QAAe,CAAA/P,eAAA,CAAC+P,uBAAc,CAACjF,CAAC,EAAEiI,cAAc,CAAA,CAAA;AAC/DjD,kBAAAA,OAAK,EAAE,SAAPA,OAAKA,CAAEyB,MAAA,EAAA;AAAA,oBAAA,OAAAxB,QAAA,CAAAzM,cAAc,CAACiO,MAAM,EAAEzS,MAAM,CAAA;mBAAA;AACpCsU,kBAAAA,WAAS,EAAA,SAATA,WAASA,CAAA7B,MAAA,EAAA;AAAA,oBAAA,OAAExB,0BAAiB,CAACwB,MAAM,EAAExB,QAAc,CAAAtR,cAAA,CAACqM,CAAC,EAAEiI,cAAc,CAAA,CAAA;mBAAA;AACrE,kBAAA,iBAAe,EAAEhD,QAAU,CAAAvQ,UAAA,CAACV,MAAM,CAAA;AAClC,kBAAA,gBAAc,EAAE4R,KAAiB,CAAAtT,kBAAA,KAAM2S,uBAAc,CAACjF,CAAC,EAAEiI,cAAc,CAAA;AACvE,kBAAA,iBAAe,EAAEhD,QAAgB,CAAArQ,gBAAA,CAACZ,MAAM,CAAA;;iBACjC,EAAAiR,QAAA,CAAA7Q,YAAY,CAACJ,MAAM,EAAEiU,cAAc,EAAEjI,CAAC,EAAA,QAAA,CAAA,CAAA,EAAA,CAE9CkG,UAAkH,CAAArB,IAAA,CAAA8B,MAAA,EAAA,QAAA,EAAA;AAA7F3S,kBAAAA,MAAM,EAAEA,MAAM;AAAGJ,kBAAAA,KAAK,EAAEqR,QAAA,CAAAtR,cAAc,CAACqM,CAAC,EAAEiI,cAAc;mBAA7E,YAAA;AAAA,kBAAA,OAAkH,CAA/BM,eAAA,CAAAC,eAAA,CAAAvD,QAAA,CAAAlR,cAAc,CAACC,MAAM,CAAA,CAAA,EAAA,CAAA,CAAA;;yBAGtG6Q,IAAA,CAAAjU,gBAAe,MAAOgX,KAAI,IAAMA,KAAI,IAAKA,KAAK,CAAC9U,MAAK,KAAA,CAAA,CAAA,IAA9D2R,SAAA,EAAA,EAAAC,kBAAA,CAEI,MAFJC,UAEI,CAAA;;AAFsE,gBAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,cAAA,CAAA;AAAkBa,gBAAAA,IAAI,EAAC;iBAAiBd,IAAG,CAAAtQ,GAAA,CAAA,cAAA,CAAA,CAAA,EAAA,CAC1H2R,UAAA,CAAsDrB,0BAAtD,YAAA;AAAA,gBAAA,OAAsD,iCAAhCI,QAAsB,CAAA9C,uBAAA,CAAA,EAAA,CAAA,CAAA;;;;cAIxC0C,IAAA,CAAA8B,MAAM,CAAC7W,MAAM;kBAAS,QAAM;AACxC+D,YAAAA,EAAA,EAAAiU,OAAA,CAAA,UAAAW,KAAA,EAAA;AAAA,cAAA,IAD4C3H,OAAQ,GAAA2H,KAAA,CAAR3H,OAAQ;cAAA,OAAA,CACpDoF,UAA6C,CAAArB,IAAA,CAAA8B,MAAA,EAAA,QAAA,EAAA;AAAxB7F,gBAAAA,OAAO,EAAEA;AAAO,eAAA,CAAA;;;8EAIjDoF,UAAyE,CAAArB,IAAA,CAAA8B,MAAA,EAAA,QAAA,EAAA;YAApDvQ,KAAK,EAAEyO,IAAO,CAAAnL,OAAA;YAAG5L,WAAW,EAAEmX,QAAc,CAAApS;cACjEgU,kBAAA,CAEM,QAFNlC,UAEM,CAAA;AAFAgB,YAAAA,IAAI,EAAC,QAAS;AAAA,YAAA,WAAS,EAAC,QAAO;YAAE,OAAM,EAAA;AAA8B,WAAA,EAAAd,IAAA,CAAAtQ,GAAG,CAA4B,uBAAA,CAAA,EAAA;AAAA,YAAA,0BAAwB,EAAE;8BAC7H0Q;;;;;;;;;;;;;"}